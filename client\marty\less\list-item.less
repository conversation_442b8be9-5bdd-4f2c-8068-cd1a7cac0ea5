.list-item {	
  display: table;
  table-layout: fixed;
  width: 100%;
	.font1;
	
	&:hover { .list-item__right {	&.list-item__icon {	button {display: inline-block !important;} } } }
	
	a {
		text-decoration: none;
		color: @accent;
		
		&:hover, &:focus {color: @accent;}
	}
	
	.list-item__right, .list-item__img, .list-item__content {border-bottom: 1px solid @black1;}
	
  &:last-child {
	
		.list-item__row { 
	  	.list-item__right, .list-item__img, .list-item__content {border: none;}
	  }
	}
	
	.list-item__row {
		.table-row;
		
		> div {
	    .table-cell;
	    padding-top: 1em;
	    padding-bottom: 1em;
	    vertical-align: middle;
	    
	    &:first-child {padding-left: 16px;} 
	    &:last-child {padding-right: 16px;}
		}
	}
		
	.list-item__left {
		width: 72px;
	}
	
	.list-item__right {
		padding-left: 16px;
		
		&.list-item__icon {	
			width: 54px;
			
			button {
				background: transparent;
				border: 0;
				cursor: pointer;

				@media @sm-x {display: none;}
				
				.icon {
					color: @black3;
					
					&:hover, &:focus {color: @accent;}
				}
			}
		}
	}
	
	.list-item__icon {

		img {
			width: 30px;
			height: 30px;
		}		
	}
	
	.list-item__avatar {

		img {
			width: 40px;
			height: 40px;
			border-radius: 100px;
		}
	}
	
	.list-item__social {
		
		.fa {
			font-size: 1.5em;
			line-height: 40px;
			width: 40px;
			text-align: center;
			border-radius: 100px;
			color: white;
		}
		
		.fa-instagram {background-color: @instagram;}
		.fa-facebook {background-color: @facebook;}
		.fa-twitter {background-color: @twitter;}
		.fa-rss {background-color: @orange;}
	}
	
	.list-item__icon {
		color: @black6;
		
		.icon {
			color: @accent;
	    display: table-cell;
	    font-size: 16px;
	    line-height: 24px;
	    text-decoration: none;
			
			&:hover, &:focus {color: @accent;}
		}
	}
	
	.list-item__content {
		
		.list-item__title {
			font-size: @subtitle;
			color: @black8;
			font-weight: 600;
			.truncate;
		}
		
		.list-item__title--full {
			font-size: @subtitle;
			color: @black8;
		}
		
		a.list-item__title {
			color: @secondary;
			display: block;
			.truncate;
			
			&:hover, &:focus {color: @accent;}
		}
		
		
		a.list-item__title--full {
			color: @secondary;
			display: block;
			
			&:hover, &:focus {color: @accent;}
		}
		
		.list-item__username {
			font-size: @caption;
			color: @black6;
			.truncate;
		}
		
		.list-item__status {
			font-size: @caption;
			color: @black4;
			.truncate;
			
			.material-icons {
		    font-size: 13px;
		    position: relative;
		    top: 2px;
			}
			
			&.list-item__status--unconnected {}
			&.list-item__status--error {color: @orange;}
			&.list-item__status--connected {color: @green;}
		}
		
		.list-item__description {
			font-size: @caption;
	    line-height: 150%;
			color: @black4;
		}
	}
	
	.list-item__delete a {
		color: @black6;
		
		&:hover, &:focus {color: @red;}
	}
	
	.list-item__img {
		@media @x-sm {border-bottom: none;}
	}
	
	.list-item__wallpaper-preview {
		border: 2px solid white;
		box-shadow: 0 1px 3px 1px @black2;
		max-width: 100%;
		display: block;
		margin-bottom: 1em;
	}
}

.list-item__field {
	
	@media (max-width: 549px) { 
 
		.list-item__row	{	  
	
			& > div {display: block !important;}
			
			.list-item__content {
				border-bottom: none;
				padding-bottom: 4px !important;
			}
			
			.list-item__right {
				padding-top: 0 !important;
			}
		}
	}
	
	.list-item__disabled {
	
		input, select {.disabled;}
	}
	
	input {
		border: 1px solid @black3;
		width: 100%;
		font-size: @body;
    padding: 10px;
    border-radius: 2px;
	}
	
	.list-item__select {
		
		select {
	    padding: 0;
	    font-size: 1em;
	    color: @accent;
	    display: inline-block;
	    border: none;
	    background: transparent;
	    -webkit-appearance: inherit;
	    cursor: pointer;
	    
	    &:hover, &:focus {color: @accent;}
		}
	}
}

.profile-preview {
	.table-wrapper;
  border: 1px solid @black2;
  box-shadow: 0 1px 3px @black1;
  padding: 8px 12px;
  border-radius: 3px;
  
  @media (max-width: 550px) {
	  margin-top: 10px;
  }

	.profile-preview__row {
		.table-row;

		.profile-preview__cell {.table-cell;} 
		
		.profile-preview__cell--avatar {
			width: 50px;
			
			img {
				width: 36px;
				height: 36px;
				border-radius: 100px;
			}
		}
	
		.profile-preview__cell--content {
			
			& > div {.truncate;}
		
			.profile-preview__name {}
		
			.profile-preview__email {
				color: @black6;
				font-size: @caption;
			}
		}
	}
}

.list-item__color {
  width: 60px;
  height: 30px;
  float: left;
  margin-right: 5px;
  border-radius: 3px;
}

.list-item__color--primary {background: @primary;}
.list-item__color--accent {background: @accent;}

.list__divider {
	color: @black6;
	background-color: #f5f5f5;
	border-radius: 3px;
	//color: desaturate(lighten(@primary, 30%), 90%);
	//background: desaturate(lighten(@primary, 72%), 70%);
  padding: 8px 15px;
  margin-top: -1px;
  text-transform: uppercase;
  letter-spacing: .5px;
  font-weight: 700;
	font-size: @tiny;
	.truncate;
	.font2;
}

.account-card {
	border-bottom: 1px solid @black1;
	
	@media (max-width: 549px) {
		text-align: center;
    padding: 16px;
	}
	@media (min-width: 550px) {.table-wrapper;}
	
	.account-card__row {
		@media (min-width: 550px) {.table-row;}	
		
		.account-card__cell {
			@media (min-width: 550px) {
				.table-cell;
				padding: 16px;
			}
	
			.account-card__cell {}
		}
	}
	
	.account-card__avatar {
		border-radius: 100px;
		width: 100px;
		height: 100px;
		
		@media (max-width: 549px) {
			margin-bottom: 1em;
		}
	}
	
	.account-card__network {
		font-size: @title;
    line-height: 1.8;
		.truncate;
	}
	
	.account-card__username {
		font-size: @body;
		color: @black6;
		.truncate;
	}
}