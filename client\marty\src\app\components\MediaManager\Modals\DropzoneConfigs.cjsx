React = require 'react'
settings = require '../../../settings'
ReactDOMServer = require 'react-dom/server'

module.exports =
  default:
    config:
      postUrl: "#{settings.JRNL_API_URL}api/v1/redactor_user_image/"
      dropMessage: 'Drop Files Here to Upload'
      uploadButtonLabel: 'Upload Files'
    djsConfig:
      acceptedFiles: ".jpg, .jpeg, .png, .gif, .tiff"
      maxFilesize: 20
      previewsContainer: "#previews"
      dictInvalidFileType: 'Invalid type!'
      dictFileTooBig: 'File too large!'
      dictResponseError: 'Upload failed!'
      dictMaxFilesExceeded: 'Max files uploaded!'
    previewTemplate: ()->
      ReactDOMServer.renderToStaticMarkup(
        <div className="dz-preview">
          <div className="file uploading">
            <div className="status">
              <i className="loading fa fa-spin ion-load-d"></i>
              <span className="message">Uploading...</span>
            </div>
          </div>
          <div className="file uploaded dz-details">
            <div className="dz-success-mark indicator">
              <i className=" icon-check"></i>
            </div>
            <img className="dz-success-mark thumb" data-dz-thumbnail />
            <span className="file-info"><span data-dz-size></span></span>
          </div>
          <div className="file upload-error">
            <button className="indicator" data-dz-remove>
              <i className="icon-times"></i>
            </button>
            <div className="status">
              <i id="dz-preview-error-icon" className="loading fa fa-exclamation-circle"></i>
              <span className="message dz-error-message" data-dz-errormessage></span>
            </div>
          </div>
        </div>
      )  
  avatar:
    config:
      postUrl: "#{settings.JRNL_API_URL}api/v1/avatar_image/"
      dropMessage: 'Drop a File Here to Upload'
      uploadButtonLabel: 'Upload File'
    djsConfig:
      acceptedFiles: ".jpg, .jpeg, .png, .gif, .tiff"
      paramName: 'image'
      maxFilesize: 1
      maxFiles: 1
      previewsContainer: "#previews"
      dictInvalidFileType: 'Invalid type!'
      dictFileTooBig: 'File too large!'
      dictResponseError: 'Upload failed!'
      dictMaxFilesExceeded: 'Max files uploaded!'
    previewTemplate: ()->
      ReactDOMServer.renderToStaticMarkup(
        <div className="dz-preview">
          <div className="file uploading">
            <div className="status">
              <i className="loading fa fa-spin ion-load-d"></i>
              <span className="message">Uploading...</span>
            </div>
          </div>
          <div className="file uploaded dz-details">
            <div className="dz-success-mark indicator">
              <i className=" icon-check"></i>
            </div>
            <img className="avatar dz-success-mark thumb" data-dz-thumbnail />
            <span className="file-info"><span data-dz-size></span></span>
          </div>
          <div className="file upload-error">
            <button className="indicator" data-dz-remove>
              <i className="icon-times"></i>
            </button>
            <div className="status">
              <i id="dz-preview-error-icon" className="loading fa fa-exclamation-circle"></i>
              <span className="message dz-error-message" data-dz-errormessage></span>
            </div>
          </div>
        </div>
      )

  
  
