React = require 'react'
Marty = require 'marty'
moment = require 'moment'
Modal = require('react-bootstrap').Modal
DatePicker = require 'react-date-picker'
FormData = require 'react-form-data'

ModalHeader = require './Headers/ModalHeader'

DateTimeModal = React.createClass
  displayName: 'DateTimeModal'
  mixins: [FormData]
  
  getInitialState: ->
    shouldHide: true
    date: @props.entryDate
    hours: @props.entryDate?.format('h')
    minutes: @props.entryDate?.format('mm')
    meridian: @props.entryDate?.format('a')
    displayTime: @props.display_time

  componentWillReceiveProps: (nextProps) ->
    if nextProps.entryDate?
      @setState
        date: @props.entryDate
        hours: @props.entryDate?.format('h')
        minutes: @props.entryDate?.format('mm')
        meridian: @props.entryDate?.format('a')

    if nextProps.display_time?
      @setState
        displayTime: nextProps.display_time
  
  onRenderDay: (props)->
    return props

  onDateChange: (value)->
    @setState
      date: moment(value, 'YYYY-MM-DD')

  onViewChange: (view)->
    @setState
      view: view
    return

  onDisplayTime: ()->
    @setState
      displayTime: !@state.displayTime

  onHoursOptionSelected: (e)->
    @setState
      hours: e.target.value
    # console.log value.target.value

  onMinutesOptionSelected: (e)->
    @setState
      minutes: e.target.value
    # console.log value.target.value

  onMeridianOptionSelected: (e)->
    @setState
      meridian: e.target.value
    # console.log value.target.value

  showDatePicker: ()->
    @setState
      shouldHide: !@state.shouldHide

  onDone: (e)->
    e.stopPropagation()
    e.preventDefault()
    # This kind of defeats the purpose of using react-form-data
    if not @formData.hours? then @formData.hours = @state.hours
    if not @formData.minutes? then @formData.minutes = @state.minutes
    if not @formData.meridian? then @formData.meridian = @state.meridian
    if not @formData.display_time? then @formData.display_time = @props.isTimeOptional
    dateString = @state.date.format('YYYY-MM-DD') + " " + @formData.hours + ":" + @formData.minutes + " " + @formData.meridian
    if @props.onDone?
      @setState
        shouldHide: true
      dateTime = moment(dateString, 'YYYY-MM-DD H:mm a')
      @props.onDone(dateTime, @formData.display_time)
      @props.onHide()

  onHide: ()->
    @setState
      shouldHide: true
    if @props.onHide?
      @props.onHide()

  renderOptionalTime: ()->
    if @props.showOptionalTime
      return (
        <div>
          <label className="checkbox-inline">
            <input name="display_time" type="checkbox" checked={@state.displayTime} onChange={@onDisplayTime}/><span> Include Entry Time (optional)</span>
          </label>
        </div>
      )
    else
      return undefined

  renderTimePicker: ()->
    if @state.displayTime
      return (
        <div className="input-group">
          <select name="hours" className="form-control" value={@state.hours} onChange={@onHoursOptionSelected}>
            <option>1</option>
            <option>2</option>
            <option>3</option>
            <option>4</option>
            <option>5</option>
            <option>6</option>
            <option>7</option>
            <option>8</option>
            <option>9</option>
            <option>10</option>
            <option>11</option>
            <option>12</option>
          </select>
          <span>:</span>
          <select name="minutes" className="form-control" value={@state.minutes} onChange={@onMinutesOptionSelected}>
            <option>00</option>
            <option>01</option>
            <option>02</option>
            <option>03</option>
            <option>04</option>
            <option>05</option>
            <option>06</option>
            <option>07</option>
            <option>08</option>
            <option>09</option>
            <option>10</option>
            <option>11</option>
            <option>12</option>
            <option>13</option>
            <option>14</option>
            <option>15</option>
            <option>16</option>
            <option>17</option>
            <option>18</option>
            <option>19</option>
            <option>20</option>
            <option>21</option>
            <option>22</option>
            <option>23</option>
            <option>24</option>
            <option>25</option>
            <option>26</option>
            <option>27</option>
            <option>28</option>
            <option>29</option>
            <option>30</option>
            <option>31</option>
            <option>32</option>
            <option>33</option>
            <option>34</option>
            <option>35</option>
            <option>36</option>
            <option>37</option>
            <option>38</option>
            <option>39</option>
            <option>40</option>
            <option>41</option>
            <option>42</option>
            <option>43</option>
            <option>44</option>
            <option>45</option>
            <option>46</option>
            <option>47</option>
            <option>48</option>
            <option>49</option>
            <option>50</option>
            <option>51</option>
            <option>52</option>
            <option>53</option>
            <option>54</option>
            <option>55</option>
            <option>56</option>
            <option>57</option>
            <option>58</option>
            <option>59</option>
          </select>
          <select name='meridian' className="form-control" value={@state.meridian} onChange={@onMeridianOptionSelected}>
            <option value="am">am</option>
            <option value="pm">pm</option>
          </select>
        </div>
      )
    else
      return undefined

  render: ()->
    <Modal backdrop={'static'} enforceFocus={false} show={@props.show} onHide={@props.onHide} {...@props}>
      <ModalHeader header={'Date & Time'} onDone={@onDone} onHide={@onHide}/>
      <div className="modal-body" id="modal_jtd">
        <div className="modal-date">
          <form onChange={@updateFormData} onSubmit={@onDone}>
            <div className="form-group date col-sm-12">
              <div className="input-group full-width">
                <input onClick={@showDatePicker} type="text" className="date-picker form-control" value={@state.date.format("dddd, MMMM D, YYYY")} readOnly/>
                <span className="input-group-btn">
                  <button className="btn btn-default" type="button" onClick={@showDatePicker}><i className="icon icon-jump-to-date"></i></button>
                </span>
              </div>
            </div>         
            <div className="time col-sm-12">
              {
                @renderOptionalTime()
              }
              {
                @renderTimePicker()
              }
            </div>
          </form>
        </div>
        <div id="calendar-widget" className={ if @state.shouldHide then 'hidden' else ''}>
          <DatePicker navPrev={<i className="icon icon-angle-left"></i>} navNext={<i className="icon icon-angle-right"></i>} onRenderDay={@onRenderDay} view={@state.view} onViewChange={@onViewChange} onChange={@onDateChange} date={@state.date} hideFooter={true} monthFormat={'MMM'}/>   
        </div>
      </div>
    </Modal>

module.exports = DateTimeModal
