React = require 'react'
<PERSON> = require 'marty'

Modal = require('react-bootstrap').Modal

OptionsListHeader = require '../Modals/OptionsList/OptionsListHeader'

CreateModal = React.createClass
  displayName: 'Create Modal'
  mixins: [Marty.createAppMixin()]

  propType:
    createAction: React.PropTypes.func
    form: React.PropTypes.object
    title: React.PropTypes.string

  onDone: ()->
    if @.refs.form.formData?
      if @.refs.form.formData != ""
        object = @.refs.form.formData
        if @props.createAction?
          @props.createAction(object)
    @props.onHide()

  render: ()->
    <Modal show={@props.show} onHide={@props.onHide}>
      <OptionsListHeader title={@props.title} onHide={@props.onHide} onDone={@onDone} />
      <div className="modal-body">
        { 
          React.createElement @props.form, {"ref":"form"}
        }
      </div>
    </Modal>

module.exports = CreateModal