React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'

Drawer = require '../Sidebar/Drawer'
Sidebar = require '../Sidebar/SidebarLayout'
CommentWidget = require '../Sidebar/Widgets/Comments/CommentWidget'

AppHeader = require '../Header/AppHeader'
EntryDetailHeader = require './EntryDetailHeader'
JournalsAndEventsModal = require '../Modals/JournalsAndEventsModal'
EntryTile = require '../Entry/EntryTile'
InvitationModal = require '../Modals/Invitation/InvitationModal'
ContactModal = require '../Modals/Contact/ContactModal'

EntryDetail = React.createClass
  displayName: 'Entry Detail'

  getInitialState: ->
    showShareModal: false
    showContactModal: false
    showComments: @props.defaultShowComments || false
  
  openShareModal: ()->
    @setState
      showShareModal: true

  closeShareModal: ()->
    @setState
      showShareModal: false

  openComments: ()->
    @setState
      showComments: true

  closeComments: ()->
    @setState
      showComments: false

  openContactModal: (entry)->
    @setState
      showContactModal: true

  closeContactModal: ()->
    @setState
      showContactModal: false

  toggleComments: ()->
    @setState
      showComments: !@state.showComments

  toggleShareModal: ()->
    @setState
      showShareModal: !@state.showShareModal

  onViewModeChange: (view_mode)->
    switch view_mode
      when 'day'
        if @props.journalId?
          @.app.entryActionCreators.updateViewMode({view_mode: view_mode}, 'Journal' + @props.journalId)  
        else if @props.tagId?
          @.app.entryActionCreators.updateViewMode({view_mode: view_mode}, 'Tag' + @props.tagId)
        else
          @.app.entryActionCreators.updateViewMode({view_mode: view_mode}, 'EntryDetail')
      when 'month','year'
        options = @buildOptions @props.min_date, view_mode    
        if @props.journalId?
          options['journal'] = @props.journalId
          @.app.entryActionCreators.updateEntries(options, 'Journal' + @props.journalId)
          @props.history.pushState null, '/journal/' + @props.journalId
        else if @props.tagId?
          options['tag'] = @props.tagId
          @.app.entryActionCreators.updateEntries(options, 'Tag' + @props.tagId)
          @props.history.pushState null, '/tag/' + @props.tagId
        else
          @.app.entryActionCreators.updateEntries(options, 'Timeline')
          @props.history.pushState null, '/'

  onWidgetCallback: (widget, value, view_mode)->
    if widget == 'calendar'
      if not view_mode?
        view_mode = 'day'
      options = @buildOptions(value, view_mode)
      if @props.journalId?
          options['journal'] = @props.journalId
          @.app.entryActionCreators.updateEntries(options, 'Journal' + @props.journalId)
          @props.history.pushState null, '/journal/' + @props.journalId
      else if @props.tagId?
        options['tags__id'] = @props.tagId
        @.app.entryActionCreators.updateEntries(options, 'Tag' + @props.tagId)
        @props.history.pushState null, '/tag/' + @props.tagId
      else
        @.app.entryActionCreators.updateEntries(options, 'Timeline')
        @props.history.pushState null, '/'

  buildOptions: (value, view_mode)->
    filter_by = @.app.entryStore.getFilterBy()
    options = 
      order_by: @props.order_by
      min_date: moment(value).startOf(view_mode).format('YYYY-MM-DD'+'T'+'00:00:00')
      max_date: moment(value).endOf(view_mode).format('YYYY-MM-DD'+'T'+'23:59:59')
      filter_by: filter_by
      view_mode: view_mode

    return options

  renderEntry: ()->
    if @props.entry?
      return (
        <EntryTile {...@props} showComments={@toggleComments} openContactModal={@openContactModal} entry={@props.entry}/>
      )
    else
      return undefined

  componentDidMount: ->   
    @.app.calendarActionCreators.lockWidget()

  render: ()->
    view_title = @props.journal?.title
    <div>
      {
        if @state.showContactModal
          <ContactModal show={@state.showContactModal} onHide={@closeContactModal} entry={@state.contactModalEntry} {...@props} />
      }
      <Drawer />
      <div id="page">
        <AppHeader username={@props.username} view_title={view_title} view_title_is_editable={false} {...@props}/>
        <EntryDetailHeader onViewModeChange={@onViewModeChange} widgetCallback={@onWidgetCallback} toggleShareModal={@toggleShareModal} showComments={@toggleComments} {...@props}/>
        {
          if @state.showShareModal
            <InvitationModal onHide={@closeShareModal} show={@state.showShareModal} {...@props} initialHeader={'Share Entry'}/>
        }
        <div id="content">
          <div className="container">  
            <div id="timeline-column" className="pull-left right-padding">
              {
                @renderEntry()
              }
            </div>
            {
              if @state.showComments and @props.entry?
                <Sidebar {...@props}>
                  <CommentWidget onHide={@closeComments} onShareModal={@openShareModal} {...@props}/>
                </Sidebar>
              else
                <Sidebar widgetCallback={@onWidgetCallback} {...@props}/>  
            }
          </div>
        </div>
      </div>
    </div>
          

EntryDetailContainer = Marty.createContainer EntryDetail,
  listenTo: ['entryStore', 'journalStore','calendarStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      entryId = @props.params.entryId
      if entryId?
        return {
          entry: @props.app.entryStore.getEntryById(entryId)
          username: @.props.app.authStore.getUsername()
          firstname: @.props.app.authStore.getFirstNameOrUsername()
          user: @props.app.authStore.fetchUser()
          journal: @props.app.journalStore.getJournalByEntryId(entryId)
          show_comments: false
          defaultShowComments: @props.location.query.comments? || false
          view_mode: 'day'
          journalId: @props.location.query.journalId
          tagId: @props.location.query.tagId
          searchId: @props.location.query.searchId
          aamId: @props.location.query.aamId
          entryId: entryId
        }
      else
        return {}
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    if results.entry?
      props['min_date'] = moment.utc(results.entry.entry_date).local().startOf('day')
      props['max_date'] = moment.utc(results.entry.entry_date).local().startOf('day')
    return <EntryDetail {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <EntryDetail {...props} />
  failed: (error)->
    console.log error
    return <div>Entry Error</div>

module.exports = EntryDetailContainer