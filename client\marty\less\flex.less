.flex-container {
	display: -webkit-box;
	display: -webkit-flex;
	display: -ms-flexbox;
	display: flex;
}

.flex-left {
	-webkit-align-items: center;
	align-items: center;
	-webkit-justify-content: flex-start;
	justify-content: flex-start;
}

.flex-centered {
	-webkit-align-items: center;
	align-items: center;
	-webkit-justify-content: center;
	justify-content: center;
}

.flex0 {
  -webkit-box-flex: 0;
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
}

.flex1 {
	-webkit-box-flex: 1;
	-webkit-flex: 1 0 auto;
	-ms-flex: 1 0 auto;
	flex: 1 0 auto;
}

.flex-column {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.flex-row {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.flex-middle {
	-webkit-box-pack: center;
	-webkit-justify-content: center;
	-ms-flex-pack: center;
	justify-content: center;
}

.flex-shrink {
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.flex-scroll-vert {
  -ms-flex: 0 1 auto;
  position: relative;
  display: inline-block;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
  -webkit-overflow-scrolling: touch;
}

// ALIGN CONTENT (Vertically)

.flex-align-start {
	-webkit-align-items: flex-start;
	align-items: flex-start;
}

.flex-align-end {
	-webkit-align-items: flex-end;
	align-items: flex-end;
}

.flex-align-center {
	-webkit-align-items: center;
	align-items: center;
}

.flex-align-stretch {
	-webkit-align-items: stretch;
	align-items: stretch;
}

// JUSTIFY CONTENT (Horizontally)

.flex-justify-start {
	-webkit-justify-content: flex-start;
	justify-content: flex-start;
}

.flex-justify-end {
	-webkit-justify-content: flex-end;
	justify-content: flex-end;
}

.flex-justify-center {
	-webkit-justify-content: center;
	justify-content: center;
}

.flex-justify-between {
	-webkit-justify-content: space-between;
	justify-content: space-between;
}

.flex-justify-around {
	-webkit-justify-content: space-around;
	justify-content: space-around;
}