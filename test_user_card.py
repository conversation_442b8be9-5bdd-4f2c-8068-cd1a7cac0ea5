#!/usr/bin/env python
"""
Test the specific AMEX card number: ***************
"""
import re

def test_user_card():
    card_number = "***************"
    
    print("=" * 60)
    print("TESTING USER'S AMEX CARD: ***************")
    print("=" * 60)
    
    # Step 1: Basic checks
    print(f"Card number: {card_number}")
    print(f"Length: {len(card_number)}")
    
    try:
        num = [int(n) for n in card_number]
        print(f"Digits: {num}")
        print("✓ Contains only digits")
    except ValueError:
        print("✗ Contains non-numeric characters")
        return
    
    # Step 2: AMEX pattern check
    amex_pattern = r'3[47]\d{13}$'
    matches_amex = bool(re.match(amex_pattern, card_number))
    print(f"\nAMEX Pattern Check:")
    print(f"Pattern: {amex_pattern}")
    print(f"Matches: {matches_amex}")
    
    if matches_amex:
        print("✓ PASS: Matches AMEX pattern")
    else:
        print("✗ FAIL: Does not match AMEX pattern")
        return
    
    # Step 3: <PERSON>hn algorithm check (exact same as util.py line 118)
    print(f"\nLuhn Algorithm Check:")
    print("-" * 30)
    
    # num[::-2] - every other digit starting from the end (odd positions from right)
    odd_positions = num[::-2]
    print(f"Odd positions (from right):  {odd_positions}")
    
    # num[-2::-2] - every other digit starting from second-to-last (even positions from right)
    even_positions = num[-2::-2]
    print(f"Even positions (from right): {even_positions}")
    
    # Double the even position digits and sum digits if > 9
    doubled_evens = [sum(divmod(d * 2, 10)) for d in even_positions]
    print(f"Doubled evens: {doubled_evens}")
    
    # Calculate total sum
    total_sum = sum(odd_positions + doubled_evens)
    print(f"Total sum: {total_sum}")
    print(f"Sum % 10: {total_sum % 10}")
    
    # Check if valid (sum divisible by 10)
    is_luhn_valid = total_sum % 10 == 0
    
    if is_luhn_valid:
        print("✓ PASS: Luhn algorithm check")
    else:
        print("✗ FAIL: Luhn algorithm check")
        print("This is why the card is rejected!")
    
    # Step 4: Show what the correct check digit should be
    print(f"\nCheck Digit Analysis:")
    print("-" * 30)
    
    # Calculate what the last digit should be for a valid card
    card_without_check = card_number[:-1]  # Remove last digit
    
    for check_digit in range(10):
        test_card = card_without_check + str(check_digit)
        test_num = [int(n) for n in test_card]
        test_odd = test_num[::-2]
        test_even = test_num[-2::-2]
        test_doubled = [sum(divmod(d * 2, 10)) for d in test_even]
        test_sum = sum(test_odd + test_doubled)
        
        if test_sum % 10 == 0:
            print(f"✓ Correct card number would be: {test_card}")
            print(f"  (Last digit should be {check_digit}, not {card_number[-1]})")
            break
    
    return is_luhn_valid

def main():
    is_valid = test_user_card()
    
    print("\n" + "=" * 60)
    print("CONCLUSION")
    print("=" * 60)
    
    if is_valid:
        print("✅ The card number is mathematically valid")
        print("The issue must be elsewhere (expiration, CVV, etc.)")
    else:
        print("❌ The card number FAILS Luhn validation")
        print("This is exactly why the user gets 'Credit card number is not valid'")
        print()
        print("SOLUTION:")
        print("1. Ask the user to double-check each digit of the card number")
        print("2. The user likely has a typo in one of the digits")
        print("3. Have them re-enter the card number carefully")
        print("4. If they insist the number is correct, the card itself may be invalid")

if __name__ == '__main__':
    main()
