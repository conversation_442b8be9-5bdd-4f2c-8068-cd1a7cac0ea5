/*---------------------------------------------------------------------------
  >Comments
---------------------------------------------------------------------------*/

.new-comment-alert {

	.icon {color: @alert !important;}
	
	span {	
		&:hover {color: @teal;}
	}
}

.comments {
	
	p {margin: 8px 0;}
	
  p, blockquote, li {.f13;}
	
	.avatar {
		width: 28px;
		height: 28px;
	}
	
	.more-comments {
		text-align: center;
		
		a {
			padding: 8px;
			display: block;
		}
	}
	
	//.add-comment-box {padding-left: 58px;}
	
	.comment-textarea {
		border: 1px solid @gray7;
		padding: 7px 10px;
		resize: vertical;
		font-size: 13px;
		.full-width;
		.radius3;
	}
	
	.comment-btn {margin: 10px 0 0 10px;}
	
	.comment {
    padding: 1em 1em 0;
    margin-bottom: -8px;
		
		.comment-heading {
		  display: inline-block;
			.table-wrapper;
		  
		  .ch-row {
			  .table-row; 
			  
			  .col {
				  vertical-align: middle;
				  .table-cell;
				}
			  
				.col.avatar {width: 40px;}
			  
			  .content {

				  .name {
		 				display: block;
 				    margin-bottom: -2px;
 				    .truncate;
		 				.navy-txt;
		 				.w600;
		 				
		 				&:hover {.teal-txt;}
				  }

				  .date {
		 				display: block;
					  .f11;
					  .gray5-txt;
					  .w400;
				  }
			  }
		  }
		  
			.options {
				width: 30px;
			
			  .dropdown {
				  display: none;
				  top: -1px;
				  right: -4px;
		      z-index: 10;
		      
		      .dropdown-menu {
			      right: -1px !important;
						top: 13px;
			    }
				  
				  .dropdown-toggle {
					  font-size: 18px;
					  line-height: 0;
				    width: 21px;
				    display: block;
					  color: @gray8;
						top: -5px;					  
				  }
			  }	
			}
		}
		
		.edit-comment-box {
			margin-top: 12px;
			
			textarea {
				min-height: 120px;
				.comment-textarea;
			}
		}
		
		.btn {.comment-btn}
		
		&:hover .dropdown {display: block !important;}
	}
	
	.add-comment-box { 
		padding: 1em;
    display: table;
    width: 100%;
		
		.btn {.comment-btn}
		
		textarea {
			display: block;
			.comment-textarea;
			
			&:focus {min-height: 120px;}
		}
	}
}