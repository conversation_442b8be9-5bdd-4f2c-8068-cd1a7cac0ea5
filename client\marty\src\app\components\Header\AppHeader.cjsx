React = require 'react'
_ = require 'underscore'
UserControl = require './Controls/UserControl'
JournalsAndEventsModal = require '../Modals/JournalsAndEventsModal'

AppHeader = React.createClass
  displayName: 'AppHeader'

  getInitialState: ->
    showJournalsModal: false

  openJournalsModal: ()->
    @setState
      showJournalsModal: true

  closeJournalsModal: ()->
    @setState
      showJournalsModal: false

  getViewFilters: ()->
    viewFilterList = []
    if @props.entry_view_mode?
      viewModeFilter = ""
      switch @props.entry_view_mode
        when 'all'
          viewModeFilter = "All entries"
        when 'just_me'
          viewModeFilter = "Just my entries"
        when 'shared_by_me'
          viewModeFilter = "Entries I have shared"
        when 'shared_with_me'
          viewModeFilter = "Entries shared with me"
      
      if not _.isEmpty viewModeFilter
        viewFilterList.push viewModeFilter
    
    if @props.order_by?
      sortOrder = @props.order_by.substring 0,1
      if sortOrder is "-"
        sortOrder = '(newest first)'
      else
        sortOrder = '(oldest first)'

      sortMode = @props.order_by.substring 1

      switch sortMode
        when 'entry_date'
          sortModeFilter = "Entry Date"
        when 'created'
          sortModeFilter = "Date created"
        when 'modified'
          sortModeFilter = "Date updated"

      if not _.isEmpty sortModeFilter
        sortModeFilter = "#{sortModeFilter} #{sortOrder}"
        viewFilterList.push sortModeFilter

    return viewFilterList

  renderViewFilters: ()->
    viewFilters = @getViewFilters()
    if viewFilters?
      <div className="subtitle">
        {
          viewFilters.join ', '
        }
      </div>
    else
      return undefined

  renderEditableTitle: ()->
    <div className="col col-left">
      {
        if @state.showJournalsModal
          <JournalsAndEventsModal {...@props} onUpdateJournal={@props.onUpdateJournal} onHide={@closeJournalsModal} show={@state.showJournalsModal} />
      }
      <a className='btn-title' onClick={@openJournalsModal}><i className="icon icon-journal teal"></i>{@props.view_title}</a>
    </div>

  renderNonEditableTitle: ()->
    <div className="col col-left">
      <div className="title">{@props.view_title}</div>
      {
        @renderViewFilters()
      }
    </div>
  
  renderTitle: ()->
    if @props.view_title? and @props.view_title_is_editable
      @renderEditableTitle()
    else
      @renderNonEditableTitle()

  render: ()->
    <header className="white-bg">
      <div id="nav-main" className="navbar navbar-static-top">
        <div className="container">
          <div className="title-wrapper">
            <div className="title-row">  
              {
                @renderTitle()
              }
              <div className="col col-right">
                <UserControl user_name={@props.firstname} {...@props}/>
              </div>
            </div>
          </div>
        </div>  
      </div>
    </header>

module.exports = AppHeader