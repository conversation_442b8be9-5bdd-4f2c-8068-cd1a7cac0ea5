React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Link = require('react-router').Link

AllAboutMeSectionIntro = React.createClass
  displayName: 'AllAboutMe SectionIntro'

  renderNewButton: ()->
    if @props.random_question?.id?
      <Link to={"/all-about-me/new/" + @props.random_question.id} className="btn btn-navy btn-large">Answer a Random Question</Link>
    else
      <a className="btn btn-navy btn-large">Answer a Random Question</a>

  render: ()->
    <div className="alert section-intro teal-bg alert-dismissible">
      <img src="/static/images/aam-instruction-image.jpg" style={width:"100%", height:"auto"} />
      <div className="section-intro-body">
        <h2>All About Me provides you with hundreds of questions that span across many different aspects of your life.</h2>
        <h3 style={"marginBottom": "30px"}>Whether you have zero journaling experience or are a seasoned pro, this section will helps you to answer key questions that cover some of the most significant moments in life.</h3>
        {
          @renderNewButton()
        }
      </div>
    </div>

module.exports = AllAboutMeSectionIntro