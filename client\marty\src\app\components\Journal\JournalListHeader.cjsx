React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Link = require('react-router').Link
Sticky = require 'react-sticky'

DateRangeHeaderControl = require '../Header/Controls/DateRangeHeaderControl'
DateRangePagerControl = require '../Header/Controls/DateRangePagerControl'
EntryListSortControl = require '../Header/Controls/EntryListSortControl'

JournalListHeader = React.createClass
  displayName: 'JournalListHeader'
  STICKY_STYLES: {}

  renderNewEntry: ()->
    if @props.journal?
      if @props.journal.journal_type is 'AllAboutMe'
        return (
          <Link to={"/all-about-me/new/" + @props.random_question.id} query={aamId: 1} className="btn btn-new btn-navy pull-right">Answer a Random Question</Link>
        )
      else
        return (
          <Link to={"/entry/journal/create/" + @props.journal?.id} className="btn btn-new pull-right btn-navy">New Entry</Link>
        )
    else
      return undefined
      
  render: ()->
    <Sticky stickyStyle={@STICKY_STYLES} stickyClass={'nav-sticky'} topOffset={-60}>
      <div id="nav_crumb" className="gray">
        <div className="container">
          <div id="crumb-bar" className="pull-left">
            <DateRangeHeaderControl calendar_id={'Journal'} widgetCallback={@props.widgetCallback} {...@props}/>
            <div id="options" className="pull-right">
              <DateRangePagerControl {...@props} />
              <EntryListSortControl order_by={@props.order_by} onEntryViewModeChange={@props.onEntryViewModeChange} entry_view_mode={@props.entry_view_mode} user={@props.user}/>
            </div>
          </div>
          <div id="tools" className="pull-right text-right">
            {
              @renderNewEntry()
            }
          </div>
        </div>
      </div>
    </Sticky>

JournalListHeaderContainer = Marty.createContainer JournalListHeader,
  listenTo: ['journalStore','entryStore','allaboutmeStore']

  fetch: ()->
    return {
      random_question: @.app.allaboutmeStore.getRandomQuestion()
    }
  done: (results)->
    props = _.extend {}, @props, results
    return <JournalListHeader {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <div></div>
  failed: (error)->
    console.log error
    return <div>Error</div>

module.exports = JournalListHeaderContainer