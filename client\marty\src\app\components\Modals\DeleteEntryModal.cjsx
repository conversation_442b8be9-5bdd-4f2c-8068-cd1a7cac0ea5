React = require 'react'
<PERSON> = require 'marty'
Modal = require('react-bootstrap').Modal

History = require('react-router').History

DeleteEntryModal = React.createClass
  displayName: 'Delete Entry Modal'
  mixins: [Marty.createAppMixin(), History]

  propTypes:
    type: React.PropTypes.string
    object: React.PropTypes.object
    onRequestHide: React.PropTypes.func

  onCancel: ()->
    @props.onHide()

  onConfirm: ()->
    transitionToTarget = undefined
    shouldTransition = false
    if @props.entryId?
      shouldTransition = true
      if @props.aamId?
        transitionToTarget = @history.createPath '/all_about_me/'
        
      else if @props.journalId?
        transitionToTarget = @history.createPath '/journal/' + @props.journalId
        
      else if @props.tagId
        transitionToTarget = @history.createPath '/tag/' + @props.tagId
        
      else if @props.searchId?
        transitionToTarget = @history.createPath '/search/'
        
      else
        transitionToTarget = @history.createPath '/timeline/'
    
    @.app.entryActionCreators.deleteEntry(@props.object, transitionToTarget, shouldTransition)
    @props.onHide()

  render: ()->
    <Modal onHide={@props.onHide} show={@props.showModal} {...@props}>
      <div className="text-center">
        <div className="modal-body">
          <div className="confirm-dialogue">
            <h2>You are about to delete {@props.type}
            <p><strong className="teal">"{@props.object.title}"</strong></p>
            </h2>
            <p><strong>Are you sure?</strong></p>
            <ul className="share-buttons">          
              <li>
                <a onClick={@onCancel} className="btn btn-teal btn-large share-2">No</a>
              </li>
              <li>
                <a onClick={@onConfirm} className="btn btn-navy btn-large">Yes</a>
              </li>
            </ul>
          </div>    
        </div>
      </div>
    </Modal>

module.exports = DeleteEntryModal