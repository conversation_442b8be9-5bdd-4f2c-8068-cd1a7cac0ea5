React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
classnames = require 'classnames'

CommentTypeItem = React.createClass
  displayName: 'CommentType'

  renderCommentCount: ()->
    if @props.comments?.length is 1
      <div className="comment-count">
        {
          if @props.new
            <i className="new fa fa-circle"></i>
        }
        {@props.comments.length} comment
      </div>
    else if @props.comments?.length > 1
      return <div className="comment-count">{@props.comments.length} comments</div>
    else
      return <div className="comment-count">{@props.comments?.length || 0} comments</div>

  formatUserCount: ()->
    if @props.groupMemberCount > 1
      return "#{@props.groupMemberCount} people"
    else if @props.groupMemberCount is 1
      return "#{@props.groupMemberCount} person"
    else
      return "no one"

  onItemClick: (e)->
    if @props.onItemClick?
      @props.onItemClick(@props.publicUser)

  renderTitle: ()->
    switch @props.type
      when 'personal'
        title = "Just Me"
      when 'group'
        title = "Shared with " + @formatUserCount()
      when 'one_on_one'
        title = "#{@props.publicUser?.username} & me"
      else
        title = "Just Me"
    return <div className="title">{title}</div>

  render: ()->
    <div className="item-wrapper">
      <div className="item-row">
        <a onClick={@onItemClick} className="item">
          {
            @renderTitle()
          }
          {
            @renderCommentCount()
          }
        </a>
      </div>
    </div>

CommentTypeItemContainer = Marty.createContainer CommentTypeItem,
  listenTo: ['publicUserStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      if @props.channelUser?      
        return {
          publicUser: @props.app.publicUserStore.getPublicUser(@props.channelUser)
        }
      else 
        return {}
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <CommentTypeItem {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <CommentTypeItem {...props} />
  failed: (error)->
    console.log error
    return <div>CommentTypeItem Error</div>

module.exports = CommentTypeItemContainer