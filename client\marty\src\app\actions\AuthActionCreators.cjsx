Marty = require 'marty'
authConstants = require '../constants/AuthConstants'
google_trackConversion = require 'google_trackConversion'
settings = require '../settings'

AuthActionCreators = Marty.createActionCreators
  authenticate: (username, password, nextPath)->
    @.app.authHttpAPI.authenticate username.trim(), password.trim()
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (success)=>
      if google_trackConversion?
        google_trackConversion settings.GA_ACTIVE_REMARKETING
      @.dispatch authConstants.RECEIVE_AUTHENTICATION, success, nextPath
      return
    .catch (error)=>
      console.log error
      @.dispatch authConstants.AUTHENTICATION_ERROR, error
      return

  logout: ()->
    @.dispatch authConstants.LOGOUT

  reset: ()->
    @.dispatch authConstants.RESET_AUTHENTICATION

  resetPasswordState: ()->
    @.dispatch authConstants.RESET_PASSWORD_STATE

  resetPasswordUpdateState: ()->
    @.dispatch authConstants.RESET_PASSWORD_UPDATE_STATE

  resetUserError: ()->
    @dispatch authConstants.RESET_USER_ERROR

  resetUserSuccess: ()->
    @dispatch authConstants.RESET_USER_SUCCESS    

  updateProfile: (user, id)->
    return @.app.authHttpAPI.updateUser(user)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (success)=>
      console.log success
      @dispatch authConstants.RECEIVE_USER, success
    .catch (error)=>
      @.dispatch authConstants.USER_ERROR, error

  requestPasswordReset: (options)->
    @.app.authHttpAPI.requestPasswordReset(options)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (success)=>
      @.dispatch authConstants.REQUEST_PASSWORD_RESET, success
    .catch (error)=>
      @.dispatch authConstants.USER_ERROR, error

  changePassword: (options)->
    @.app.authHttpAPI.changePassword(options)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (success)=>
      @.dispatch authConstants.PASSWORD_CHANGE,success
    .catch (error)=>
      @.dispatch authConstants.USER_ERROR, error

  updatePassword: (options)->
    @.app.authHttpAPI.updatePassword(options)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (success)=>
      @.dispatch authConstants.PASSWORD_UPDATED,success
    .catch (error)=>
      @.dispatch authConstants.USER_ERROR, error


module.exports = AuthActionCreators
  