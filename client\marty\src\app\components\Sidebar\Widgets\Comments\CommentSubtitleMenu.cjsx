React = require 'react'

Dropdown = require('react-bootstrap').Dropdown
  
CommentSubtitleMenu = React.createClass
  displayName: 'CommentOptionMenu'

  getInitialState: ->
    isOpen: false

  onToggle: (isOpen)->
    @setState
      isOpen: isOpen

  onSelect: (e, key)->
    @setState
      isOpen: false
  
  onManage: ()->
    if @props.onManage?
      @props.onManage()
  
  onDelete: ()->
    console.log 'onDelete'
    alert 'NOT IMPLEMENTED'
  
  onInvite: ()->
    if @props.onInvite?
      @props.onInvite()

  renderMenuOptions: ()->
    options = []
    switch @props.type
      when 'group'
        options.push  <li key={@props.type + '-manage'}><a onClick={@onManage}>Manage Group Sharing</a></li>
        options.push  <li key={@props.type + '-invite'}><a onClick={@onInvite}>Invite More</a></li>
        # options.push  <li key={@props.type + '-delete'}><a onClick={@onDelete}>Delete All</a></li>
      when '1on1', 'one_on_one'
        options.push  <li key={@props.type + '-manage'}><a onClick={@onManage}>Manage 1 on 1 Sharing</a></li>
        # options.push  <li key={@props.type + '-delete'}><a onClick={@onDelete}>Delete this 1 on 1</a></li>
    return options

  render: ()->
    <Dropdown id={'CommentSubtitleOptionMenu'} className="col col-option" componentClass={'div'} onToggle={@onToggle} onSelect={@onSelect} open={@state.isOpen}>
      <Dropdown.Toggle noCaret={true} useAnchor={true} className="dropdown-toggle icon icon-options">
      </Dropdown.Toggle>
      <Dropdown.Menu className='dropdown-swing-left dropdown-default' style={minWidth: '100px'}>
        {   
          @renderMenuOptions()
        }
      </Dropdown.Menu>
    </Dropdown>

module.exports = CommentSubtitleMenu