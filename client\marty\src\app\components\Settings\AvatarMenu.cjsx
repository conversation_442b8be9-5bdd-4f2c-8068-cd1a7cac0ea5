React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

ButtonToolbar = require('react-bootstrap').ButtonToolbar
MenuItem = require('react-bootstrap').MenuItem
Dropdown = require('react-bootstrap').Dropdown
Button = require('react-bootstrap').Button

classnames = require 'classnames'
utils = require('react-bootstrap').utils.bootstrapUtils

utils.addStyle Dropdown.Toggle, ['gray']
utils.addStyle Button, ['gray']

AvatarMenu = React.createClass
  displayName: 'Avatar Menu'
  
  getInitialState: ->
    open: false
  
  setDropdownState: (state, callback)->
    @setState
      open: state
    , callback

  onUploadAvatar: ()->
    @setDropdownState false, ()=>
      if @props.onUploadAvatar?
        @props.onUploadAvatar()

  onRemoveCurrentAvatar: ()->
    @setDropdownState false, ()=>
      if @props.onRemoveCurrentAvatar?
        @props.onRemoveCurrentAvatar()

  render: ()->
    <ButtonToolbar>
      <Dropdown id={'AvatarMenu'}>
        <Dropdown.Toggle bsStyle={'gray'} bsSize={'small'} title={'Change Avatar'}>
        </Dropdown.Toggle>
        <Dropdown.Menu className="dropdown-default">
          <MenuItem onClick={@onUploadAvatar}>Upload New Image</MenuItem>
          <MenuItem onClick={@onRemoveCurrentAvatar}>Remove Current Image</MenuItem>
        </Dropdown.Menu>
      </Dropdown>
    </ButtonToolbar>

AvatarMenuContainer = Marty.createContainer AvatarMenu,
  listenTo: ['userStore']

  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <AvatarMenu {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <AvatarMenu {...props} />
  failed: (error)->
    console.log error
    return <div>Avatar Menu Error</div>

module.exports = AvatarMenuContainer