/*---------------------------------------------------------------------------
  >FONTS & TYPOGRAPHY
---------------------------------------------------------------------------*/

@ss: ~"only screen and (max-width: 480px)";
@xs: ~"only screen and (max-width: 767px)";
@sm: ~"only screen and (min-width: 768px) and (max-width: 991px)";
@md: ~"only screen and (min-width: 992px) and (max-width: 1199px)";
@lg: ~"only screen and (min-width: 1200px) and (max-width: 1599px)";

@ss-xs: ~"only screen and (max-width: 767px)";
@ss-md: ~"only screen and (max-width: 991px)";
@xs-md: ~"only screen and (min-width: 481px) and (max-width: 991px)";
@sm-lg: ~"only screen and (min-width: 768px) and (max-width: 1199px)";

@ss-x: ~"only screen and (min-width: 0px)";
@xs-x: ~"only screen and (min-width: 481px)";
@sm-x: ~"only screen and (min-width: 768px)";
@md-x: ~"only screen and (min-width: 992px)";
@lg-x: ~"only screen and (min-width: 1200px)";
@hg-x: ~"only screen and (min-width: 1600px)";

@x-ss: ~"(max-width: 480px)";
@x-sm: ~"(max-width: 767px)";
@x-md: ~"(max-width: 991px)";
@x-lg: ~"(max-width: 1199px)";
@x-hg: ~"(max-width: 1599px)";

.font1 {font-family: 'Open Sans', sans-serif;}
.font2 {font-family: 'Roboto Slab', sans-serif;}

/*********** >Sizes ***********/

.f8 {font-size: 8px; line-height: 15px;}
.f9 {font-size: 9px; line-height: 16px;}
.f10 {font-size: 10px; line-height: 17px;}
.f11 {font-size: 11px; line-height: 18px;}
.f12 {font-size: 12px; line-height: 19px;}
.f13 {font-size: 13px; line-height: 20px;}
.f14 {font-size: 14px; line-height: 22px;}
.f15 {font-size: 15px; line-height: 23px;}
.f16 {font-size: 16px; line-height: 24px;}
.f17 {font-size: 17px; line-height: 25px;}
.f18 {font-size: 18px; line-height: 26px;}
.f19 {font-size: 19px; line-height: 27px;}
.f20 {font-size: 20px; line-height: 28px;}
.f21 {font-size: 21px; line-height: 29px;}
.f22 {font-size: 22px; line-height: 30px;}
.f23 {font-size: 23px; line-height: 31px;}
.f24 {font-size: 24px; line-height: 32px;}
.f25 {font-size: 25px; line-height: 33px;}
.f26 {font-size: 26px; line-height: 34px;}
.f27 {font-size: 27px; line-height: 35px;}
.f28 {font-size: 28px; line-height: 36px;}
.f29 {font-size: 29px; line-height: 37px;}
.f30 {font-size: 30px; line-height: 38px;}
.f31 {font-size: 31px; line-height: 39px;}
.f32 {font-size: 32px; line-height: 40px;}
.f33 {font-size: 33px; line-height: 41px;}
.f34 {font-size: 34px; line-height: 42px;}
.f35 {font-size: 35px; line-height: 43px;}
.f36 {font-size: 36px; line-height: 44px;}
.f37 {font-size: 37px; line-height: 45px;}
.f38 {font-size: 38px; line-height: 46px;}
.f39 {font-size: 39px; line-height: 47px;}
.f40 {font-size: 40px; line-height: 48px;}

/*********** >Font Weights ***********/

.w300 {font-weight: 300;}
.w400 {font-weight: 400;}
.w500 {font-weight: 500;}
.w600 {font-weight: 600;}
.w700 {font-weight: 700;}
.w800 {font-weight: 800;}

/*********** >Uppercase ***********/

.uppercase {text-transform: uppercase;}

.truncate {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

/*---------------------------------------------------------------------------
  >COLORS
---------------------------------------------------------------------------*/

@gray0:  hsla(255,5%,5%,1);
@gray1:  hsla(255,5%,10%,1);
@gray2:  hsla(255,5%,20%,1);
@gray3:  hsla(255,5%,30%,1);
@gray4:  hsla(255,5%,40%,1);
@gray5:  hsla(255,5%,50%,1);
@gray6:  hsla(255,5%,60%,1);
@gray7:  hsla(255,5%,70%,1);
@gray8:  hsla(255,5%,80%,1);
@gray9:  hsla(255,5%,90%,1);
@gray10: hsla(255,5%,95%,1);

@teal0: #0ED1E0;
.teal0-txt {color: @teal0;}
.teal0-bg {background-color: @teal0;}

@teal: #00B5CC;
.teal-txt {color: @teal;}
.teal-bg {background-color: @teal;}

@teal2: #009bb8;
.teal2-txt {color: @teal2;}
.teal2-bg {background-color: @teal2;}

@teal3: #0091b0;
.teal3-txt {color: @teal3;}
.teal3-bg {background-color: @teal3;}

@navy: #002856;
.navy-txt {color: @navy;}
.navy-bg {background-color: @navy;}

@royal: #1D5493;
.royal-txt {color: @royal;}
.royal-bg {background-color: @royal;}

.gray0-txt {color: @gray0;}
.gray0-bg {background-color: @gray0;}

.gray1-txt {color: @gray1;}
.gray1-bg {background-color: @gray1;}

.gray2-txt {color: @gray2;}
.gray2-bg {background-color: @gray2;}

.gray3-txt {color: @gray3;}
.gray3-bg {background-color: @gray3;}

.gray4-txt {color: @gray4;}
.gray4-bg {background-color: @gray4;}

.gray5-txt {color: @gray5;}
.gray5-bg {background-color: @gray5;}

.gray6-txt {color: @gray6;}
.gray6-bg {background-color: @gray6;}

.gray7-txt {color: @gray7;}
.gray7-bg {background-color: @gray7;}

@white: white;
.white-txt {color: @white;}
.white-bg {background-color: @white;}

@yellow: #FFF9D7;
.yellow-bg {background-color: @yellow;}

@green: #72CE00;
.green-txt {color: @green;}
.green-bg {background-color: @green;}

@light-blue: #51b6e1;
.light-blue-txt {color: @light-blue;}
.light-blue-bg {background-color: @light-blue;}

@orange: #FFA500;
.orange-txt {color: @orange;}
.orange-bg {background-color: @orange;}

@orange2: #FFBC00;

@red: #AC0000;
.red-txt {color: @red;}
.red-bg {background-color: @red;}

@red2: #d30000;

@red-light: #FFE2E2;

@color-personal: #5D6D7F;
.color-personal-bg {background-color: @color-personal;}

@color-group: #CE6052;
.color-group-bg {background-color: @color-group;}

@color-1on1: #42B39C;
.color-1on1-bg {background-color: @color-1on1;}

@alert: #ff7200;
.alert-bg {background-color: @alert;}

@plus: #ED5C5C;
@prime: #2A7BC4;

.gradient-w-g {
background: #ffffff; /* Old browsers */
/* IE9 SVG, needs conditional override of 'filter' to 'none' */
background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2ZmZmZmZiIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNmNGY0ZjUiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
background: -moz-linear-gradient(top,  #ffffff 0%, #f4f4f5 100%); /* FF3.6+ */
background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(100%,#f4f4f5)); /* Chrome,Safari4+ */
background: -webkit-linear-gradient(top,  #ffffff 0%,#f4f4f5 100%); /* Chrome10+,Safari5.1+ */
background: -o-linear-gradient(top,  #ffffff 0%,#f4f4f5 100%); /* Opera 11.10+ */
background: -ms-linear-gradient(top,  #ffffff 0%,#f4f4f5 100%); /* IE10+ */
background: linear-gradient(to bottom,  #ffffff 0%,#f4f4f5 100%); /* W3C */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#f4f4f5',GradientType=0 ); /* IE6-8 */

}

.gradient-g-w {
	background: #f4f4f5; /* Old browsers */
	/* IE9 SVG, needs conditional override of 'filter' to 'none' */
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2Y0ZjRmNSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNmZmZmZmYiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
	background: -moz-linear-gradient(top, #f4f4f5 0%, #ffffff 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f4f4f5), color-stop(100%,#ffffff)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top, #f4f4f5 0%,#ffffff 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top, #f4f4f5 0%,#ffffff 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top, #f4f4f5 0%,#ffffff 100%); /* IE10+ */
	background: linear-gradient(to bottom, #f4f4f5 0%,#ffffff 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f4f4f5', endColorstr='#ffffff',GradientType=0 ); /* IE6-8 */
}

/*---------------------------------------------------------------------------
  >BORDERS
---------------------------------------------------------------------------*/

.hide-overflow {overflow: hidden;}

.border-top {border-top: 1px solid @gray9;}

.border-right {border-right: 1px solid @gray9;}

.border-btm {border-bottom: 1px solid @gray9;}

.border-btm-dark {border-bottom: 1px solid rgba(0, 0, 0, 0.4);}

.shadow {box-shadow: 0 1px 5px rgba(0,0,0,.15);}

.shadow-small {box-shadow: 0 1px 3px rgba(0,0,0,.15);}

.frame {
	.white-bg;
	.radius3;
	.shadow;
	.fl;
}

.full-width {
	width: 100%;
}

.nmp {
	margin: 0;
	padding: 0;	
}

.padding-20 {padding: 20px !important;}
.padding-30 {padding: 30px !important;}
.padding-40 {padding: 40px !important;}


/*********** >Radius ***********/

.radius-top {border-radius: 3px 3px 0 0;}

.radius-bottom {border-radius: 0 0 3px 3px;}

.radius3 {border-radius: 3px;}

.radius4 {border-radius: 4px;}

.avatar {border-radius: 100px;}


/*---------------------------------------------------------------------------
  >FLOATS
---------------------------------------------------------------------------*/

.fl {float: left;}

.fr {float: right;}

.fn {float: none;}

/*---------------------------------------------------------------------------
  >Transitions
---------------------------------------------------------------------------*/

.hover-shrink {
	width: 0;
	height: 0;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	margin: auto;
	z-index: 0;
	.trans3;
}

.trans-quick,
.trans4,
#nav_crumb div>div>a,
#nav_crumb .dropdown-toggle,
#editor .editor-button:hover,
#steps>a,
.list>li>a,
.list .item
{
	-webkit-transition: all .4s ease-in-out;
	-moz-transition:    all .4s ease-in-out;
	-o-transition:      all .4s ease-in-out;
	-ms-transition:     all .4s ease-in-out;
	transition:         all .4s ease-in-out;
}

.trans3 {
	-webkit-transition: all .3s ease-in-out;
	-moz-transition:    all .3s ease-in-out;
	-o-transition:      all .3s ease-in-out;
	-ms-transition:     all .3s ease-in-out;
	transition:         all .3s ease-in-out;
}

.trans2 {
	-webkit-transition: all .2s ease-in-out;
	-moz-transition:    all .2s ease-in-out;
	-o-transition:      all .2s ease-in-out;
	-ms-transition:     all .2s ease-in-out;
	transition:         all .2s ease-in-out;
}

.trans-slow,
.trans1,
#nav_crumb div>div>a:hover,
#nav_crumb .dropdown-toggle:hover,
.container,
#editor .editor-button,
#steps>a:hover,
.list>li>a:hover,
.list .item:hover
{
	-webkit-transition: all .1s ease-in-out;
	-moz-transition:    all .1s ease-in-out;
	-o-transition:      all .1s ease-in-out;
	-ms-transition:     all .1s ease-in-out;
	transition:         all .1s ease-in-out;
}

.disabled {
  filter: gray;
  filter: grayscale(1);
  -webkit-filter: grayscale(1);
  pointer-events: none;
	opacity: 0.3 !important;
}


/*---------------------------------------------------------------------------
  >Buttons
---------------------------------------------------------------------------*/

.btn {
  display: inline-block;
  text-align: center;
	border: 1px solid transparent;
	.trans-quick;
	.noselect;
	.font1;
	.w600;
	.radius4;
	
	&:hover {.trans-slow;}
}

.btn-flat {
	.noselect;
	.white-txt;
	.w600;
	font-size: 15px;
  padding: 15px 20px;
  display: block;
  border-radius: 0;
  height: 51px;
	
	&:hover, &:active, &:focus {
	  background-color: rgba(0,0,0,0.15) !important;
	  .white-txt;
	}  
}

#action-nav .pull-right .btn-flat {
	border-left: 1px solid rgba(0, 0, 0, 0.1);
}

#btn_spinner {
  position: absolute;
  left: 0;
  right: 0;
}

.btn-tiny {
	font-size: 12px;
  padding: 3px 10px;
}

.btn-small {
	font-size: 12px;
  padding: 5px 10px;

  #btn_spinner {
    font-size: 21px;
    top: 3px;
  }
}

.btn-medium {
	font-size: 14px;
  padding: 8px 16px;
  
  #btn_spinner {
    font-size: 26px;
    top: 5px;
  }
}

.btn-large {
  font-size: 17px;
  padding: 10px 20px;
  
  #btn_spinner {
    font-size: 28px;
    top: 8px;
  }
}

.btn-huge {
  font-size: 22px;
  padding: 12px 24px;
  
  #btn_spinner {
    font-size: 32px;
    top: 11px;
  }
}

#status_indicator {
	@-webkit-keyframes pulse {
		0% {background-color: #fffbe7;}
		100% {background-color: #fff4b3;}
	}
	
	 -webkit-animation: pulse .6s infinite alternate;
	 border-color: #EADE94;
	.yellow-bg;
		
	.panel-body {
		.table-wrapper;
		
		.col-left, .col-right {
			.table-cell;
			width: 100%;
		}
		
		.col-left {width: 60px;}
		
		.col-right {
			vertical-align: middle;
			padding: 0;
			line-height: 20px;
			color: #5F3F15;
			.w600;
		}
	}
}

.alert-error {.red-bg;}

.icon-times-circle {.gray3-txt;}

.icon-times-circle:hover {.red-txt !important;}

@instagram: 				hsl(340, 75%, 54%);
@instagram-lighter: hsl(340, 80%, 59%);
@facebook: 					hsl(221, 44%, 41%);
@facebook-lighter:  hsl(221, 49%, 46%);
@twitter:  					hsl(206, 82%, 63%);
@twitter-lighter:   hsl(206, 87%, 68%);

/*********** >Instagram Button ***********/

.btn--instagram {
	.white-txt;
	background-color: @instagram;
}

.btn--instagram:hover,
.btn--instagram:focus
{
	.white-txt !important;
	background-color: @instagram-lighter !important;
}

/*********** >Navy Button ***********/

.btn-navy {
	.white-txt;
	.navy-bg;
}

.btn-navy:hover,
.btn-navy:focus
{
	.white-txt !important;
	.royal-bg !important;
}

/*********** >Navy Button ***********/

.btn-red {
	.white-txt;
	.red-bg;
}

.btn-red:hover,
.btn-red:focus
{
	.white-txt !important;
	background-color: @red2 !important;
}

/*********** >Orange Button ***********/

.btn-orange {
	.white-txt;
	.orange-bg;
}

.btn-orange:hover,
.btn-orange:focus
{
	.white-txt !important;
	background: @orange2;
}

/*********** >Personal Button ***********/

.btn-color-personal {
	.white-txt;
	.color-personal-bg;
}

.btn-color-personal:hover,
.btn-color-personal:focus
{
	.white-txt !important;
	background: #6985A5;
}

/*********** >Group Button ***********/

.btn-color-group {
	.white-txt;
	.color-group-bg;
}

.btn-color-group:hover,
.btn-color-group:focus
{
	.white-txt !important;
	background: #E27C6F;
}

/*********** >1 on 1 Button ***********/

.btn-color-1on1 {
	.white-txt;
	.color-1on1-bg;
}

.btn-color-1on1:hover,
.btn-color-1on1:focus
{
	.white-txt !important;
	background: #58D0B8;
}

/*********** >Gray Button ***********/

.btn-gray {
	color: @gray2;
	border-color: @gray7;
	.gradient-w-g;

	&:hover, &:focus {
		.teal-txt;
		background: #ffffff;
		background: -moz-linear-gradient(top,  #ffffff 0%, #ffffff 100%);
		background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(100%,#ffffff));
		background: -webkit-linear-gradient(top,  #ffffff 0%,#ffffff 100%);
		background: -o-linear-gradient(top,  #ffffff 0%,#ffffff 100%);
		background: -ms-linear-gradient(top,  #ffffff 0%,#ffffff 100%);
		background: linear-gradient(to bottom,  #ffffff 0%,#ffffff 100%);
		filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#ffffff',GradientType=0 );

	}
}

/*********** >White Button ***********/

.btn-white {
	border-color: #E4E5EA;
	.navy-txt;
	.white-bg;

	&:hover, &:focus {
		border-color: transparent;
		.white-txt;
		.royal-bg;
	}
}

/*********** >Teal Button ***********/

.btn-teal {
	.white-txt !important;
	.teal-bg;
}

.btn-teal:hover, 
.btn-teal:focus {
	.white-txt;
	.teal0-bg !important;
}

/*---------------------------------------------------------------------------
  >Framework
---------------------------------------------------------------------------*/

* {position: relative;}

html {position: inherit;}

body {
	position: inherit;
	text-rendering: optimizeLegibility;
	text-rendering: geometricPrecision;
	font-smooth: always;
	-webkit-font-smoothing: antialiased;
}

a {
	.teal-txt;
	cursor: pointer;
}

a, 
a:hover, 
a:focus, 
a:active 
{
	outline: 0;
	text-decoration: none;
}

*:focus {
  outline: 0;
  -webkit-box-shadow: none;
}

.hover-teal-txt {
	
	&:hover, &:focus {
		.teal-txt;
	}
}

.avatar-sm {
	width: 36px;
	height: 36px;
}

.dropdown-swing-left {
  right: 5px !important;
  left: inherit;
}

.noselect {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

hr {border-top: 1px solid @gray8;}

/* for FireFox */

input[type="submit"]::-moz-focus-inner, input[type="button"]::-moz-focus-inner, a:focus {border : 0px;}

/* for IE8 */

input[type="submit"]:focus, input[type="button"]:focus, a:focus {outline : none;}

.form-control:-moz-placeholder					 {color: @gray7; .w400;}
.form-control::-moz-placeholder    			 {color: @gray7; .w400;}
.form-control:-ms-input-placeholder			 {color: @gray7; .w400;}
.form-control::-webkit-input-placeholder {color: @gray7; .w400;}

textarea.form-control:-moz-placeholder  				 {.w400;}
textarea.form-control::-moz-placeholder  				 {.w400;}
textarea.form-control:-ms-input-placeholder			 {.w400;}
textarea.form-control::-webkit-input-placeholder {.w400;}