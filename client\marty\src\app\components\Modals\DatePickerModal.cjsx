React = require 'react'
Marty = require 'marty'
moment = require 'moment'
_ = require 'underscore'

Modal = require('react-bootstrap').Modal
ModalHeader = require './Headers/ModalHeader'

DatePicker = require 'react-date-picker'

DatePickerModal = React.createClass
  displayName: 'Date Picker Modal'

  propTypes:
    onDone: React.PropTypes.func

  getInitialState: ->
    viewMode: @props.initialViewMode || 'month'
    date: moment(@props.date, 'YYYY-MM-DD'+'T'+'HH:mm:ss')
  
  componentWillReceiveProps: (nextProps) ->
    if nextProps.date?
      @setState
        date: moment(nextProps.date, 'YYYY-MM-DD'+'T'+'HH:mm:ss')
        
  mapView: (pickerView)->
    JRNL_VIEW_MODES =
      decade: 'year'
      year: 'year'
      month: 'month'

    if pickerView?
      return JRNL_VIEW_MODES[pickerView]
    else
      return 'day'

  onViewChange: (viewMode)->
    console.log viewMode
    @setState
      viewMode: viewMode

  onChange: (date, dateAsMoment, event)->
    @setState
      date: dateAsMoment

  onDone: ()->
    console.log 'onDone'
    if @props.onDone?
      @props.onDone(@state.date)
    @props.onHide()

  onRenderDay: (props)->
    year = props.date.year()
    month = props.date.month()+1
    day = props.date.date()
    
    classNames = []
    
    if props.date.isAfter(moment.utc())
      classNames.push ' dp-upcoming'
    
    if @props.calendar[year]?[month]?
      if _.findWhere(@props.calendar[year][month], {day: day})?
        classNames.push ' entry'

    for name in classNames
      props.className += name
    
    return props

  onToday: ()->
    console.log 'onToday'
    @setState
      viewMode: 'month'
      date: moment()
    ,()=> @onDone()

  render: ()->
    <Modal show={@props.show} onHide={@props.onHide} {...@props}>
      <ModalHeader header={"Choose Date"} onHide={@props.onHide} onDone={@onDone}/>
      <div id="modal_jtd" className="modal-body">
        <div id="calendar-widget">
          <DatePicker navPrev={<i className="icon icon-angle-left"></i>} navNext={<i className="icon icon-angle-right"></i>} minDate={@props.minDate} maxDate={@props.maxDate} onRenderDay={@onRenderDay} view={@state.viewMode} onViewChange={@onViewChange} onChange={@onChange} hideFooter={true} date={@state.date} weekDayNames={['S','M','T','W','T','F','S']} monthFormat={'MMM'}/>
          <div className="dp-footer">
            <div onClick={@onToday} className="dp-footer-today">Today</div>
          </div>
        </div>
      </div>
    </Modal>

DatePickerModalContainer = Marty.createContainer DatePickerModal,
  listenTo: ['calendarStore']

  fetch: () ->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        calendar: @props.app.calendarStore.getCalendar(@props.options || {}, @props.calendar_id)
      }
    else
      return {}
  done: (results)->
    return <DatePickerModal calendar={results.calendar} {...@props} />
  pending: ()->
    return <div></div>
  failed: (error)->
    console.log error
    return <div>DATEPICKERMODAL ERROR</div>

module.exports = DatePickerModalContainer