React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
FormData = require 'react-form-data'
Sticky = require 'react-sticky'

SearchHeader = React.createClass
  displayName: 'Search Header'
  mixins: [FormData]
  STICKY_STYLES: {}
    
  getInitialState: ->
    query: @props.query
  
  componentDidMount: ->
    @formData.query = @props.query
  
  onSearch: (e)->
    options =
      q: @formData.query || undefined
    if @props.onSearch?
      @props.onSearch(options)
    e.stopPropagation()
    e.preventDefault()  

  onChange: (e)->
    @setState
      query: e.target.value

  onKeyDown: (e)->
    if e.keyCode == 13
      @onSearch(e)

  render: ()->
    <Sticky stickyStyle={@STICKY_STYLES} stickyClass={'nav-sticky'} topOffset={-60}>
      <div id="nav_crumb" className="gray">
        <div className="container">
          <div className="input-group" style={"top":"8px"} onChange={@updateFormData}>
            <input type="text" name="query" className="form-control" placeholder="Search for words or phrases" value={@state.query} onChange={@onChange} onKeyDown={@onKeyDown}/>
            <span className="input-group-btn">
              <button onClick={@onSearch} className="btn btn-default btn-navy" type="button">Search</button>
            </span>
          </div>
        </div>
      </div>
    </Sticky>

SearchHeaderContainer = Marty.createContainer SearchHeader,
  fetch: ()->
    return {
      query: @props.query
    }
  done: (results)->
    props = _.extend {}, @props, results
    return <SearchHeader {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <SearchHeader {...props} />
  failed: (error)->
    console.log error
    return <div>Search Error</div>

module.exports = SearchHeaderContainer