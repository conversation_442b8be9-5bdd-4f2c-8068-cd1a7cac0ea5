React = require 'react'
Marty = require 'marty'
moment = require 'moment'
_ = require 'underscore'

AuthenticatedComponent = require '../AuthenticatedComponent'
AddPaymentProfileModal = require('../Modals/PaymentProfileModal').AddPaymentProfileModal
DeletePaymentProfileModal = require '../Modals/DeletePaymentProfileModal'

PaymentProfilePanel = React.createClass
  displayName: 'PaymentProfilePanel'

  CARD_TYPES:
    'mc': 'Mastercard'
    'visa': 'Visa'
    'amex': 'American Express'
    'discover': 'Discover'

  getInitialState: ->
    showDeleteModal: false
    showEditModal: false

  openDeleteModal: ()->
    @setState
      showDeleteModal: true

  closeDeleteModal: ()->
    @setState
      showDeleteModal: false

  closeEditModal: ()->
    @setState
      showEditModal: false

  openEditModal: ()->
    @setState
      showEditModal: true

  formatCardType: ()->
    if @props.payment_profile?.card_type?
      return @CARD_TYPES[@props.payment_profile.card_type]
    else
      return undefined

  formatExpirationDate: ()->
    if @props.payment_profile?.expiration_date?
      return moment.utc(@props.payment_profile.expiration_date).format('MM/YY')
    else
      return undefined    

  render: ()->
    return (
      <div className="panel-body">
        <div className="options pull-right">
          <DeletePaymentProfileModal show={@state.showDeleteModal} onHide={@closeDeleteModal} card_type={@formatCardType()} {...@props} />
          <a onClick={@openDeleteModal} className="icon icon-times-circle"></a>
        </div>
        <div className="card-name">
          <strong>{@formatCardType()}</strong>
          <br/>
          <span className="f12 gray5-txt">{@props.payment_profile?.safe_credit_card_number}</span>
          <br/>
          <span className="f12 gray5-txt">Expiration: {@formatExpirationDate()}</span>
        </div>
      </div>
    )

PaymentProfilePanelContainer = AuthenticatedComponent(Marty.createContainer PaymentProfilePanel,
  listenTo: ['billingStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        payment_profile: @props.app.billingStore.getPaymentProfileByURI(@props.payment_profile_uri)
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <PaymentProfilePanel {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <PaymentProfilePanel {...props}/>
  failed: (error)->
    console.log error
    return <div>Payment Profile Error</div>
)

BillingSettings = React.createClass
  displayName: 'BillingSettings'
  
  getInitialState: ->
    showAddModal: false
    showDeleteModal: false
    success: @props.success

  componentWillReceiveProps: (nextProps) ->
    if nextProps.success != @state.success and nextProps.success
      @setState
        showAddModal: false
      @props.app.billingStore.reset()
  
  openAddModal: ()->
    @setState
      showAddModal: true

  closeAddModal: ()->
    @setState
      showAddModal: false

  renderEmpty: ()->
    return (
      <div className="panel-body">
        No card on file.
      </div> 
    )

  render: ()->
    <div role="tabpanel" className="tab-pane" id="billing-settings">
      <div className="settings-wrapper">
        <h2 className="section-title">Billing</h2>
        <div className="panel panel-default">
          {
            if @props.user_payment_profile?.default_payment_profile?
              payment_profile = @props.user_payment_profile.default_payment_profile
              <PaymentProfilePanelContainer key={@props.user_payment_profile.default_payment_profile} payment_profile_uri={@props.user_payment_profile.default_payment_profile} {...@props} />
            else
              @renderEmpty()
          }
        </div>
        <hr />
        <div>
          <AddPaymentProfileModal show={@state.showAddModal} onHide={@closeAddModal} {...@props}/>
          <a onClick={@openAddModal} className="btn btn-navy btn-med">Add Credit Card</a>
        </div>
      </div>
    </div>

BillingSettingsContainer = AuthenticatedComponent(Marty.createContainer BillingSettings,
  listenTo: ['billingStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn()
      return {
        user_payment_profile: @props.app.billingStore.getUserPaymentProfile()
        success: @props.app.billingStore.getSuccess()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <BillingSettings {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <BillingSettings {...props}/>
  failed: (error)->
    console.log error
    return <div>Billing Settings Error</div>
)

module.exports = BillingSettingsContainer