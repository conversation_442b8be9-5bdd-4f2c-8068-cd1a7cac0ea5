React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'
History = require('react-router').History
Lifecycle = require('react-router').Lifecycle

BookCover = React.createClass
  displayName: 'BookCover'
  mixins: [History, Lifecycle]
  SUCCESS_BOOK_BUILDER_STATUS: [100, 99, 98]

  getInitialState: ->
    subtitle_options: @getSubtitleOptions(@props.book)
    saved: true

  getSubtitleOptions: (book)->
    if book?
      if book.subtitle != ""
        return 'custom'
      else
        return 'date_range'
  
  componentWillReceiveProps: (nextProps) ->
    if nextProps.book? and not @props.book?
      @setState
        subtitle_options: @getSubtitleOptions(nextProps.book)

    if nextProps.saved?
      @setState
        saved: nextProps.saved

  routerWillLeave: ()->
    if !@state.saved
      return "Discard unsaved changes?"
  
  canProceed: ()->
    if @props.book.book_builder_status in @SUCCESS_BOOK_BUILDER_STATUS and @props.book.preview_pdf != null and @props.book.page_count != null
      return true
    else 
      return false

  updateBook: (e)->
    if @props.updateBook?
      @props.updateBook(e)

  updateSubtitleOptions: (e)->
    if e.target.value == 'date_range'
      fakeEvent =
        target:
          value: ""
          name: 'subtitle'
      @updateBook(fakeEvent)
    @setState
      subtitle_options: e.target.value

  onPrevious: ()->
    @history.pushState null, '/book/content/' + @props.params.bookId

  onNext: ()->
    if @props.book.book_builder_status in @SUCCESS_BOOK_BUILDER_STATUS and @props.book.preview_pdf != null and @props.book.page_count != null
      if @props.onSave?
        @props.onSave(true, '/book/preview/')

  onCustomSubtitle: ()->
    @setState
      subtitle_options: 'custom'

  renderDateRange: ()->
    if @props.book?.start_date? and @props.book?.end_date?
      start_date = moment(@props.book.start_date)
      end_date = moment(@props.book.end_entry_date)

      start_date_subtitle = start_date.format('MMMM D, YYYY')
      end_date_subtitle = end_date.format('MMMM D, YYYY')

      subtitle = "(" + start_date_subtitle + " to " + end_date_subtitle + ")"

      return subtitle

    else if @props.journal?
      start_date = moment.utc(@props.journal.first_entry_date).local()
      end_date = moment.utc(@props.journal.last_entry_date).local()

      start_date_subtitle = start_date.format('MMMM D, YYYY')
      end_date_subtitle = end_date.format('MMMM D, YYYY')

      subtitle = "(" + start_date_subtitle + " to " + end_date_subtitle + ")"

      return subtitle
    else
      return undefined
  
  renderNextButton: ()->
    if @canProceed() 
      return <a onClick={@onNext} className="btn btn-navy btn-large pull-right">Next</a>
    else
      return <a onClick={@onNext} className="btn btn-navy btn-large pull-right disabled">Calculating page count and preview</a>

  render: ()->
    <form>
      <h2 className="section-title">Front Cover</h2>
      <div className="form-group">
        <label htmlFor="bookTitle">Book Title</label>
        <input name="title" type="text" className="form-control full-width" id="bookTitle" value={@props.book?.title} placeholder={@props.book?.title || "Type a book title"} onChange={@updateBook}/>
      </div>
      <div className="form-group">
        <label>Subtitle</label>
        <div className="radio">
          <ul>
            <li>
              <label>
                <input type="radio" name="subtitle_options" value="date_range" checked={@state.subtitle_options == "date_range"} onChange={@updateSubtitleOptions}/>
                Show a date range {@renderDateRange()}
              </label>
              
            </li>
            <li>
              <label>
                <input type="radio" name="subtitle_options" value="custom" checked={@state.subtitle_options == "custom"} onChange={@updateSubtitleOptions}/>
                Show a custom subtitle 
              </label>
              <input type="text" name="subtitle" onClick={@onCustomSubtitle} className="form-control full-width" value={@props.book?.subtitle} placeholder={@props.book?.subtitle || "Type a subtitle"} onChange={@updateBook}/>
            </li>
          </ul>
        </div>
      </div>
      <hr />
      <h2 className="section-title">Spine</h2>
      <div className="form-group">
        <label htmlFor='spine-input'>Choose what you want to appear on the book spine.
        </label>
        <input type="text" id="spine-input" className="form-control full-width" name="spine" placeholder={@props.book?.spine || "Spine text"} value={@props.book?.spine} onChange={@updateBook}/>
      </div>
      <hr />
      <h2 className="section-title">Back Cover (Optional)</h2>
        <div className="form-group">
          <textarea name='back_cover' className="form-control full-width" rows="3" placeholder={@props.book?.back_cover} value={@props.book?.back_cover} onChange={@updateBook}></textarea>
        </div>
      <hr />
      <h2 className="section-title">Custom Introduction Page (Optional)</h2>
        <div className="form-group">
          <textarea name='custom_introduction' className="form-control full-width" rows="3" placeholder={@props.book?.custom_introduction} value={@props.book?.custom_introduction} onChange={@updateBook}></textarea>
        </div>
      <hr />
      <a onClick={@onPrevious} className="btn btn-gray btn-large pull-left">Previous</a>
      {
        @renderNextButton()
      }
    </form>

BookCoverContainer = Marty.createContainer BookCover,
  listenTo: []

  fetch:()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <BookCover ref="innerComponent" {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <BookCover ref="innerComponent" {...props} />
  failed: (error)->
    console.log error
    return <div>Book Cover Error</div>

module.exports = BookCoverContainer