React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'

DatePickerModal = require '../../Modals/DatePickerModal'

DateRangeHeaderControl = React.createClass
  displayName: 'DateRangeHeaderControl'

  getInitialState: ->
    showDatePickerModal: false
  
  closeModal: ()->
    @setState
      showDatePickerModal: false

  openModal: ()->
    @setState
      showDatePickerModal: true

  onDone: (value, view_mode)->
    # Passes the DatePickerModal values up the prop chain
    if @props.widgetCallback?
      @props.widgetCallback('calendar', value, view_mode)

  onViewModeChange: (view_mode)->
    if @props.onViewModeChange?
      @props.onViewModeChange(view_mode)

  formatDateCrumbs: (dateCrumbs)->
    crumbs = []
    i = 0
    for crumb in dateCrumbs
      i = i + 1
      for key, val of crumb
        crumbs.push <i key={key + val} className="icon icon-angle-right"></i>
        if i == dateCrumbs.length
          crumbs.push <span key={val}>{val}</span>
        else 
          crumbs.push <a key={key} onClick={@onViewModeChange.bind null, key}>{val}</a>
    return crumbs
        
  getDateCrumbs: ()->
    # TODO This is bad. crumb 1 is year, 2 is month, 3 is date. key (i.e. 'month') is the value the view_mode will be set to when clicked
    dateCrumbs = [
      year:moment.utc(@props.min_date).format('YYYY')
    ]
    
    switch @props.view_mode
      when 'day'
        dateCrumbs.push 
          month: moment.utc(@props.min_date).format('MMMM')        
        dateCrumbs.push 
          month: moment.utc(@props.min_date).format('Do')
      when 'month'
        dateCrumbs.push 
          month: moment.utc(@props.min_date).format('MMMM')
        
    return dateCrumbs
  
  render: ()->
    if not @props.disabled
      return (
        <div id="date" className="pull-left">
          {
            if @state.showDatePickerModal
              <DatePickerModal date={@props.min_date} show={@state.showDatePickerModal} onHide={@closeModal} initialViewMode={'month'} onDone={@onDone} {...@props} />  
          }
          
          <a onClick={@openModal} className="icon crumb-icon icon-jump-to-date"></a>
          
          {
            @formatDateCrumbs(@getDateCrumbs())
          }
        </div>
      )
    else
      return (
        <div id="date" className="pull-left">
          <a className="disabled icon crumb-icon icon-jump-to-date"></a>
          {
            @formatDateCrumbs(@getDateCrumbs())
          }
        </div>
      )

DateRangeHeaderControlContainer = Marty.createContainer DateRangeHeaderControl,
  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <DateRangeHeaderControl {...props} />
  pending: (fetches)->
    return <div></div>
  failed: (error)->
    console.log error
    return <div>Error</div>


module.exports = DateRangeHeaderControlContainer