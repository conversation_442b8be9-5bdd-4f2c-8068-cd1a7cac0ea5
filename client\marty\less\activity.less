/*---------------------------------------------------------------------------
  >Activity
---------------------------------------------------------------------------*/

#activity {
	
	.dropdown-header {
		padding: 0 !important;
	  border-radius: 3px 3px 0 0;
		.teal-bg;
		
		a {
			.white-txt;
			.w600;
		  padding: 7px 15px;
			display: inline-block;
			
			&:hover {
				background-color: rgba(0, 132, 158, 0.5);
			}
		}
	}

	.dropdown-menu {
	  min-width: 400px;
	  padding: 0;
	  
		ul {
			max-height: 400px;
		  overflow-y: scroll;
	  
		  li {
				.border-btm;	
				
				span {
				  display: block;
				  color: gray;
				  .f11;
				}
		  }
		  
			.alert-item {
			  font-size: 12px;
			  line-height: 16px;
			  padding: 10px 12px 10px 60px;
			 
				a {
					.w600;
					.navy-txt;
					.hover-teal-txt;
				}
			  
				.avatar {
				  position: absolute;
				  left: 12px;
				  width: 35px;
				}
			  
				.icon {
				  top: 2px;
				  margin-right: 4px;
				  .f16;
				}
			}
		}
	}
	
	.dropdown-footer {
		.border-top;
	  padding: 7px 0;
	}
}