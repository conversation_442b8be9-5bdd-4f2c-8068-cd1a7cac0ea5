React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'

QuotaWidget = React.createClass
  displayName: 'Storage Quota Widget'

  inMegabytes: (bytes)->
    if bytes?
      return parseInt(bytes/1048576)
    else
      return 0

  getQuotaUsedPercentage: ()->
    if @props.user?.data_remaining? and @props.user?.data_quota?
      if @props.user.data_quota > 0
        percentage = (parseInt(@props.user.data_quota)-parseInt(@props.user.data_remaining))/parseInt(@props.user.data_quota) * 100
        return percentage
      else
        return 0

  getMeterPercentage: ()->
    return @getQuotaUsedPercentage() + "%"

  render: ()->
    <div id="monthly_usage" className="widget">
      <div className="title">
        Monthly Quota Usage
      </div>
      <div className="progress">
        <div className="progress-bar teal-bg" style={width: @getMeterPercentage()}>
          <span className="sr-only">{@getMeterPercentage()} Complete</span>
        </div>
      </div>
      <div className="stats">
        <p><span className="box teal-bg"></span>{@inMegabytes(@props.user?.data_used)} MB used</p>
        <p><span className="box gray2-bg"></span>{@inMegabytes(@props.user?.data_remaining)} MB available</p>
        <p>Total {@inMegabytes(@props.user?.data_quota)} MB, resets on {moment().endOf('month').format('MMM D, YYYY')}</p>
      </div>
    </div>

QuotaWidgetContainer = Marty.createContainer QuotaWidget,
  listenTo: ['authStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        user: @props.app.authStore.fetchUser()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <QuotaWidget {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <QuotaWidget {...props} />
  failed: (error)->
    console.log error
    return <div>Quota Widget Error</div>

module.exports = QuotaWidgetContainer