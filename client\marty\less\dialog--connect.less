.dialog--connect {
	
	.dialog__content {
		background: #f5f5f5;

		@media @xs {padding: 2em;}
		@media @sm-x {padding: 3em;}
		
		.import__col {
			width: 100%;
		
			.btn {
				width: 100%;
				
				@media @sm-x {
					font-size: @subtitle;
			    line-height: 50px;
			    height: 50px;
		    }
				
				.icon {margin-right: 10px;}
			}
		}
	}
}

.connect-intro {
	text-align: center;
	
	.connect-intro__title {
		font-size: @title;
    font-weight: 600;
    line-height: 130%;
    margin: 0 0 10px;
	}
	
	.connect-intro__subtitle {
		font-size: @body;
		color: @black6;
	}
	
	.connect-intro__image {
		width: 100%;
		height: auto;
    margin: 1em 0 2em;
	}
}