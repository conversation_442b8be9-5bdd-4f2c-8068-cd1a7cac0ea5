Marty = require 'marty'
activationConstants = require '../constants/ActivationConstants'
google_trackConversion = require 'google_trackConversion'
fbEvents = require '../utils/fbevents'
settings = require '../settings'

ActivationActionCreators = Marty.createActionCreators
  activate: (activation_id)->
    @.app.activationHttpAPI.activate(activation_id)
    .then (response)=>
      # console.log response
      if google_trackConversion?
        google_trackConversion settings.GA_ACTIVATION_CONVERSION
        google_trackConversion settings.GA_ACTIVE_REMARKETING
      
      fbEvents.track 'PageView'
      @.dispatch activationConstants.ACTIVATED, response
      return
    .catch (error)=>
      # console.log error
      @.dispatch activationConstants.ACTIVATION_ERROR, error
      return
  
  reset: ()->
    @.dispatch activationConstants.RESET_ACTIVATION

module.exports = ActivationActionCreators
