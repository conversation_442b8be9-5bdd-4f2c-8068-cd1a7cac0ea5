React = require 'react'

LoadMore = React.createClass
  displayName: "Load More"

  getRemaining: ()->
    remaining = @props.pager.total_count - @props.loaded
    if remaining >= 10 
      return 10
    else
      return remaining

  render: ()->  
    <div id="load_more">
      <a onClick={@props.onClick} className="btn btn-gray full-width">Load {@getRemaining()} more of {@props.pager.total_count} ({@props.loaded} loaded)</a>
    </div>

module.exports = LoadMore