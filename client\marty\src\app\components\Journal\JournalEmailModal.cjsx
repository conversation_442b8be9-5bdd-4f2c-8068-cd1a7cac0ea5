React = require 'react'
Marty = require 'marty'

Modal = require('react-bootstrap').Modal
JournalEmail = require '../Journal/JournalEmail'
FileDownload = require '../Utility/FileDownload'

JournalEmailModal = React.createClass
  displayName: 'JournalEmailModal'

  getInitialState: ->
    showSuccess: false
    showDownload: false
  
  generateNew: ()->
    @props.app.emailToJRNLActionCreators.generate({journal_id: @props.object.id})
    @setState
      showSuccess: true

  renderHeader: ()->
    if @state.showSuccess
      <div className="modal-header teal-bg">
        <div className="row">
          <div className="col-sm-2">
            <a onClick={@props.onHide} className="btn-flat pull-left">
              <i className="icon icon-times"></i>
            </a>
          </div>
          <div className="col-sm-8 text-center">
            <div className="modal-title">Email Generated</div>
          </div>
        </div>
      </div>
    else
      <div className="modal-header teal-bg">
        <div className="row">
          <div className="col-sm-12 text-center">
            <div className="modal-title">Generate New Email</div>
          </div>
        </div>
      </div>

  renderBody: ()->
    if @state.showSuccess
      <div className="modal-body padding-20">
        <div className="confirm-dialogue text-center">
          <h2>The email address for<br />
            <strong>{@props.object.title}</strong><br />
            has been regenerated and is now:</h2>
          <p><strong>{@props.object.incoming_email_address}</strong></p>
          <JournalEmail {...@props} as_buttons={true} onHide={@props.onHide}/>
        </div>
      </div>
    else
      <div className="modal-body padding-20">
        <div className="confirm-dialogue text-center">
          <h2>Are you sure that you want to reset the email address for:</h2>
          <p><strong>{@props.object.title}</strong></p>
          <ul className="share-buttons">          
            <li>
              <a onClick={@props.onHide} className="TEMP-trigger-edit-journal-initial btn btn-teal btn-large share-2">No</a>
            </li>
            <li>
              <a onClick={@generateNew} className="TEMP-trigger-edit-journal-reset-confirm btn btn-navy btn-large">Yes</a>
            </li>
          </ul>
        </div>
      </div>

  render: ()->
    <Modal backdrop={'static'} onHide={@props.onHide} show={@props.show} id={"modal_edit_journal_settings"} app={@props.app}>
      {
        @renderHeader()
      }
      {
        @renderBody()
      }
    </Modal>




module.exports = JournalEmailModal