React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Link = require('react-router').Link

# TODO npm package for global namespace access
AuthenticatedComponent = require '../AuthenticatedComponent'

Drawer = require '../Sidebar/Drawer'
AllAboutMeSidebar = require './AllAboutMeSidebar'
AppHeader = require '../Header/AppHeader'
AllAboutMeHeader = require './AllAboutMeHeader'
SectionIntro = require './AllAboutMeSectionIntro'
AnswerTile = require './AnswerTile'
LoadMore = require '../Timeline/LoadMore'

# Deprecated
AllAboutMe = React.createClass
  displayName: 'AllAboutMe'

  getInitialState: ->
    pager: {
      previous: @props.page?.previous
      next: @props.page?.next
      total_count: @props.page?.total_count
      offset: @props.page?.offset
      limit: @props.page?.limit
    }
  
  onLoadMore: ()->
    if @state.pager.next?
      @.app.entriesQueries.getPage(@state.pager.next, 'AllAboutMe')

  componentWillReceiveProps: (nextProps) ->
    isEmpty = false
    if not nextProps.answers? or nextProps.answers?.length == 0 
      isEmpty = true
    
    @setState
      isEmpty: isEmpty
      pager: {
        previous: nextProps.page?.previous
        next: nextProps.page?.next
        total_count: nextProps.page?.total_count
        offset: nextProps.page?.offset
        limit: nextProps.page?.limit
      }  

  
  renderNewButton: ()->
    if @props.random_question?.id?
      return <Link to={"/all-about-me/new/" + @props.random_question.id} query={aam: @props.aamId} className="btn btn-new btn-navy pull-right">Answer a Random Question</Link>
    else
      return undefined

  renderPager: ()->
    if @state.pager?.next? and @props.answers?
      return (
        <div>
          <LoadMore pager={@state.pager} loaded={@props.answers.length} onClick={@onLoadMore}/>
        </div>
      )
    else
      return undefined

  render: ()->
    <div>
        <Drawer />
        <div id="page">
          <AppHeader view_title='All About Me' {...@props}/>
          <AllAboutMeHeader title='All Answers' newButton={@renderNewButton()} />
          <div id="content">
            <div className="container">  
              <div id="timeline-column" className="pull-left right-padding">
                {
                  if @props.answers?.length?
                    if !@props.answers.length
                      <SectionIntro {...@props}/>
                    else
                      undefined
                  else
                    undefined
                }
                {
                  if @props.answers?  
                    _.sortBy @props.answers, (answer)->
                      return -(new Date answer.entry_date)
                    .map (answer)=>
                      <AnswerTile key={answer.id} answer={answer} journalId={@props.journal?.id} {...@props} />
                  
                }
                {
                  @renderPager()
                }
              </div>
              <AllAboutMeSidebar {...@props}/>
            </div>
          </div>
        </div>
      </div>

AllAboutMeContainer = AuthenticatedComponent(Marty.createContainer AllAboutMe,
  listenTo: ['allaboutmeStore', 'entryStore']

  fetch: ()->
    return {
      answers: @.props.app.entryStore.getAnswers('AllAboutMe')
      questions: @.props.app.allaboutmeStore.getQuestions('Questions')
      username: @.props.app.authStore.getUsername()
      firstname: @.props.app.authStore.getFirstNameOrUsername()
      page: @props.app.pageStore.getPage('AllAboutMe')
      journal: @props.app.journalStore.getAllAboutMeJournal()
      random_question: @props.app.allaboutmeStore.getRandomQuestion()
      aamId: 1
    }
  done: (results)->
    props = _.extend {}, @props, results
    return <AllAboutMe {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <AllAboutMe {...props} />
  failed: (error)->
    console.log error
    return <div>AllAboutMe error</div>
)

module.exports = AllAboutMeContainer