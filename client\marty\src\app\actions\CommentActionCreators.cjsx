React = require 'react'
Marty = require 'marty'
commentConstants = require '../constants/CommentConstants'

sanitizeHtml = require '../utils/sanitizeHtml'

CommentActionCreators = Marty.createActionCreators
  sanitizeCommentContent: (comment)->
    if comment?.content?
      comment.content = sanitizeHtml comment.content
    return comment

  createComment: (newComment)->
    if newComment?
      newComment = @sanitizeCommentContent newComment
    return @.app.commentHttpAPI.saveComment(newComment)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (response)=>
      @.dispatch commentConstants.COMMENT_SAVED, response
    .catch (error)=>
      console.log error
      @.dispatch commentConstants.COMMENT_ERROR, error

  deleteComment: (comment)->
    return @.app.commentHttpAPI.deleteComment(comment)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      @.dispatch commentConstants.COMMENT_DELETE, comment
    .catch (error)=>
      console.log error
      @.dispatch commentConstants.COMMENT_ERROR, error

  updateComment: (comment)->
    if comment?
      comment = @sanitizeCommentContent comment
    return @.app.commentHttpAPI.updateComment(comment)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (response)=>
      @.dispatch commentConstants.COMMENT_UPDATE, response
    .catch (error)=>
      console.log error
      @.dispatch commentConstants.COMMENT_ERROR, error

  delete: (comment)->
    return @.app.commentHttpAPI.delete(comment)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      @.dispatch commentConstants.COMMENT_DELETE, comment
    .catch (error)=>
      console.log error
      @.dispatch commentConstants.COMMENT_ERROR, error

  update: (comment)->
    if comment?
      comment = @sanitizeCommentContent comment
    return @.app.commentHttpAPI.update(comment)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      @.dispatch commentConstants.COMMENT_UPDATE, comment
    .catch (error)=>
      console.log error
      @.dispatch commentConstants.COMMENT_ERROR, error    


module.exports = CommentActionCreators