



/**
 * React Select
 * ============
 * Created by <PERSON> and <PERSON><PERSON> for KeystoneJS, http://www.keystonejs.com/
 * https://twitter.com/jedwatson https://twitter.com/jossmackison https://twitter.com/keystonejs
 * MIT License: https://github.com/keystonejs/react-select
*/
.Select {
  position: relative;
}
.Select-control {
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  color: #333;
  cursor: default;
  outline: none;
  padding: 8px 52px 8px 10px;
}
.Select-control:hover {
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);
}
.is-searchable.is-open > .Select-control {
  cursor: text;
}
.is-open > .Select-control {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
  background: #fff;
  border-color: #b3b3b3 #ccc #d9d9d9;
}
.is-open > .Select-control > .Select-arrow {
  border-color: transparent transparent #999;
  border-width: 0 5px 5px;
}
.is-searchable.is-focused:not(.is-open) > .Select-control {
  cursor: text;
}
.is-focused:not(.is-open) > .Select-control {
  border-color: #08c #0099e6 #0099e6;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 0 5px -1px rgba(0, 136, 204, 0.5);
}
.Select-placeholder {
  color: #aaa;
  padding: 8px 52px 8px 10px;
  position: absolute;
  top: 0;
  left: 0;
  right: -15px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.has-value > .Select-control > .Select-placeholder {
  color: #333;
}
.Select-value {
  color: #aaa;
  padding: 8px 52px 8px 10px;
  position: absolute;
  top: 0;
  left: 0;
  right: -15px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.has-value > .Select-control > .Select-value {
  color: #333;
}
.Select-input > input {
  cursor: default;
  background: none transparent;
  box-shadow: none;
  height: auto;
  border: 0 none;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  padding: 0;
  outline: none;
  display: inline-block;
  -webkit-appearance: none;
}
.is-focused .Select-input > input {
  cursor: text;
}
.Select-control:not(.is-searchable) > .Select-input {
  outline: none;
}
.Select-loading {
  -webkit-animation: Select-animation-spin 400ms infinite linear;
  -o-animation: Select-animation-spin 400ms infinite linear;
  animation: Select-animation-spin 400ms infinite linear;
  width: 16px;
  height: 16px;
  box-sizing: border-box;
  border-radius: 50%;
  border: 2px solid #ccc;
  border-right-color: #333;
  display: inline-block;
  position: relative;
  margin-top: -8px;
  position: absolute;
  right: 30px;
  top: 50%;
}
.has-value > .Select-control > .Select-loading {
  right: 46px;
}
.Select-clear {
  color: #999;
  cursor: pointer;
  display: inline-block;
  font-size: 16px;
  padding: 6px 10px;
  position: absolute;
  right: 17px;
  top: 0;
}
.Select-clear:hover {
  color: #c0392b;
}
.Select-clear > span {
  font-size: 1.1em;
}
.Select-arrow-zone {
  content: " ";
  display: block;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 30px;
  cursor: pointer;
}
.Select-arrow {
  border-color: #999 transparent transparent;
  border-style: solid;
  border-width: 5px 5px 0;
  content: " ";
  display: block;
  height: 0;
  margin-top: -ceil(2.5px);
  position: absolute;
  right: 10px;
  top: 14px;
  width: 0;
  cursor: pointer;
}
.Select-menu-outer {
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
  background-color: #fff;
  border: 1px solid #ccc;
  border-top-color: #e6e6e6;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);
  box-sizing: border-box;
  margin-top: -1px;
  max-height: 200px;
  position: absolute;
  top: 100%;
  width: 100%;
  z-index: 1000;
  -webkit-overflow-scrolling: touch;
}
.Select-menu {
  max-height: 198px;
  overflow-y: auto;
}
.Select-option {
  box-sizing: border-box;
  color: #666666;
  cursor: pointer;
  display: block;
  padding: 8px 10px;
}
.Select-option:last-child {
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
}
.Select-option.is-focused {
  background-color: #f2f9fc;
  color: #333;
}
.Select-option.is-disabled {
  color: #cccccc;
  cursor: not-allowed;
}
.Select-noresults,
.Select-search-prompt,
.Select-searching {
  box-sizing: border-box;
  color: #999999;
  cursor: default;
  display: block;
  padding: 8px 10px;
}
.Select.is-multi .Select-control {
  padding: 2px 52px 2px 3px;
}
.Select.is-multi .Select-input {
  vertical-align: middle;
  border: 1px solid transparent;
  margin: 2px;
  padding: 3px 0;
}
.Select-item {
  background-color: rgba(0, 126, 255, 0.08);
  border-radius: 2px;
  border: 1px solid rgba(0, 126, 255, 0.24);
  color: #007eff;
  display: inline-block;
  font-size: 1em;
  margin: 2px;
}
.Select-item-icon,
.Select-item-label {
  display: inline-block;
  vertical-align: middle;
}
.Select-item-label {
  cursor: default;
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px;
  padding: 3px 5px;
}
.Select-item-label .Select-item-label__a {
  color: #007eff;
  cursor: pointer;
}
.Select-item-icon {
  cursor: pointer;
  border-bottom-left-radius: 2px;
  border-top-left-radius: 2px;
  border-right: 1px solid rgba(0, 126, 255, 0.24);
  padding: 2px 5px 4px;
}
.Select-item-icon:hover,
.Select-item-icon:focus {
  background-color: rgba(0, 113, 230, 0.08);
  color: #0071e6;
}
.Select-item-icon:active {
  background-color: rgba(0, 126, 255, 0.24);
}
.Select.is-multi.is-disabled .Select-item {
  background-color: #f2f2f2;
  border: 1px solid #d9d9d9;
  color: #888;
}
.Select.is-multi.is-disabled .Select-item-icon {
  cursor: not-allowed;
  border-right: 1px solid #d9d9d9;
}
.Select.is-multi.is-disabled .Select-item-icon:hover,
.Select.is-multi.is-disabled .Select-item-icon:focus,
.Select.is-multi.is-disabled .Select-item-icon:active {
  background-color: #f2f2f2;
}
@keyframes Select-animation-spin {
  to {
    transform: rotate(1turn);
  }
}
@-webkit-keyframes Select-animation-spin {
  to {
    -webkit-transform: rotate(1turn);
  }
}
body {
  color: #333;
  font-family: Helvetica Neue, Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  margin: 0;
  padding: 0;
}
a {
  color: #007eff;
  text-decoration: none;
}
a:hover {
  text-decoration: underline;
}
.container {
  margin-left: auto;
  margin-right: auto;
  max-width: 400px;
  padding: 0 30px;
}
h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  color: black;
  font-weight: 500;
  line-height: 1;
  margin-bottom: .66em;
  margin-top: 0;
}
h1,
.h1 {
  font-size: 3em;
}
h2,
.h2 {
  font-size: 2em;
  font-weight: 300;
}
h3,
.h3 {
  font-size: 1.25em;
}
h4,
.h4 {
  font-size: 1em;
}
h5,
.h5 {
  font-size: .85em;
}
h6,
.h6 {
  font-size: .75em;
}
.page-body,
.page-footer,
.page-header {
  padding: 30px 0;
}
.page-header {
  background-color: #007eff;
  color: #bfdfff;
}
.page-header h1,
.page-header h2,
.page-header h3 {
  color: white;
}
.page-header p {
  font-size: 1.2em;
  margin: 0;
}
.page-header a {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  text-decoration: none;
}
.page-header a:hover,
.page-header a:focus {
  border-bottom-color: white;
  outline: none;
  text-decoration: none;
}
.page-subheader {
  background-color: #e6f2ff;
  line-height: 20px;
  padding: 30px 0;
}
.page-subheader__button {
  float: right;
}
.page-subheader__link {
  border-bottom: 1px solid rgba(0, 126, 255, 0.3);
  outline: none;
  text-decoration: none;
}
.page-subheader__link:hover,
.page-subheader__link:focus {
  border-bottom-color: #007eff;
  outline: none;
  text-decoration: none;
}
.page-footer {
  background-color: #fafafa;
  color: #999;
  padding: 30px 0;
  text-align: center;
}
.page-footer a {
  color: black;
}
@media (min-width: 480px) {
  .page-body,
  .page-header {
    padding: 60px 0;
  }
  .page-header {
    font-size: 1.4em;
  }
  .page-subheader {
    font-size: 1.125em;
    line-height: 28px;
  }
}
.checkbox-list {
  margin-top: .5em;
  overflow: hidden;
}
.checkbox-list > .checkbox {
  clear: left;
  float: left;
  margin-top: .5em;
}
.checkbox-control {
  margin-right: .5em;
  position: relative;
  top: -1px;
}
.switcher {
  color: #999;
  cursor: default;
  font-size: 12px;
  margin: 10px 0;
  text-transform: uppercase;
}
.switcher .link {
  color: #007eff;
  cursor: pointer;
  font-weight: bold;
  margin-left: 10px;
}
.switcher .link:hover {
  text-decoration: underline;
}
.switcher .active {
  color: #666;
  font-weight: bold;
  margin-left: 10px;
}
.section {
  margin-bottom: 40px;
}
.hint {
  font-size: .85em;
  margin: 15px 0;
  color: #666;
}
/*
// include these styles to test normal form fields
.form-input {
	margin-bottom: 15px;
}
.form-input input {
	background-color: white;
	border-color: lighten(@select-input-border-color, 5%) @select-input-border-color darken(@select-input-border-color, 10%);
	border-radius: @select-input-border-radius;
	border: 1px solid @select-input-border-color;
	box-sizing: border-box;
	color: @select-text-color;
	font-size: 14px;
	outline: none;
	padding: @select-padding-vertical @select-padding-horizontal;
	transition: all 200ms ease;
	width: 100%;
	&:hover {
		box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);
	}
	&:focus {
		border-color: @select-input-border-focus lighten(@select-input-border-focus, 5%) lighten(@select-input-border-focus, 5%);
		box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1), 0 0 5px -1px fade(@select-input-border-focus,50%);
	}
}
*/