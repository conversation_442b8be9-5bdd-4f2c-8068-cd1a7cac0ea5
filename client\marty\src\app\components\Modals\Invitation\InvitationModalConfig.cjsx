constants = require './InvitationModalConstants'

types =
  GROUP:
    name: constants.CONTENT_TYPES.GROUP
    bgClass: 'color-group-bg'
    selectProps:
      multi: true
      placeholder: ''
      inputPlaceholder: 'Type email address or names'
    default_message: "I created this JRNL entry and would love your feedback on it. Please click on the following link to add comments."
  ONE:
    name: constants.CONTENT_TYPES.ONE
    bgClass: 'color-1on1-bg'
    selectProps:
      multi: false
      placeholder: 'Type an email address or name'
      inputPlaceholder: ''
    default_message: "I created this JRNL entry and am sharing it with you privately. I would love your feedback on it. Any comments added are only visible to you and me. Please click on the following link to add comments."
stages =
  INTRO:
    name: constants.CONTENT_STAGES.INTRO
    header: undefined
    showFooter: false
    previous: undefined
    next: constants.CONTENT_STAGES.INITIAL
  INITIAL:
    name: constants.CONTENT_STAGES.INITIAL
    header: undefined
    showFooter: true
    previous: constants.CONTENT_STAGES.INTRO
    next: constants.CONTENT_STAGES.CONFIRM
  CONFIRM:
    name: constants.CONTENT_STAGES.CONFIRM
    header: 'Ready to Share?'
    showFooter: true
    previous: constants.CONTENT_STAGES.INITIAL
    next: constants.CONTENT_STAGES.SEND
  SEND:
    name: constants.CONTENT_STAGES.SEND
    header: 'Sending Invitations'
    showFooter: true
    previous: constants.CONTENT_STAGES.CONFIRM
    next: undefined
    
module.exports = {
  types: types
  stages: stages
}