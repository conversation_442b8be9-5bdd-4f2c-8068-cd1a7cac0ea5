React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

# TODO npm package for global namespace access
AuthenticatedComponent = require '../AuthenticatedComponent'
Link = require('react-router').Link
Drawer = require '../Sidebar/Drawer'
AppHeader = require '../Header/AppHeader'
AllAboutMeHeader = require './AllAboutMeHeader'

LoadMore = require '../Timeline/LoadMore'

QuestionRow = React.createClass
  displayName: 'QuestionRow'

  render: ()->
    <li>
      <Link to={"/all-about-me/question/" + @props.question.id} query={aamId: 1}>
        <div className="item-icon-right">
          <span className="icon icon-angle-right"></span>
          {
            if @props.question.answered
              <span className="icon icon-check pull-right"></span>
          }
        </div>
        <h2>{@props.question.text}</h2>
      </Link>
    </li>

AllAboutMeCategoryIndex = React.createClass
  displayName: 'AllAboutMe Category Index'
  
  onLoadMore: ()->
    if @props.pager?.next?
      @.app.allaboutmeQueries.getQuestionPage(@props.pager.next, 'Questions')

  renderPager: ()->
    if @props.pager?.next? and @props.questions?
      return (
        <div>
          <LoadMore pager={@props.pager} loaded={@props.questions.length} onClick={@onLoadMore}/>
        </div>
      )
    else
      return undefined

  renderNewButton: ()->  
    if @props.random_question?.id?
      return <Link to={"/all-about-me/new/" + @props.random_question.id} query={aamId: 1} className="btn btn-new btn-navy pull-right">Answer a Random Question</Link>
    else
      return undefined

  render: ()->
    <div>
      <Drawer />
      <div id="page">
        <AppHeader view_title='All About Me' {...@props}/>
        <AllAboutMeHeader title={@props.category?.name} newButton={@renderNewButton()} />
        <div id="content">
          <div className="container">
            <div className="section-heading">
              <div className="btn-group pull-right">
                
              </div>
              <h2>{@props.category?.name}</h2>
            </div>
            <div id="subsection" className="frame">
              <ul className="list">
                {
                  if @props.questions?
                    @.props.questions.map (question)->
                      return <QuestionRow key={question.id} question={question} />
                }
              </ul>
              {
                @renderPager()
              }
            </div>
          </div>
        </div>
      </div>
    </div>

AllAboutMeCategoryIndexContainer = Marty.createContainer AllAboutMeCategoryIndex,
  listenTo: ['allaboutmeStore']

  fetch: ()->
    categoryId = @props.params.categoryId
    return {
      category: @.props.app.allaboutmeStore.getCategoryById(categoryId)
      questions: @.props.app.allaboutmeStore.getQuestionsByCategoryId(categoryId)
      pager: @props.app.pageStore.getPage('Questions')
      username: @.props.app.authStore.getUsername()
      firstname: @.props.app.authStore.getFirstNameOrUsername()
      random_question: @props.app.allaboutmeStore.getRandomQuestion()
      user: @props.app.authStore.fetchUser()
    }
  done: (results)->
    # results['pager'] = @.props.app.allaboutmeStore.getQuestionsPage()
    props = _.extend {}, @props, results
    return <AllAboutMeCategoryIndex {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <AllAboutMeCategoryIndex {...props} />
  failed: (error)->
    console.log error
    return <div>AllAboutMeCategoryIndex Error</div>

module.exports = AllAboutMeCategoryIndexContainer