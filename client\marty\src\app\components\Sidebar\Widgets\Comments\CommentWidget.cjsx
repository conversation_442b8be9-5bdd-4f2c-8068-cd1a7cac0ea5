React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

CommentTypeList = require './CommentTypeList'
CommentList = require './CommentList'

CommentWidget= React.createClass
  displayName: 'CommentWidget'

  getInitialState: ->
    type: @props.defaultType || 'list'
    channelUser: undefined
  
  onTypeChange: (type, channelUser)->
    @setState
      type: type
      channelUser: channelUser

  render: ()->
    if @state.type? and @state.type isnt 'list'
      <CommentList type={@state.type} onTypeChange={@onTypeChange} channelUser={@state.channelUser} onShareModal={@props.onShareModal} {...@props} />
    else
      <CommentTypeList type={@state.type} onTypeChange={@onTypeChange} onShareModal={@props.onShareModal} {...@props} />

CommentWidgetContainer = Marty.createContainer CommentWidget,
  listenTo: ['entryStore','commentStore','invitationStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      if @props.entry?
        if @props.user?.sharing
          return {
            sharedComments: @props.app.commentStore.getSharedCommentsForEntry(@props.entry?.id)
            ownedComments: @props.app.commentStore.getCommentsForEntry(@props.entry?.id)
            sharedEntryInvitations: @props.app.invitationStore.getSharedEntryInvitations({entry__in: @props.entry?.id})
            entrySharedInvitations: @props.app.invitationStore.getEntrySharedInvitations({entry__in: @props.entry?.id})
          }
        else
          return {
            sharedComments: []
            ownedComments: @props.app.commentStore.getCommentsForEntry(@props.entry?.id)
            sharedEntryInvitations: []
            entrySharedInvitations: []
          }
      else
        return {}
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    
    allComments = []
    if props.sharedComments?
      allComments = allComments.concat props.sharedComments

    if props.ownedComments?
      allComments = allComments.concat props.ownedComments
        
    if props.entry?.isOwnedByMe
      props['personalChannel'] = {
        comments: _.chain(allComments)
        .filter {display_channel: 'personal'}
        .sortBy (comment)->
          return new Date comment.created
        .value()
      }

    if @props.user?.sharing 
      allInvitations = props.sharedEntryInvitations.concat props.entrySharedInvitations
      public_me = @props.app.authStore.getMyPublicUser()
      me = @props.app.authStore.getUserURI()

      members = []
      if @props.entry?.isOwnedByMe
        owner = public_me
      else if @props.entry?
        owner = @props.entry?.user
      else
        owner = undefined

      if @props.entry?
        members = _.union @props.entry.shared_group_users, @props.entry.shared_group_non_users

      props['groupChannel'] = {
        owner: owner
        members: members
        comments: _.chain(allComments)
        .filter {display_channel: 'group'}
        .sortBy (comment)->
          return new Date comment.created
        .value()
        invitations: _.filter allInvitations, {display_channel: 'group'}
      }

      shared_channels = _.chain(props['sharedEntryInvitations'])
      .filter {display_channel: 'one_on_one'}
      .indexBy 'shared_user'
      .omit 'null'
      .value()
      
      owned_channels = _.chain(props['entrySharedInvitations'])
      .filter {display_channel: 'one_on_one'}
      .indexBy 'sharing_user'
      .omit 'null'
      .value()
      
      channels = _.extend shared_channels, owned_channels

      for channel in _.keys(channels)
        channels[channel].comments = allComments.map (comment)=>
          if comment.display_channel is 'one_on_one'
            if (comment.one_on_one_user is channel and comment.user is me) or (comment.one_on_one_user is me and comment.user is channel)
              return comment
            else if (comment.one_on_one_user is channel and comment.user is public_me) or (comment.one_on_one_user is public_me and comment.user is channel)
              return comment
        channels[channel].comments = _.compact channels[channel].comments
        channels[channel].comments = _.sortBy channels[channel].comments, (comment)->
          return new Date comment.created

      props['oneOnOneChannel'] = {
        owner: owner
        channels: channels
        invitations: _.filter allInvitations, {display_channel: 'one_on_one'}
      }
    
    return <CommentWidget {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    if props.entry?.isOwnedByMe
      props['personalChannel'] = {}
    return <CommentWidget {...props} />
  failed: (error)->
    console.log error
    return <div>CommentWidget Error</div>

module.exports = CommentWidgetContainer