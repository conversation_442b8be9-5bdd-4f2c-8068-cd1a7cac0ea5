/*---------------------------------------------------------------------------
  >ERROR
---------------------------------------------------------------------------*/

#error {
	background-image: url('../images/chevrons.png');
	background-color: @teal;
	background-attachment: fixed;
	background-position: center center;
    	
	.logo {
		box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		margin: auto;
		background: white;
    padding: 1em 2.2em 1.3em 1.7em;
		border-radius: 0 0 5px 5px;
		width: 200px;
		height: auto;
	}
	
	.error-wrapper {
		
		.table-wrapper;

		.box {
			vertical-align: middle;
			text-align: center;
	    padding: 6em 2em;
			.table-cell;
			
			h2, p {
				text-shadow: 0 1px 5px rgba(0, 0, 0, 0.5);
				color: white;
			}

			h2 {
				margin: 0 0 .5em;
				.font2;
				@media @xs {font-size: 2em;}
				@media @sm-x {font-size: 3em;}
			}
			
			p {
				margin: 0 0 1.2em;
				@media @xs {font-size: 1em;}
				@media @sm-x {font-size: 1.5em;}
			}
		}	
	}
	
	.debug {
    border-top: 1px solid rgba(0,0,0,0.15);
    color: rgba(255, 255, 255, 0.7);
    background: rgba(0,0,0,0.2);
    padding: .1em .4em;
		position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    width: 100%;
    height: 25px;
    font-size: .8em;
    line-height: 2em;
    text-align: center;
    overflow: hidden;
	}
}