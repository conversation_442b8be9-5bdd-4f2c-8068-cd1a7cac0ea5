/*---------------------------------------------------------------------------
  >Framework
---------------------------------------------------------------------------*/

body {
	background: url('/static/images/backgrounds/patterns/pattern-001.png') #E1E1E1; //fallback
	height: 100%;
	color: @gray0;
	.font1;
}

.table-wrapper {
	display: table;
	table-layout: fixed;
	width: 100%;
	height: 100%;
}

.table-row {display: table-row;}

.table-cell {
	height: 100%;
	display: table-cell;
  float: none !important;
}

html,
body, 
#stage,  
#jrnl-app, 
#stage > div {
	.table-wrapper
}

#drawer, #page {
	.table-cell;
}

a:hover {.teal2-txt;}

ul {
	margin: 0;
	padding: 0;
}

li {list-style: none;}

#page {
  background-size: 110px;
  overflow: hidden;
}

.text-center {text-align: center;}
.text-right {text-align: right;}

.fa-spin {
  filter: alpha(opacity=40);
  -moz-opacity: .4;
  opacity: .4;
}

.nav>li>a:focus, .nav>li>a:hover {
	background: inherit;
}

.table>thead>tr>th {border: none;}

#default-sidebar {
	@media @xs {width: 100%;}
	
	.date-picker {
    border-radius: 3px 3px 0 0;
	}
}

/************** >Section Heading **************/

.section-heading {
  margin: 10px 0 15px;
  padding: 12px 15px;
	display: table;
  width: 100%;
  .white-bg;
	.radius3;
  .shadow;

	.divider .icon {
	  font-size: 18px;
	  color: @gray8;
	  top: -2px;
	  padding-right: 2px;
	}

	img {
	  margin-right: 6px;
	  width: 32px;
	  top: -2px;
	}  

	h2 {
	  margin: 0;
	  z-index: 0;
	  display: inline-block;
	  .f25;
	  .font2;
	  .navy-txt;
	  .w400;
	}
	
	.form-control {
		margin-right: 10px;
		width: initial;
		z-index: 1;
	}

	.btn {
    z-index: 5;
    top: 1px;
    margin-left: 10px;
    padding: 5px 10px;
	  .f12;
	  
		.icon {
		  height: 12px;
		  display: inline-block;
		  .f14;
		}
	}
}

.hidden-sm {
  @media @ss-md {display: none;}
}

/*---------------------------------------------------------------------------
  >Queries
---------------------------------------------------------------------------*/


#content {

	@media @xs {
		padding-top: 15px;
	  padding-bottom: 15px;
	}
	@media @sm{
		padding-top: 20px;
	  padding-bottom: 30px;
	}
	@media @md-x {
		padding-top: 20px;
	  padding-bottom: 40px;
	}
}

.container {
	
	@media @xs {width: 100%;}
	@media @sm {width: 710px;}
	@media @md {width: 800px;}
	@media @lg {width: 985px;}
	@media @hg-x {width: 1175px;}
}

#crumb-bar, #timeline-column {

	@media @xs {width: 100%;}
	@media @sm {width: 450px; padding-right: 15px;}
	@media @md {width: 540px;}
	@media @md-x {padding-right: 20px;}
	@media @lg {width: 655px;}
	@media @hg-x {width: 844px;}
}

#tools, #default-sidebar, #drawer-comments-header, #drawer_comments {
	
	@media @xs {width: 100%;}
	@media @sm {width: 230px;}
	@media @md {width: 230px;}
	@media @lg-x {width: 300px;}
}