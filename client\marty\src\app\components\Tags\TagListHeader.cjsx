React = require 'react'
Link = require('react-router').Link
Sticky = require 'react-sticky'

DateRangeHeaderControl = require '../Header/Controls/DateRangeHeaderControl'
DateRangePagerControl = require '../Header/Controls/DateRangePagerControl'
EntryListSortControl = require '../Header/Controls/EntryListSortControl'

TagListHeader = React.createClass
  displayName: 'TagListHeader'
  STICKY_STYLES: {}

  renderNewEntry: ()->
    if @props.tag?
      return (
        <Link to={"/entry/create/"} query={tag_id: @props.tagId} className="btn btn-new pull-right btn-navy">New Entry</Link>
      )
      return undefined
    else
      return undefined
      
  render: ()->
    <Sticky stickyStyle={@STICKY_STYLES} stickyClass={'nav-sticky'} topOffset={-60}>
      <div id="nav_crumb" className="gray">
        <div className="container">
          <div id="crumb-bar" className="pull-left">
            <DateRangeHeaderControl calendar_id={'Tag'+@props.tagId} widgetCallback={@props.widgetCallback} {...@props}/>
            <div id="options" className="pull-right">
              <DateRangePagerControl {...@props} />
              <EntryListSortControl order_by={@props.order_by} onEntryViewModeChange={@props.onEntryViewModeChange} entry_view_mode={@props.entry_view_mode} user={@props.user} disableEntryViewMode={true}/>
            </div>
          </div>
          <div id="tools" className="pull-right text-right">
            {
              @renderNewEntry()
            }
          </div>
        </div>
      </div>
    </Sticky>

module.exports = TagListHeader