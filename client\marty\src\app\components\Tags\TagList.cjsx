React = require 'react'
Marty = require 'marty'
moment = require 'moment'
_ = require 'underscore'

# TODO npm package for global namespace access
AuthenticatedComponent = require '../AuthenticatedComponent'

Drawer = require '../Sidebar/Drawer'
Sidebar = require '../Sidebar/Sidebar'
AppHeader = require '../Header/AppHeader'

TagListHeader = require './TagListHeader'
EntryTile = require '../Entry/EntryTile'
AnswerTile = require '../AllAboutMe/AnswerTile'
EmptyTile = require '../Timeline/EmptyTile'
LoadMore = require '../Timeline/LoadMore'
NewEntryListButton = require '../Timeline/NewEntryListButton'

WelcomeModal = require '../Modals/WelcomeModal'

TagList = React.createClass
    displayName:"TagList Handler"
    mixin: [Marty.createAppMixin()]

    getInitialState: ->
      isEmpty: false
      pager: {
        previous: @props.page?.previous
        next: @props.page?.next
        total_count: @props.page?.total_count
        offset: @props.page?.offset
        limit: @props.page?.limit
      }
      showWelcomeModal: false

    componentDidMount: ()->
      @app.listStore.setEntryViewMode('just_me', "Tag" + @props.tagId)
      if @state.pager?
        if parseInt(@state.pager.total_count) == 0
          @setState
            isEmpty: true
    
    componentWillReceiveProps: (nextProps) ->
      isEmptyForReal = false
      if @props.entries? and nextProps.entries?
        if !_.isEmpty(@props.entries) and !_.isEmpty(nextProps.entries)
          if @props.entries[0].id == "EMPTY" and nextProps.entries[0].id == "EMPTY"
            isEmptyForReal = true
          else
            isEmptyForReal = false
        else
          isEmptyForReal = false

      if nextProps.page?
        if parseInt(nextProps.page.total_count) == 0
          isEmptyForReal = true

      if nextProps.user?.has_seen_welcome_web?
        if nextProps.user.has_seen_welcome_web
          @setState
            showWelcomeModal: false
        else
          @setState
            showWelcomeModal: true
      
      @setState
        isEmpty: isEmptyForReal
        pager: {
          previous: nextProps.page?.previous
          next: nextProps.page?.next
          total_count: nextProps.page?.total_count
          offset: nextProps.page?.offset
          limit: nextProps.page?.limit
        }
    
    onLoadMore: ()->
      if @state.pager.next?
        @.app.entriesQueries.getPage(@state.pager.next, 'Tag' + @props.tag.id)
               
    onWidgetCallback: (widget, value, view_mode)->
      if widget == 'calendar'
        if not view_mode?
          view_mode = 'day'
        options = @buildOptions(value, view_mode, 'just_me')
        if @props.tag?
          options['tags__id'] = @props.tag.id
        @.app.entryActionCreators.updateEntries(options, 'Tag' + @props.tag.id)
        @.app.calendarActionCreators.lockWidget()

    buildOptions: (value, view_mode, entry_view_mode)->
      filter_by = @.app.entryStore.getFilterBy()
      options = 
        order_by: @props.order_by
        min_date: moment(value).startOf(view_mode).format('YYYY-MM-DD'+'T'+'00:00:00')
        max_date: moment(value).endOf(view_mode).format('YYYY-MM-DD'+'T'+'23:59:59')
        filter_by: filter_by
        view_mode: view_mode
        tags__id: @props.tag?.id
        entry_view_mode: entry_view_mode

    onViewModeChange: (view_mode)->
      switch view_mode
        when 'day'
          @.app.entryActionCreators.updateViewMode({view_mode: view_mode}, 'Tag' + @props.tag.id)
        when 'month','year'
          options = @buildOptions @props.min_date, view_mode, @props.entry_view_mode    
          @.app.entryActionCreators.updateEntries(options, 'Tag' + @props.tag.id)

    onEntryViewModeChange: (entry_view_mode, id)->
      options = @buildOptions @props.min_date, @props.view_mode, entry_view_mode
      @app.entryActionCreators.updateEntries(options, 'Tag' + @props.tag.id)
      calendarOptions = {utc_offset: moment().format('Z'), tags__id: @props.tag.id}
      @app.calendarQueries.getCalendar(calendarOptions, 'Tag' + @props.tag.id)

    openWelcomeModal: ()->
      @setState
        showWelcomeModal: true

    closeWelcomeModal: ()->
      @app.userActionCreators.updateWelcomeStatus(true)
      @setState
        showWelcomeModal: false

    renderEmpty: ()->
      if @state.isEmpty
        return (
          <EmptyTile {...@props} />
        )
      else
        return undefined

    renderPager: ()->
      if @state.pager?.next? and @props.entries?
        return (
          <div>
            <LoadMore pager={@state.pager} loaded={@props.entries.length} onClick={@onLoadMore}/>
          </div>
        )
      else
        return undefined

    renderEntriesOrEmpty: (entries)->
      if entries?
        addButton = false
        output = entries.map (entry)=>               
          switch entry.entry_type
            when "QA"
              addButton = true
              return (
                <AnswerTile {...@props} key={entry.id} answer={entry} />
              )
            when "Default"
              addButton = true
              return (
                <EntryTile {...@props} key={entry.id} entry={entry} />
              )
            when ""
              addButton = true
              return (
                <EntryTile {...@props} key={entry.id} entry={entry} />
              )
            when "EMPTY"
              return @renderEmpty()
        
        if addButton and @props.view_mode == 'day'
          output.unshift <NewEntryListButton {...@props} />
        
        return output
      else
        return undefined

    render: ()->      
      entries = @props.entries
      
      <div>
        <WelcomeModal show={@state.showWelcomeModal} onHide=(@closeWelcomeModal) {...@props} />
        <Drawer />
        <div id="page">
          <AppHeader view_title={<span className="title-wrapper"><i className="icon icon-tag"></i>{@props.tag?.name}</span>} {...@props}/>
          <TagListHeader onEntryViewModeChange={@onEntryViewModeChange} onViewModeChange={@onViewModeChange} widgetCallback={@onWidgetCallback} view_id='Tag' {...@props}/>
          <div id="content">
            <div className="container">
              <div id="timeline-column" className="pull-left right-padding">
                {
                  @renderEntriesOrEmpty(entries)
                }  
                {
                  @renderPager()
                }
              </div>
              <Sidebar widgetCallback={@onWidgetCallback} {...@props}/>
            </div>
          </div>
        </div>
      </div>

TagListContainer = AuthenticatedComponent(
  Marty.createContainer TagList,
    listenTo: ['authStore', 'entryStore','tagsStore','calendarStore']

    fetch: ()->
      if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
        tagId = @props.params.tagId  
        options = {tags__id: tagId}
        return {
          tagId: tagId
          tag: @props.app.tagsStore.getTagById(tagId)
          entries: @props.app.entryStore.getEntriesByTag(tagId, 'Tag' + tagId)
          order_by: @props.app.entryStore.getOrderBy()
          min_date: @props.app.listStore.getMinDate(options, 'Tag'+tagId)
          max_date: @props.app.listStore.getMaxDate(options, 'Tag'+tagId)
          username: @props.app.authStore.getUsername()
          firstname: @.props.app.authStore.getFirstNameOrUsername()
          view_mode: @props.app.listStore.getDateViewMode('Tag'+tagId)
          entry_view_mode: @props.app.listStore.getEntryViewMode('Tag'+tagId)
          page: @props.app.pageStore.getPage('Tag' + tagId)
          user: @props.app.authStore.fetchUser()
        } 
      else
        return {}
    done: (results)->
      props = _.extend({}, @props, results)
      return <TagList {...props}/>
    pending: (fetches)->
      props = _.extend({}, @props, fetches)
      return <TagList {...props}/>
    failed: (error)->
      console.log error
      return <div>Error</div>
)

module.exports = TagListContainer
