React = require 'react'
Marty = require 'marty'
Link = require('react-router').Link
Sticky = require 'react-sticky'

BookbuilderHeader = React.createClass
  displayName: 'Bookbuilder Header'
  STICKY_STYLES: {}

  onDeleteSelected: ()->
    if @props.onDeleteSelected?
      @props.onDeleteSelected()

  renderViewOrders: ()->
    if @props.hasOrders
      return <Link to='/orders/' className="btn btn-gray btn-small">View Orders</Link>
    else
      return <Link to='/orders/' className="btn btn-gray btn-small disabled">View Orders</Link>

  renderDeleteSelected: ()->
    if @props.hasSelected
      return <a className='btn btn-gray btn-small' onClick={@onDeleteSelected} >Delete Selected</a>
    else
      return <a className='btn btn-gray btn-small disabled' onClick={@onDeleteSelected} >Delete Selected</a>

  render: ()->
    <div id="nav_crumb" className="gray">
      <div className="container">
        <div id="crumb-bar" className="pull-left">
          <div id="date" className="pull-left">
            {
              @renderViewOrders()
            }
            {
              @renderDeleteSelected()
            }
          </div>
        </div>
        <div id="tools" className="pull-right text-right">
          <a onClick={@props.onNewBook} className="btn btn-new btn-navy pull-right">New Book</a>
        </div>
      </div>
    </div>
    

module.exports = BookbuilderHeader