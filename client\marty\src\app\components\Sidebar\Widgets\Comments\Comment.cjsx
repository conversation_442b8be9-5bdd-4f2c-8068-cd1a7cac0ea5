React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'
FormData = require 'react-form-data'
CommentHeader = require './CommentHeader'

Comment = React.createClass
  displayName: 'Comment'
  mixins:[FormData]

  getInitialState: ->
    isEditable: false
    content: undefined

  componentWillReceiveProps: (nextProps) ->
    if nextProps.editableComment? and nextProps.editableComment != @props.comment.id
      @setState
        isEditable: false
  
  onDelete: (comment)->
    @.props.app.commentActionCreators.delete(comment)

  onEdit: (comment)->
    @setState
      isEditable: true
      content: @props.comment.content.replace(/<br\s*[\/]?>/gi,"\n")
    if @props.onToggleEdit?
      @props.onToggleEdit(@props.comment.id)

  onTogglePublish: (comment)->
    comment = _.extend comment, {publishable: !comment.publishable}
    @props.app.commentActionCreators.update(comment)

  onChange: (e)->
    @updateFormData
    @setState
      content: e.target.value

  onCancel: ()->
    @setState
      isEditable: false
      content: undefined
    if @props.onToggleEdit?
      @props.onToggleEdit(undefined)

  onSaveComment: ()->
    content = undefined
    if @formData.content?
      if @formData.content.length > 0
        content = @formData.content.replace(/\r?\n/g, '<br />')

    comment = 
      id: @props.comment.id
      content: content
    
    @.props.app.commentActionCreators.updateComment(comment)
    @setState
      isEditable: false
    if @props.onToggleEdit?
      @props.onToggleEdit(undefined)

  renderEditableComment: ()->
    <div key={"edit-comment-#{@props.comment.id}"} className="edit-comment-box" onChange={@updateFormData}>
      <textarea name='content' rows="1" placeholder="Comment on this entry" value={ if @state.content? then @state.content.replace(/<br\s*[\/]?>/gi,"\n") else @props.comment.content.replace('<br />',"\n") } onChange={@onChange}></textarea>
      <a onClick={@onSaveComment} className="btn btn-navy btn-medium fr">Save</a>
      <a onClick={@onCancel} className="btn btn-gray btn-medium fr">Cancel</a>
    </div>

  renderContent: ()->
    if @state.isEditable
      return @renderEditableComment()
    else
      if @props.comment.content?
        content = @props.comment.content.replace(/\r?\n/g, '<br />')
        return (
          <div dangerouslySetInnerHTML={{__html: content}}>
          </div>
        )
      else
        return undefined

  render: ()->
    if not moment.utc(@props.comment?.created).isSame(moment.utc(@props.comment?.modified), 'seconds')
      edited = true
    <div className="comment">
      <CommentHeader entry={@props.entry} comment={@props.comment} app={@props.app} onEdit={@onEdit} onDelete={@onDelete} onTogglePublish={@onTogglePublish} isEditable={@state.isEditable} />
      {
        @renderContent()
      }
    </div>

CommentContainer = Marty.createContainer Comment,
  listenTo: ['entryStore','commentStore']

  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Comment {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Comment {...props} />
  failed: (error)->
    console.log error
    return <div>Comment Error</div>

module.exports = CommentContainer