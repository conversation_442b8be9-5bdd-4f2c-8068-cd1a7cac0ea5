React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'

ReminderModal = require '../Modals/ReminderModal'
AuthenticatedComponent = require '../AuthenticatedComponent'

ReminderSettings = React.createClass
  displayName: 'Reminder Settings'

  getInitialState: ()->
    showReminderModal: false
    success: @props.success

  componentWillReceiveProps: (nextProps) ->
    if nextProps.success != @state.success and nextProps.success
      @setState
        showReminderModal: false

  closeReminderModal: ()->
    @setState
      showReminderModal: false

  openReminderModal: ()->
    @setState
      showReminderModal: true
  
  formatTime: ()->
    if @props.reminder_profile?.time?
      return moment.utc(@props.reminder_profile.time, "HH:mm:ss").local().format("h:mm a")
    return undefined

  formatCustomMessage: ()->
    if @props.reminder_profile?.custom_message?
      formattedCustomMessage = @props.reminder_profile.custom_message.replace /\n\r?/g, '<br />'
      return formattedCustomMessage
    return undefined

  renderFrequency: ()->
    if @props.reminder_profile?.frequency?
      switch @props.reminder_profile.frequency
        when 7
          return @renderOncePerWeek()
        when 2
          return <span className="f12 gray5-txt">Every other day at {@formatTime()}<br /></span>
        when 1
          return <span className="f12 gray5-txt">Every day at {@formatTime()}<br /></span>
    return undefined
  
  renderEnabledStatus: ()->
    if @props.reminder_profile?.enabled?
      if @props.reminder_profile.enabled
        return <div className="alert success text-center">Reminders are enabled</div>
      else
        return <div className="alert warning text-center">Reminders are not enabled</div>
    return undefined

  renderOncePerWeek: ()->
    if @props.reminder_profile?.frequency?
      if @props.reminder_profile.frequency == 7
        day_of_the_week = @props.reminder_profile.day_of_the_week
        formatted_day_of_the_week = day_of_the_week.charAt(0).toUpperCase() + day_of_the_week.slice(1)
        return <span className="f12 gray5-txt">Once per week on {formatted_day_of_the_week} at {@formatTime()}<br /></span>
    return undefined

  render: ()->
    <div role="tabpanel" className="tab-pane" id="reminders-settings">
      
        <div className="settings-wrapper">
          <h2 className="section-title">Reminder Settings</h2>
          <div className="panel panel-default">
            <div className="panel-body">
              <div className="card-name">
                {
                  @renderEnabledStatus()
                }
                <strong>Email to: {@props.user?.email}</strong><br />
                {
                  @renderFrequency()
                }
                <span className="f12 gray5-txt" dangerouslySetInnerHTML={{__html: @formatCustomMessage()}}></span>
              </div>
            </div>
          </div>
          <hr />
          <div className="row">
            <div className="col-sm-12">
              <ReminderModal show={@state.showReminderModal} onHide={@closeReminderModal} {...@props} />
              <a onClick={@openReminderModal} className="btn btn-navy btn-med">Update Reminder Settings</a>
            </div>
          </div>
        </div>
      
    </div>

ReminderSettingsContainer = AuthenticatedComponent(Marty.createContainer ReminderSettings,
  listenTo: ['reminderStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        reminder_profile: @props.app.reminderStore.getReminderProfile()
        success: @props.app.reminderStore.getSuccess()
      }
    else 
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <ReminderSettings {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <ReminderSettings {...props}/>
  failed: (error)->
    console.log error
    return <div>Reminder Settings Error</div>
)

module.exports = ReminderSettingsContainer