/*---------------------------------------------------------------------------
  >PROFILE CARD
---------------------------------------------------------------------------*/

#profile_card {
	
	.avatar {
		position: relative;
		border-radius: 2px 2px 0 0;
		text-align: center;
		padding: 2em 0 0;
    overflow: hidden;
		
		img {
	    //border: 5px solid white;
	    //margin-bottom: 2em;
			height: 150px;
			width: auto;
			z-index: 1;
			
			@media @sm {height: 120px;}
		}
		
		.name {
			font-size: large;
			text-align: center;
	    padding: .8em 1em;
	    font-weight: 600;
	    z-index: 1;
	    color: @navy;
	    .truncate;
	    
	    @media @sm {font-size: medium;}
		}
		
		/*.bg-avatar {
			background-position: center center;
		  filter: 			  blur(15px);
		  -webkit-filter: blur(15px);
		  -moz-filter: 		blur(15px);
		  -o-filter:		  blur(15px);
		  -ms-filter: 		blur(15px);
      transform: scale(1.2);
			overflow: hidden;
			width: 100%;
			height: 100%;
			position: absolute;
	    top: 0;
	    left: 0;
			z-index: 0;
		}*/
	}
	
	.info {
		border-top: 1px solid @gray9;
		padding: .5em 0;
		.gradient-g-w;
		.radius-bottom;
		
		.table-row {
			
			div {
				padding: .5em 1em;
				.table-cell;
				
				&:first-child {
					text-transform: uppercase; 
					font-size: smaller;
					color: @gray5;
					width: 90px;
					
					@media @sm {font-size: x-small;}
				}
				
				&:last-child {
					.truncate;
					padding-left: 0;
					text-align: right;
					font-size: small;
				}
			}
		}
	}
}