React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

InstagramImport = React.createClass
  displayName: 'InstagramImport'

  render: ()->
    <div>INSTAGRAMIMPORT</div>

InstagramImportContainer = Marty.createContainer InstagramImport,
  listenTo: ['authStore','socialStore']
  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        instagram_authorization_code: @app.socialStore.fetchInstagramCode()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <InstagramImport {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <InstagramImport {...props} />
  failed: (errors)->
    console.log errors
    return <div>InstagramImport Error</div>

module.exports = InstagramImportContainer