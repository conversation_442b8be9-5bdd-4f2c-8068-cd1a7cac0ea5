Marty = require 'marty'
userConstants = require '../constants/UserConstants'
authConstants = require '../constants/AuthConstants'

UserActionCreators = Marty.createActionCreators
  id: 'UserActionCreators'

  updateWelcomeStatus: (has_seen_welcome_web)->
    @app.authHttpAPI.updateUser({has_seen_welcome_web: has_seen_welcome_web})
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @dispatch authConstants.USER_RECEIVED, success
    .catch (error)=>
      console.log error
      @dispatch authConstants.USER_ERROR, has_seen_welcome_web

  updateCurrentAvatar: (avatar)->
    @app.authHttpAPI.updateUser({avatar_image: avatar.resource_uri})
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @dispatch authConstants.USER_RECEIVED, success
    .catch (error)=>
      console.log error
      @dispatch authConstants.USER_ERROR, error

  removeCurrentAvatar: (avatar)->
    @app.authHttpAPI.updateUser({avatar_image: null})
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @dispatch authConstants.USER_RECEIVED, success
    .catch (error)=>
      console.log error
      @dispatch authConstants.USER_ERROR, error

  
  updateWallpaper: (wallpaper)->
    return @app.authHttpAPI.updateUser({wallpaper_image: wallpaper.resource_uri})
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @dispatch authConstants.USER_RECEIVED, success
    .catch (error)=>
      console.log error
      @dispatch authConstants.USER_ERROR, error

  removeWallpaper: (avatar)->
    @app.authHttpAPI.updateUser({wallpaper_image: null})
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @dispatch authConstants.USER_RECEIVED, success
    .catch (error)=>
      console.log error
      @dispatch authConstants.USER_ERROR, error

module.exports = UserActionCreators