React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
FormData = require 'react-form-data'

SecuritySettings = React.createClass
  displayName: 'SecuritySettings'
  mixins: [FormData]

  getInitialState: ->
    showError: false
    success: @props.success
    error: @props.error
  
  componentWillReceiveProps: (nextProps) ->
    showError = undefined
    error = undefined
    success= undefined
    
    if nextProps.success
      success = true
    else
      success = false
    
    if nextProps.error != undefined and nextProps.error != {}
      showError = true
      error = nextProps.error

    @setState
      showError: showError
      error: error
      success: success
  
  onSaveNewPassword: (e)->
    e.stopPropagation()
    e.preventDefault()
    if not _.isEmpty(@formData.new_password) and not _.isEmpty(@formData.new_password1)
      if @formData.new_password == @formData.new_password1
        options = 
          raw_password: @formData.new_password
        @props.app.authActionCreators.updatePassword(options)
      else
        @setState
          showError: true
          error:
            user_message: 'Passwords do not match'
    else
      @setState
        showError: true
        error:
            user_message: 'All fields required'
    
  formatFieldErrorMessage: (field, message)->
    return <div className="alert danger text-center">{message}</div>

  formatError: ()->
    return (
      <div className="col-sm-12">
        <div className="alert danger text-center">{@state.error?.user_message}</div>
      </div>
    )

  renderFieldError: (field)->
    if @state.showError
      if @state.error?
        if @state.error.validation_errors[field]?
          messages = @state.error.validation_errors[field]
          output = []
          for message in messages
            output.push @formatFieldErrorMessage(field, message)
          return output
    return undefined

  renderError: ()->
    if @state.showError
      if @state.error?
        return @formatError()
    return undefined

  removeError: ()->
    @setState
      showError: false
      error: undefined
      success: undefined

  renderSuccess: ()->
    if @state.success
      return (
        <div className="col-sm-12">
          <div className="alert success text-center">Password Updated</div>
        </div>   
      )

  render: ()->
    <div role="tabpanel" className="tab-pane" id="security-settings">
      <div className="settings-wrapper" onChange={@updateFormData}>
        <h2 className="section-title">Password</h2>
        <div className="row">
          {
            @renderError()
          }
          {
            @renderSuccess()
          }
          <div className="col-sm-12 col-lg-12">
            <div className="form-group">
              <label>New Password</label>
              <input name="new_password" type="password" className="form-control" placeholder="Enter new password" onClick={@removeError}/>
            </div>
          </div>
          <div className="col-sm-12 col-lg-12">
            <div className="form-group">
              <label>Repeat New Password</label>
              <input name="new_password1" type="password" className="form-control" placeholder="Repeat new password" onClick={@removeError}/>
            </div>
          </div>
        </div>  
        <div className="row">
          <div className="col-sm-12">
            <hr/>
            <a onClick={@onSaveNewPassword}className="btn btn-navy btn-med">Save</a>
          </div>
        </div>
      </div>
    </div>

SecuritySettingsContainer = Marty.createContainer SecuritySettings,
  listenTo: ['authStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        success: @props.app.authStore.getPasswordUpdateSuccess()
        error: @props.app.authStore.getError()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <SecuritySettings {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <SecuritySettings {...props}/>
  failed: (error)->
    console.log error
    return <div>Security Settings Error</div>

module.exports = SecuritySettingsContainer