React = require 'react'
_ = require 'underscore'
Marty = require 'marty'

Select = require 'react-select-qi'
InvitationSelectValue = require './InvitationSelectValue'

InvitationSelect = React.createClass
  displayName: 'InvitationSelect'
  
  getInitialState: ->
    isLoading: false
    selectValue: []
    component_validation_error: undefined
    errorsCleared: false

  # componentDidMount: ->
  #   console.log "#{@props.selectId} mounted"

  # componentWillUnmount: ->
  #   console.log "#{@props.selectId} unmounted"    
   

  getDefaultProps: ->
    multi: true
    placeholder: ''
    inputPlaceholder: 'Type email address or names'

  componentWillReceiveProps: (nextProps) ->
    if nextProps.component_validation_error?
      if @state.errorsCleared is false
        @setState
          component_validation_error: nextProps.component_validation_error
      else
        if (_.findWhere nextProps.component_validation_error, {isFormInvalid: true})?
          console.log nextProps.component_validation_error
          @setState
            component_validation_error: nextProps.component_validation_error
            errorsCleared: false
    else
      @setState
        component_validation_error: undefined
    


  clearErrors: ()->
    if @state.errorsCleared is false and @state.component_validation_error?
      @setState
        component_validation_error: undefined
        errorsCleared: true
    return
    
  onChange: (newValue, selectedValues)->
    if @props.onChange?
      @setState
        errorsCleared: false
      ,@props.onChange(newValue, selectedValues, @props.selectId)

  onClick: (e)->
    @refs.select.focus()
    @clearErrors()
    
  renderComponentValidationErrors: ()->
    errorDisplayed = false
    if @state.component_validation_error? and not _.isEmpty @state.component_validation_error
      @state.component_validation_error.map (error)=>
        if error.selectId is @props.selectId
          return <div key={error.error} className="alert warning text-center">{error.error}</div>
        else if error.isFormInvalid and not errorDisplayed 
          errorDisplayed = true
          return <div key={error.error} className="alert danger text-center">{error.error}</div>
  
  render: ()-> 
    <div className="invitation_box" onClick={@onClick}>
      <Select 
        ref="select"
        multi={@props.multi}
        allowCreate={true}
        placeholder={@props.placeholder}
        inputProps={placeholder: @props.inputPlaceholder}
        isLoading={@state.isLoading}
        options={@props.options}
        onChange={@onChange}
        value={@props.value}
        valueKey={'value'}
        valueComponent={InvitationSelectValue}
        filterOptions={@props.filterOptions}
      />
      {
        @renderComponentValidationErrors()
      }
    </div>

module.exports = InvitationSelect