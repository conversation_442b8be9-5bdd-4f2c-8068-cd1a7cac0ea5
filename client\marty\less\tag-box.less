/*---------------------------------------------------------------------------
  >Tag Box
---------------------------------------------------------------------------*/

#tag_box, .Select-control {min-height: 60px;}

#tag_box {
	width: 100%;
	display: block;
	border-top: 1px solid @gray9;

  .gradient-g-w;
  .f13;
  
  .tag-wrapper {padding: 10px 12px;}
  
  .add-tag {
    padding: 4px 10px;
    margin: 3px;
    display: inline-block;
    border-radius: 3px;
    border: 1px solid transparent;
    top: 1px;
	  .w600;
	  .white-txt;
	  .radius4;
	  .teal-bg;
	  
	  &:hover {.teal0-bg;}
	}
	
	.tag-styles {
	  margin: 3px;
		display: inline-block;
		text-align: center;
		border: 1px solid #CFCFD4;
		color: #565861;
		.gradient-w-g;
		.noselect;
		.radius4;
		.font1;
		.w600;
	}
	.tag {
	  padding: 4px 10px 5px;
		.tag-styles;
	}
	.Select-item {
		.tag-styles;
		.trans-quick;
		
		.icon-times-circle {
	    opacity: .2;
	    color: black;
	    padding: 8px 6px 8px 6px;
	    margin-right: -10px;
	    position: relative;
	    z-index: 1;
			
			&:hover {
				opacity: 1;
				cursor: pointer;
				color: white;
			}
		}		
	}
	.Select-item-label {
	  padding: 4px 10px 5px;
	}
	
	/**
	 * React Select
	 * ============
	 * Created by Jed Watson and Joss Mackison for KeystoneJS, http://www.keystonejs.com/
	 * https://twitter.com/jedwatson https://twitter.com/jossmackison https://twitter.com/keystonejs
	 * MIT License: https://github.com/keystonejs/react-select
	*/
	.Select {
	  position: relative;
	}
	.Select-control {
	  position: relative;
	  overflow: hidden;
	  box-sizing: border-box;
	  color: #333;
	  cursor: default;
	  outline: none;
	  padding: 10px 52px 10px 12px;
	}
	.Select-control:hover {
	  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);
	}
	.is-searchable.is-open > .Select-control {
	  cursor: text;
	}
	.is-open > .Select-control {
	  border-bottom-right-radius: 0;
	  border-bottom-left-radius: 0;
	}
	.is-open > .Select-control > .Select-arrow {

	}
	.is-searchable.is-focused:not(.is-open) > .Select-control {
	  cursor: text;
	}
	.is-focused:not(.is-open) > .Select-control {
	}
	.Select-placeholder {
	  color: #aaa;
	  padding: 8px 52px 8px 15px;
	  position: absolute;
	  top: 8px;
	  left: 0;
	  right: -15px;
	  max-width: 100%;
	  overflow: hidden;
	  text-overflow: ellipsis;
	  white-space: nowrap;
	}
	.has-value > .Select-control > .Select-placeholder {
	  color: #333;
	}
	.Select-value {
	  color: #aaa;
	  padding: 8px 52px 8px 10px;
	  position: absolute;
	  top: 0;
	  left: 0;
	  right: -15px;
	  max-width: 100%;
	  overflow: hidden;
	  text-overflow: ellipsis;
	  white-space: nowrap;
	}
	.has-value > .Select-control > .Select-value {
	  color: #333;
	}
	.Select-input > input {
		min-height: 28px;
	  cursor: default;
	  background: none transparent;
	  box-shadow: none;
	  height: auto;
	  border: 0 none;
	  font-family: inherit;
	  font-size: inherit;
	  margin: 0;
	  padding: 0;
	  outline: none;
	  display: inline-block;
	  -webkit-appearance: none;
	}
	.is-focused .Select-input > input {
	  cursor: text;
	}
	.Select-control:not(.is-searchable) > .Select-input {
	  outline: none;
	}
	.Select-loading {
	  -webkit-animation: Select-animation-spin 400ms infinite linear;
	  -o-animation: Select-animation-spin 400ms infinite linear;
	  animation: Select-animation-spin 400ms infinite linear;
	  width: 16px;
	  height: 16px;
	  box-sizing: border-box;
	  border-radius: 50%;
	  border: 2px solid #ccc;
	  border-right-color: #333;
	  display: inline-block;
	  position: relative;
	  margin-top: -8px;
	  position: absolute;
	  right: 30px;
	  top: 50%;
	}
	.has-value > .Select-control > .Select-loading {
	  right: 46px;
	}
	.Select-clear-zone {
	  color: #999;
	  cursor: pointer;
	  display: inline-block;
	  font-size: 16px;
	  padding: 6px 10px;
	  position: absolute;
	  right: 17px;
    top: 14px;
	}
	.Select-clear:hover {
	  color: #c0392b;
	}
	.Select-clear > span {
	  font-size: 1.1em;
	}
	.Select-arrow-zone {
	  content: " ";
	  display: block;
	  position: absolute;
	  right: 0;
	  top: 0;
	  bottom: 0;
	  width: 30px;
	  cursor: pointer;
	}
	.Select-arrow {
	  border-color: #999 transparent transparent;
	  border-style: solid;
	  border-width: 5px 5px 0;
	  content: " ";
	  display: block;
	  height: 0;
	  margin-top: -ceil(2.5px);
	  position: absolute;
	  right: 10px;
    top: 27px;
	  width: 0;
	  cursor: pointer;
	}
	.Select-menu-outer {
	  border-bottom-right-radius: 4px;
	  border-bottom-left-radius: 4px;
	  background-color: #fff;
	  border: 1px solid #ccc;
	  border-top-color: #e6e6e6;
	  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);
	  box-sizing: border-box;
	  margin-top: -1px;
	  max-height: 200px;
	  position: absolute;
	  top: 100%;
	  width: 100%;
	  z-index: 1000;
	  -webkit-overflow-scrolling: touch;
	}
	.Select-menu {
	  max-height: 198px;
	  overflow-y: auto;
	}
	.Select-option {
	  box-sizing: border-box;
	  color: #666666;
	  cursor: pointer;
	  display: block;
	  padding: 8px 10px;
	}
	.Select-option:last-child {
	  border-bottom-right-radius: 4px;
	  border-bottom-left-radius: 4px;
	}
	.Select-option.is-focused {
	  background-color: @teal;
	  font-weight: 600;
	  color: white;
	}
	.Select-option.is-disabled {
	  color: #cccccc;
	  cursor: not-allowed;
	}
	.Select-noresults,
	.Select-search-prompt,
	.Select-searching {
	  box-sizing: border-box;
	  color: #999999;
	  cursor: default;
	  display: block;
	  padding: 8px 10px;
	}
	.Select.Select--multi .Select-input {
	  padding: 10px 52px 10px 12px;
	}
	.Select.Select--multi .Select-input {
	  vertical-align: middle;
	  border: 1px solid transparent;
	  margin: 2px;
	  padding: 3px 0;
	}
	.Select-item-icon,
	.Select-item-label {
	  display: inline-block;
	  vertical-align: middle;
	}
	.Select-item-label .Select-item-label__a {
	  color: #007eff;
	  cursor: pointer;
	}
	.Select.Select--multi.is-disabled .Select-item {
	  background-color: #f2f2f2;
	  border: 1px solid #d9d9d9;
	  color: #888;
	}
	.Select.Select--multi.is-disabled .Select-item-icon {
	  cursor: not-allowed;
	  border-right: 1px solid #d9d9d9;
	}
	.Select.Select--multi.is-disabled .Select-item-icon:hover,
	.Select.Select--multi.is-disabled .Select-item-icon:focus,
	.Select.Select--multi.is-disabled .Select-item-icon:active {
	  background-color: #f2f2f2;
	}
	@keyframes Select-animation-spin {
	  to {
	    transform: rotate(1turn);
	  }
	}
	@-webkit-keyframes Select-animation-spin {
	  to {
	    -webkit-transform: rotate(1turn);
	  }
	}
}