React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Modal = require('react-bootstrap').Modal
FormData = require 'react-form-data'

OptionsListHeader = require './OptionsList/OptionsListHeader'
OptionsListSearch = require './OptionsList/OptionsListSearch'
OptionsList = require './OptionsList/OptionsList'

LoadMore = require '../Timeline/LoadMore'

JournalOption = React.createClass
  displayName: "Tag Option Item"

  propTypes: 
    option: React.PropTypes.object
    onOptionSelected: React.PropTypes.func

  onClick: (option)->
    @props.onOptionSelected(option)
    
  renderSelected: ()->
    if @props.isSelected?
      return (
        <div className="item-icon-right">
          <span className="icon icon-check pull-right"></span>
        </div>
      )
    else
      return undefined

  render: ()->
    <a onClick={@onClick.bind null,@props.option}>
      <div className="item-icon-left">
        <span className="icon icon-journal"></span>
      </div> {@props.option.title}
      <span className="sub-text">{@props.option.entry_count} Entries</span> 
      {@renderSelected()}
    </a>


JournalsAndEventsModal = React.createClass
  displayName: "Journals And Events Modal"

  getInitialState: ->
    selectedOptions: @getInitialSelectedOptions()
    query: undefined
    pager: {
      previous: @props.page?.previous
      next: @props.page?.next
      total_count: @props.page?.total_count
      offset: @props.page?.offset
      limit: @props.page?.limit
    }

  componentWillReceiveProps: (nextProps)->
    @setState
      pager: {
        previous: nextProps.page?.previous
        next: nextProps.page?.next
        total_count: nextProps.page?.total_count
        offset: nextProps.page?.offset
        limit: nextProps.page?.limit
      }
  
  getInitialSelectedOptions: ()->
    selectedOptions = []
    selectedOptions.push @props.journal
    return selectedOptions

  onOptionSelected: (option)->
    selectedOptions = []
    selectedOptions.push option
    @setState
      selectedOptions: selectedOptions
  
  onDone: ()->
    if not _.isEmpty(@state.selectedOptions)
      newJournal = @state.selectedOptions[0]
      if @props.params.entryId?
        if newJournal.id == @props.journal.id
          @props.onHide()
        else
          @.app.entryActionCreators.updateJournalForEntry(@props.entry, newJournal.id)
      else
        if @props.onUpdateJournal?
          @props.onUpdateJournal(newJournal)
    
    @props.onHide()

  onSearch: (query)->
    if query != @state.query
      @setState
        query: query

  onLoadMore: ()->
    if @state.pager.next?
      @.app.journalQueries.getPage(@state.pager.next, 'Journals')

  renderPager: ()->
    if @state.pager?.next? and @props.journals?
      return (
        <div>
          <LoadMore pager={@state.pager} loaded={@props.journals.length} onClick={@onLoadMore}/>
        </div>
      )
    else
      return undefined

  getOptions: ()->
    options = _.reject @props.journals, (journal)->
      journal.journal_type == "AllAboutMe"
    return options

  render: ()->
    <Modal onHide={@props.onHide} show={@props.showModal} {...@props}>
      <OptionsListHeader title={@props.modal_title || 'Journals & Events'} onHide={@props.onHide} onDone={@onDone} />
      <div className="modal-body">
        {
          if not @props.hideSearch
            <OptionsListSearch onSearch={@onSearch} searchPlaceholder={'Search Journals & Events'} query={@state.query}/>
          else
            undefined
        }
        <OptionsList optionsSortBy={'title'} optionComponent={JournalOption} header={'All Journals and Events'} options={@getOptions()} selectedOptions={@state.selectedOptions} onOptionSelected={@onOptionSelected} query={@state.query}/>
        {
          @renderPager()
        }
      </div>
    </Modal>

JournalsAndEventsModalContainer = Marty.createContainer JournalsAndEventsModal,
  listenTo: ['journalStore']

  fetch: 
    journals: ()->
      return @.app.journalStore.getJournals('Journals')
    page: ()->
      return @.app.pageStore.getPage('Journals')
  done: (results)->
    props = _.extend {}, @props, results
    return <JournalsAndEventsModal {...props} /> 
  pending: ()->
    return <div></div>
  failed: ()->
    return <div>MODAL ERROR</div>

module.exports = JournalsAndEventsModalContainer