/*---------------------------------------------------------------------------
  >Crumb Nav
---------------------------------------------------------------------------*/

#nav_crumb {
  z-index: 100;
  .gradient-g-w;
  .shadow;
  
  @media @ss-x {min-height: 50px;}
  
	.btn-new {
	  .f14;
	  padding: 7px 15px;
		line-height: 1;
		
	  @media @xs {margin-bottom: 10px;}
	  @media @sm-x {margin: 10px 0;}
	}
  
	#crumb-bar {
	
		 #date {
			.font2;
		  .f16;
		  color: white;
		  padding-top: 14px;
		  
		  @media @xs {
			  font-size: 14px;
				padding: 11px 0;
			}
	
			.icon-angle-right {
			  filter: alpha(opacity=20);
			  -moz-opacity: .2;
				opacity: .2;
			  padding: 0 4px;
			  color: black;
			  top: -1px;
			  .f13;
			  
			  @media @xs {
				  font-size: 12px;
				  padding: 0 2px;
				}
			}
			
			.disabled {
				color: @gray3;
				pointer-events: none;
	   		cursor: default;
		 	}
			
			&>a {.navy-txt;}
			
			a {display: inline-block;	.hover-teal-txt;}
			 
			span {color: @gray7; .w400;}
			
			.icon-jump-to-date {top: -1px;}
			
			.btn {
		    margin-right: 6px;
		    top: -3px;
			}
		}
		
		.sharing-buttons {
	    top: 13px;
	    right: 3px;
	    
	    @media @ss {display: none;}

			a {
				color: @gray7;
				font-size: 17px;
				padding: 5px;
				
				&:hover {color: @teal;}
			}
		}
		
		select {
	    width: initial;
	    margin: 13px 10px 0 0;
	    height: initial;
	    display: inline-block;
	    
	    @-moz-document url-prefix() {
				height: 26px;
				padding: 0;
	    }
		}
		 
/*
		 .btn {
			 top: 11px;
			 margin-right: 10px;
		 }
*/
	
		.crumb-icon {
		  padding: 0;
		  .f16;
		  
		  @media @xs {font-size: 14px;}
		}
		
		.dropdown-toggle {
			right: 5px;
		  top: 12px;
		  color: @gray7;
		  .hover-teal-txt;
		  .f20;
		  
		  @media @xs {
		    right: 2px;
		    top: 11px;
		    font-size: 17px;
		  }
		}
		
		.dropdown-menu {
			top: 40px;
			overflow: hidden;
			
			img {
				width: 14px;
				margin-right: 10px;
			}
		}
		
		.ion-ios-more {
		  margin: 2px 8px 0 0;
			.f26;
			.gray4-txt;
		  .hover-teal-txt;
		  
		  &:hover, &:focus {
			  background-color: transparent;
		  }
		}
	}
	
	#tools {
		
		@media @sm-x {height: 50px;}
		
		.btn-new, .btn-group {width: 100%;}
	}
}