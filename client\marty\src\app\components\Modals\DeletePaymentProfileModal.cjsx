React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Modal = require('react-bootstrap').Modal

DeletePaymentProfileModal = React.createClass
  displayName: 'Delete Payment Modal'

  onCancel: ()->
    @props.onHide()

  onConfirm: ()->
    @props.app.billingActionCreators.deletePaymentProfile(@props.payment_profile)
    @props.onHide()

  render: ()->
    <Modal onHide={@props.onHide} show={@props.show} {...@props}>
      <div className="text-center">
        <div className="modal-body">
          <div className="confirm-dialogue">
            <h2>You are about to delete the {@props?.card_type} <strong className="teal">"{@props.payment_profile?.safe_credit_card_number}"</strong></h2>
            <p><strong>Are you sure?</strong></p>
            <ul className="share-buttons">          
              <li>
                <a onClick={@onCancel} className="btn btn-teal btn-large share-2">No</a>
              </li>
              <li>
                <a onClick={@onConfirm} className="btn btn-navy btn-large">Yes</a>
              </li>
            </ul>
          </div>    
        </div>
      </div>
    </Modal>
  
DeletePaymentProfileModalContainer = Marty.createContainer DeletePaymentProfileModal,
  listenTo: ['billingStore']

  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <DeletePaymentProfileModal {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <DeletePaymentProfileModal {...props} />
  failed: (error)->
    console.log error
    return <div>Error</div>

module.exports = DeletePaymentProfileModalContainer