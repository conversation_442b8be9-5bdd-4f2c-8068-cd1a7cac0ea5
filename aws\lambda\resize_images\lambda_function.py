import urllib
import uuid
from PIL import Image
import boto3

DEBUG = False

VALID_BUCKETS = ['jrnl']

SIZES = {
    'xl' : 844,
    'lg' : 655,
    'md-xs' : 550,
    'xs' : 375,
}

s3 = boto3.resource('s3')

def lambda_handler(event, context):
    for record in event['Records']:
        bucket = record['s3']['bucket']['name']
        key = urllib.unquote_plus(record['s3']['object']['key']).decode('utf8')
        if DEBUG:
            print "Bucket '%s'" % bucket
            print "Key '%s" % key
        if bucket not in VALID_BUCKETS:
            if DEBUG:
                print "Bucket '%s' not valid" % bucket
            continue
        max_width = None
        for size in SIZES.keys():
            path_start = 'resize/%s/' % size
            if key.startswith(path_start):
                if DEBUG:
                    print "Key matches %s" % path_start
                max_width = SIZES[size]
                break
            if DEBUG:
                print "Key does not match '%s'" % path_start
        if not max_width:
            if DEBUG:
                print "Key did not match any sizes"
            continue

        path_safe_key = key.replace('/', '-')
        if DEBUG:
            print "Path safe key '%s'" % path_safe_key
        download_path = '/tmp/%s-%s' % (uuid.uuid4(), path_safe_key)
        if DEBUG:
            print "dowload path '%s'" % download_path
        upload_path = '/tmp/resized-%s-%s' % (max_width, path_safe_key)
        if DEBUG:
            print "upload path '%s'" % upload_path
        s3_file = s3.Object(bucket, key)
        if s3_file.metadata.get('resized') != 'true':
            s3_file.download_file(download_path)
            with Image.open(download_path) as image:
                if image.size[0] >= max_width:
                    size = (max_width, image.size[1])
                    if DEBUG:
                        print "Resizing image to %s" % str(size)
                    image.thumbnail(size)
                    image.save(upload_path)
                elif DEBUG:
                    print "Width already %s. Not resizing" % str(image.size[0])
            metadata = s3_file.metadata
            metadata['resized'] = 'true'
            s3_file.upload_file(upload_path, ExtraArgs={'ContentType': s3_file.content_type, 'Metadata' : metadata})
            if DEBUG:
                print "File uploaded"
        elif DEBUG:
            print "File already resized; skipping"