React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
History = require('react-router').History
Link = require('react-router').Link

BookShippingCalculator = require './BookShippingCalculator'
PaymentMethod = require './PaymentMethod'

Lifecycle = require('react-router').Lifecycle
History = require('react-router').History

BookOrder = React.createClass
  displayName: 'BookOrder'
  mixins: [History, Lifecycle]
  
  getInitialState: ->
    orderPlaced: false
    showError: false
    error: undefined
    allowOrder: true
    
  componentDidUpdate: (prevProps, prevState) ->
    if @state.quantity_field > 0 and prevState.quantity_field > 0
      if @state.quantity_field != prevState.quantity_field
        
        @setState
          allowOrder: false

    if @state.allowOrder is false
      
      if prevProps.book?.modified != @props.book?.modified
        
        if (@state.quantity_field == @props.book.quantity)
          
          @setState
            allowOrder: true
  
  routerWillLeave: ()->
    if !@state.orderPlaced
      return "Your order has not been placed. Are you sure you want to leave?"
  
  updateBook: (e)->
    if e.target.name == 'quantity'
      if e.target.value > 0
        if @props.updateBook?
          @props.updateBook(e)
      else
        @setState
          showError: true
          error:
            user_message: 'Quantity must be 1 or greater'

  updateQuantity: (e)->
    if !!(parseInt(e.target.value))
      fakeEvent = 
        target:
          name: e.target.name
          value: e.target.value
      @setState({quantity_field: parseInt(e.target.value)}, @updateBook.bind(null, fakeEvent))

  onPrevious: ()->
    @history.pushState null, '/book/preview/' + @props.params.bookId

  onNext: ()->
    if @props.user_payment_profile?.default_payment_profile?
      if @props.onSave?
        @props.onPlaceOrder(true, '/orders/', @refs.paymentMethod.getPaymentProfile())
        @setState
          orderPlaced: true
    else
      @setState
        showError: true
        error: 
          user_message: 'No payment method'

  removeError: ()->
    @setState
      showError: false
      error: undefined

  formatTotalPrice: ()->
    return @props.book?.total_amount

  renderError: (field)->
    if @state.showError
      if @state.error?
        return <div className="alert danger text-center">{@state.error.user_message}</div>
    return undefined    

  renderSuccess: ()->
    if @state.orderPlaced
      return <div className="alert success">Success, your order is created. Redirecting to Your Orders.</div>
    return undefined

  renderEmpty: ()->
    return (
      <div className="panel-body">
        No card on file.
      </div> 
    )
 
  renderPlaceOrder: ()->
    if @state.orderPlaced
      return <a onClick={@onNext} className="btn btn-navy btn-large pull-right disabled">Order Placed</a>
    else
      if @state.allowOrder
        return <a onClick={@onNext} className="btn btn-navy btn-large pull-right">Place Order</a>
      else
        return <a onClick={@onNext} className="btn btn-navy btn-large pull-right disabled">Updating price</a>

  renderOrderLink: ()->
    if @props.orders? and !_.isEmpty(@props.orders)
      return (
        <div className="alert info text-center">
          <Link to={"/orders/" + @props.params.bookId} >View Orders</Link>
        </div>
      )

  render: ()->
    <form onClick={@removeError}>
      <h2 className="section-title">JRNL Book Order</h2>
      {
        @renderSuccess()
      }
      <div className="panel panel-default">                 
        <table id="checkout-table" cellPadding="0" cellSpacing="0" className="table full-width">
          <thead>
            <tr>
              <th>Book Title</th>
              <th className="quantity">Quantity</th>
              <th className="price">Price</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border-btm">
                <div className="order-title">
                  <strong>{@props.book?.title || 'No Title'}</strong>
                </div>
              </td>
              <td className="quantity border-btm">
                <input name="quantity" type="text" className="form-control quantity" placeholder="Quantity" value={@props.book?.quantity} onChange={@updateQuantity}/>
              </td>
              <td className="border-btm price">${@props.book?.price}</td>
            </tr>
            <tr className="small">
              <td>Shipping Cost</td>
              <td className="quantity"><strong></strong></td>
              <td className="price"><strong>${@props.book?.shipping_cost}</strong></td>
            </tr>
            <tr>
              <td className="total"><strong>Total Amount to be charged to card</strong></td>
              <td className="total quantity">{@props.book?.quantity}</td>
              <td className="total price"><strong>${@formatTotalPrice()}</strong></td>
            </tr>
          </tbody>
        </table>
      </div>     
      <hr />
      {
        @renderError()
      }
      <PaymentMethod ref="paymentMethod" key={@props.user_payment_profile?.default_payment_profile} payment_profile_uri={@props.user_payment_profile?.default_payment_profile} success={@props.billing_success} {...@props} />
      {
        @renderOrderLink()
      }
      <a onClick={@onPrevious} className="btn btn-gray btn-large pull-left">Previous</a>
      {
        @renderPlaceOrder()
      }
    </form>

BookOrderContainer = Marty.createContainer BookOrder,
  listenTo: []

  fetch:()->
    if @props.app.authStore.isLoggedIn()
      return {
        user_payment_profile: @props.app.billingStore.getUserPaymentProfile()
        orders: @props.app.bookOrderStore.getBookOrdersByBook('Book'+ @props.params.bookId, @props.params.bookId)
        billing_success: @props.app.billingStore.getSuccess()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <BookOrder ref="innerComponent" {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <BookOrder ref="innerComponent" {...props} />
  failed: (error)->
    console.log error
    return <div>Book Order Error</div>

module.exports = BookOrderContainer