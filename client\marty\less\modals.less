/*---------------------------------------------------------------------------
  >Modals
---------------------------------------------------------------------------*/

.modal {

	.modal-dialog {
		z-index: 2000;
		box-shadow: 0 1px 10px rgba(122, 159, 187, 0.3);
		
		@media @xs {
			max-width: 100%;
	    margin: 1em;
		}
	}

	.modal-header {
	  display: table;
	  width: 100%;
	  padding: 0;
	  height: 52px;
		border: none;
		border-radius: 5px 5px 0 0;

		.btn-flat {
			padding: 15px 10px;
			
			.icon {font-size: 22px;}
			
			&:focus {
				.white-txt;
			}
		}

		.modal-title {
			.font2;
			.white-txt;
			.w600;
			.uppercase;
		  .f20;
		  top: 10px;
		}	  
		
		li {
		  display: inline-block;
		}
	}
	
	.modal-content {
		background-color: transparent;
		border-radius: 5px;
	}

	.modal-body {
		display: table;
	  width: 100%;
	  padding: 0;
	  background: white;
	  border-radius: 0 0 5px 5px;
	  
		.input-group {
			
			select {
		    width: inherit !important;
		    float: none !important;
		    margin-right: 5px;
			}	
			
			span {padding: 0 4px 0 0;}
		}
	  
	  // .form-group:last-child {margin-bottom: 0;}
	  
		.search {
		  display: table;
		  width: 100%;
		  box-shadow: none;
		  
			.btn {
				position: absolute;
			  right: 10px;
			  top: 10px;
			  .f12;
			  padding: 6px 12px;
			}
		}
		
		.list {
		  max-height: 460px;
		  overflow-y: auto;
		  .gray5-txt;

			li .avatar {
				.fl;
			  width: 25px;
				height: 25px;
			  border-radius: 100px;
			  top: 11px;
			  margin-right: 13px;
			}
	
			ul {
			  max-height: 400px;
			  overflow-x: hidden;
		  }
			
			.item-icon-left .avatar {
			  width: 22px;
			  height: 22px;
			  height: auto;
			  top: 12px;
			}
			
			.selected a {
				.teal-txt;
				
				.item-icon-left .icon, .item-icon-right .icon {
					.teal-txt;
				}
			}
		}
		
		.list.multiselect li a {
		  padding: 14px 50px 14px 14px;
		}
		
		.form-group.title {
		  margin: 0;
		
			input {
			  .f20;
			  height: 50px;
			  border-width: 0;
			  .border-btm;
		  }
		}
		
		.form-group {
			
			input.full-input {
				border-width: 0;
			  box-shadow: none;
			  border-radius: 0;
				.border-btm;
			}
		}
		
		.share-message {
		  border: none;
		  min-height: 220px;
		  padding: 14px 16px;
		  .full-width;
		}	

		.confirm-dialogue {
			padding: 70px 0;
	
			h2 {
			  .f30;
			  .font2;
			  font-weight: 300;
			  letter-spacing: -.3px;
			  padding: 0 90px;
			  margin: 0 0 20px;
			}
			
			p {
			  margin: 0 0 20px;
			  .f25;
			}
			
			.confirm-dialogue__buttons {
				
				.btn {margin: 5px 0;}
			}

			.share-buttons {
			
				li {
				  display: inline-block;
			  }
			
				.btn {
				  margin: 0 8px;
				  display: table;
				}
			}	
		}
	}
	
	.modal-footer {
		background-color: white;
		border-radius: 0 0 5px 5px;
	}
	
	#list {
    max-height: 400px;
    overflow-y: auto;
    background-color: white;
	} 
	
	.icon-times-circle {
	  position: absolute;
	  right: 10px;
	  top: 12px;
	}
}

.modal .list li>a:hover,
.modal .list li>div:hover,
#subsection .list li>a:hover,
#subsection .list .item:hover,
#category li a:hover
{
  /* border-left: 5px solid @teal; */
}

#subsection,
#category_tile,
#category
{
	.full-width;
}