React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

Link = require('react-router').Link

QuestionTile = React.createClass
  displayName: 'QuestionTile'

  getCategorySVG: ()->
    if @props.question?.category?
      category_src = "/static/images/icons/icon-" + @props.question?.category?.name.split(" ").join('').toLowerCase() + ".svg"
      return <img src={category_src}/>
    else
      return undefined

  render: ()->
    <div id="question_box" className="entry">
      <Link to={'/all-about-me/category/' + @props.question?.category?.id} className="category">
        {
          @getCategorySVG()
        }
        {
          @props.question?.category?.name
        }
      </Link>
      <div className="title">{@props.question?.text}</div>
      <div className="sub-text pull-right">{@props.entries?.length} Answers</div>
      <Link to={'/all-about-me/new/' + @props.question?.id} query={aamId: 1} className="btn btn-gray">Answer {if @props.entries?.length > 0 then 'Again?'}</Link>
    </div>

QuestionTileContainer = Marty.createContainer QuestionTile,
  listenTo: ['allaboutmeStore']

  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <QuestionTile {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <QuestionTile {...props} />
  failed: (error)->
    console.log error
    return <div>QuestionTile Error</div>


module.exports = QuestionTileContainer