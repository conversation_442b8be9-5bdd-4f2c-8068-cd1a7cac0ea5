React = require 'react'

ListItem = React.createClass
  displayName: 'ListItem'

  redirectToInstagram: ()->
    window.open(@props.titleURL, '_blank')
  
  renderIcon: ()->
    if @props.social?
      return (
        <div className="list-item__left list-item__social">
          {
            if @props.social is "Instagram"
              <i className="fa fa-instagram"></i>
          }
        </div>
    )

    if @props.avatarURL?
      return (
        <div className="list-item__left list-item__avatar" onClick={@props.onClick}>
          <img src={@props.avatarURL} />
        </div>
      )

    if @props.icon?
      return (
        <div className="list-item__left list-item__icon">
          {
            @props.icon
          }
        </div>
      )

  renderUsername: ()->
    if @props.username?
      return (
        <div className="list-item__username">{@props.username}</div>
      )

  render: ()->
    if @props.connectedStatus
      connectionStatus = 'Connected'
      connectionClassName = 'list-item__status--connected'
    else
      connectionStatus = 'Unconnected'
      connectionClassName = 'list-item__status--unconnected'
    
    if @props.asButton
      <a className="TEMP-trigger-import-main btn btn--instagram" onClick={@redirectToInstagram}><i className="icon fa fa-instagram"></i>Connect to Instagram</a>
    else
      <div className="list-item">
        <div className="list-item__row">    
          {
            @renderIcon()
          }
          <div className="list-item__content">
            <a target={"_blank"} className="list-item__title" href={@props.titleURL}>
              {
                @props.title
              }
            </a>
            {
              @renderUsername()
            }   
            <div className={"list-item__status" + connectionClassName} >
              { 
                connectionStatus 
              }
            </div>
          </div>
        </div>
      </div>

module.exports = ListItem