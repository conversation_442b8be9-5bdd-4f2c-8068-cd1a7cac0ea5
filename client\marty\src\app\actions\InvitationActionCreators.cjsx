Marty = require 'marty'
_ = require 'underscore'
invitationConstants = require '../constants/InvitationConstants'

InvitationActionCreators = Marty.createActionCreators
  id: 'InvitationActionCreators'

  updateInvite: (newInvite)->
    return @dispatch invitationConstants.UPDATE_INVITE, newInvite

  resetInvite: ()->
    return @dispatch invitationConstants.RESET_INVITE

  resetErrors: ()->
    return @dispatch invitationConstants.RESET_ERRORS   

  createInvitations: (invitations, options)->
    for invitation in invitations
      @createInvitation _.extend {}, options, {
        shared_email: invitation
      }
    @dispatch invitationConstants.REQUEST_MULTIPLE_INVITATIONS, invitations, options

  createInvitation: (invitation)->
    return @app.invitationHttpAPI.createInvitation(invitation)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (response)=>
      @dispatch invitationConstants.INVITATION_CREATED, response
    .catch (error)=>
      console.log error
      @dispatch invitationConstants.INVITATION_ERROR, error, invitation

  deleteInvitation: (invitation)->
    return @app.invitationHttpAPI.deleteInvitation(invitation)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      @dispatch invitationConstants.DELETE_INVITATION, invitation
    .catch (error)=>
      console.log error
      @dispatch invitationConstants.INVITATION_ERROR

  associateInvitationWithUser: (invitationId)->
    return @app.invitationHttpAPI.updatePublicEntrySharedInvitation(invitationId)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (response)=>
      @dispatch invitationConstants.ASSOCIATE_INVITATION_WITH_USER, response
    .catch (error)=>
      console.log error
      @dispatch invitationConstants.INVITATION_ERROR, error, invitationId



module.exports = InvitationActionCreators