React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Link = require('react-router').Link

FormData = require 'react-form-data'

ConfirmPassword = React.createClass
  displayName: 'Confirm Password'
  mixins: [FormData]

  getInitialState: ->
    showError: !!@props.error?
    error: @props.error

  componentWillReceiveProps: (nextProps) ->
    if nextProps.error? != @state.error
      @setState
        showError: true
        error: nextProps.error

  componentWillUnmount: ->
    @props.app.authActionCreators.resetPasswordState()
  
  onRequestPasswordReset: (e)->
    e.stopPropagation()
    e.preventDefault()

    options = {}
    if @formData.new_password1 and @formData.new_password2
      if (@formData.new_password1 == @formData.new_password2)
        if @props.uid? and @props.token?
          options['new_password1'] = @formData.new_password1
          options['new_password2'] = @formData.new_password2
          options['uid'] = @props.uid
          options['token'] = @props.token
        else
          error =
            field_error: "Missing required parameters"
      else
        error = 
          field_error: "Passwords do not match"
    else
      error = 
        field_error: "All fields required"

    
    if _.isEmpty(options)
      @setState
        showError: true
        error: error
    else
      @.props.app.authActionCreators.changePassword(options)
    
    return
  
  renderSuccess: ()->
    return (
      <div role="tabpanel" className="tab-pane" id="password">
        <div className="message-wrapper">
          <h2>Thank you!</h2>
          <p>Your password has been reset.</p>
        </div>
        <div className="form-wrapper">
        </div>
        <div className="access-links">
          <div className="left">
            <Link to={"/login/"}>Login to my account</Link>
          </div>
          <div className="right">
            <Link to={"/signup/"}>Signup Here!</Link>
          </div>
        </div>
      </div>
    )
  
  renderForm: ()->
    return (
      <form onChange={@updateFormData} autoComplete={"off"} onSubmit={@onRequestPasswordReset}>
        <div role="tabpanel" className="tab-pane" id="password">
          <div className="message-wrapper">
            <h2>Reset Password</h2>
            <p>Enter your new password</p>
          </div>
          <div className="form-wrapper">
            {
              @renderError()
            }
            <div className="form-group">
              <input type="password" className="form-control" name="new_password1" placeholder="Enter password" onClick={@removeError}/>
              {
                @renderFieldError('new_password1')
              }
              <input type="password" className="form-control" name="new_password2" placeholder="Enter password again" onClick={@removeError}/>
              {
                @renderFieldError('new_password2')
              }
              <a onClick={@onRequestPasswordReset} className="btn btn-navy btn-med full-width">Reset Password</a>
            </div>
          </div>
          <div className="access-links">
            <div className="left">
              <Link to={"/login/"}>Login to my account</Link>
            </div>
            <div className="right">
              <Link to={"/signup/"}>Signup Here!</Link>
            </div>
          </div>
        </div>
      </form>
    )

  renderError: ()->
    if @state.showError
      if @state.error?
        if @state.error.user_message?
          if @state.error.validation_errors?
            validation_error = @state.error.validation_errors.__all__
            output = <div className="alert danger text-center">{@state.error.user_message} {validation_error}</div>
        else if @state.error.field_error
          output = <div className="alert danger text-center">{@state.error.field_error}</div>
        return output

    return undefined

  renderFieldError: (field)->
    if @state.showError
      if @state.error?
        if @state.error.validation_errors?
          if @state.error.validation_errors[field]?
            messages = @state.error.validation_errors[field]
            console.log messages
            output = []
            for message in messages
              output.push <div className="alert danger text-center">{message}</div>
            return output
    return undefined

  removeError: ()->
    @setState
      showError: false
      error: undefined

  render: ()->
    if @props.success
      content = @renderSuccess()
    else
      content = @renderForm()
    <div>
      <div id='access'>
        <div className='container' style={maxWidth: "350"}>
          <div className="panel frame">
            <div className="panel-body">
              <Link to={"/"} className="jrnl-logo"><img src="/static/images/jrnl-logo.png"/></Link>      
              {
                content
              }
            </div>
          </div>
        </div>
      </div>
    </div>

ConfirmPasswordContainer = Marty.createContainer ConfirmPassword,
  listenTo: ['authStore']

  fetch: ()->
    return {
      uid: @props.params.uid
      token: @props.params.token
      error: @props.app.authStore.getError()
      success: @props.app.authStore.passwordResetState()
    }
  done: (results)->
    props = _.extend {}, @props, results
    return <ConfirmPassword {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <ConfirmPassword {...props}/>
  failed: (error)->
    console.log error
    return <div>Error</div>

module.exports = ConfirmPasswordContainer