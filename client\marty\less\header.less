/*---------------------------------------------------------------------------
  >Header
---------------------------------------------------------------------------*/

header {
  display: table;
  height: 60px;
  width: 100%;
  z-index: 200;
  .shadow-small;

	.title-wrapper {
		.table-wrapper;
		
		.title-row {
			.table-row;
			
			.col {
				.table-cell;
				vertical-align: middle;
			}
			
			.col-left {
   	    @media @sm-x {width: 70%;}
				
				.avatar {
					margin-right: 15px;
			    margin-bottom: -6px;
			    top: -2px;
					float: left;
				}
								
				.title {
				  margin: 0;
				  font-size: 22px;
					line-height: 1.4em;
					.w600; 
					.navy-txt;
					.truncate;
					
					@media @xs {font-size: 17px;}
				}
				
				.subtitle {
			    font-size: 12px;
			    color: @gray5;
					.truncate;
				}
			}
			
			.col-right {
   	    @media @sm-x {width: 30%;}
				
				.nav {float: right;}
			}
		}
	}
  
/*
	h1 {

		.avatar {
		  margin-right: 13px;
		  top: -2px;
		}
	
		.title-wrapper {
			
			.icon {
				.f16;
				margin-right: 6px;
				top: -3px;
			}
		}
	}
*/
	
	#nav-main {
		margin-bottom: 0px !important;
		
		.title a {
			.navy-txt;
			.hover-teal-txt;
		}
		
		.nav .open>a, .nav .open>a:focus, .nav .open>a:hover {background: transparent;}
		
		.nav {
			margin: 0;
			
			.avatar-sm {
			  top: 17px;
			  width: 26px;
				height: 26px;
				position: absolute;
			  right: 0;
			}
			
			.badge {
				margin-left: 8px;
		    padding: 3px 6px;
		    line-height: 13px;
		    vertical-align: baseline;
		    text-align: center;
		    background-color: #ff7200;
		    border-radius: 4px;
			}
			
			&>#user>a {padding-right: 36px;}
			
			&>li {
				display: inline-block;
				
				&>a {
				  padding: 20px 13px;
				  .w600;
				  .navy-txt;
				  .hover-teal-txt;
				  
				  &:hover, &:focus {
					  text-decoration: none;
					  background-color: transparent;
					  cursor: pointer !important;
					}	  
			  }
			}
			
			.dropdown-menu {
			  top: 52px;
			  z-index: 100;
			  .radius4;
			  
			  @media @xs {
					position: absolute;
					background-color: white;
					float: left;
					border: 1px solid rgba(0,0,0,.15);
					.shadow; 
			  }

				&>li>a {
					@media @xs {padding: 5px 12px !important;}
				}
			}
		}
	}
}