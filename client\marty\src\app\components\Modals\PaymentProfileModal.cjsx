React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'
Modal = require('react-bootstrap').Modal
FormData = require 'react-form-data'

ModalHeader = require '../Modals/Headers/ModalHeader'
AuthenticatedComponent = require '../AuthenticatedComponent'

PaymentProfileModal = React.createClass
  displayName: 'Payment Profile Modal'
  mixins: [FormData]

  getInitialState: ->
    showError: !!@props.error?
    error: @props.error

  componentDidMount: ->
    @formData = {}
  
  componentWillReceiveProps: (nextProps) ->
    showError = undefined
    error = undefined
    
    if nextProps.error? != undefined
      showError = true
      error = nextProps.error

    if not @props.show and nextProps.show
      @formData = {}

    @setState
      showError: showError
      error: error
  
  onDone: ()->
    if @props.payment_profile?
      keys = ['card_type', 'first_name','last_name','city','state','expiration_month','expiration_year']
      paymentProfile = _.defaults @formData, _.pick @props.payment_profile, keys
    else
      paymentProfile = @formData
    
    if not paymentProfile.expiration_month? and @refs.expiration_month.getDOMNode().value > 0
      paymentProfile['expiration_month'] = @refs.expiration_month.getDOMNode().value
    
    if not paymentProfile.expiration_year? and @refs.expiration_month.getDOMNode().value > 0
      paymentProfile['expiration_year'] = @refs.expiration_year.getDOMNode().value
    
    if not paymentProfile.state? and @refs.state.getDOMNode().value > 0
      paymentProfile['state'] = @refs.state.getDOMNode().value
    
    switch @props.mode
      when "Add"
        paymentProfile['default'] = true
        @props.app.billingActionCreators.createPaymentProfile(paymentProfile)
        return
      when "Edit"
        @props.app.billingActionCreators.updatePaymentProfile(paymentProfile, @props.payment_profile.id)
    return

  onHide: ()->
    @formData = {}
    @setState
      success: undefined
      error: undefined
      showError: false
    @props.app.billingStore.reset()
    if @props.onHide?
      @props.onHide()
    
  cleanExpirationDate: (expiration_month, expiration_year)->
    month = expiration_month
    year = expiration_year    
    date = month + "-" + year
    return moment(date, "MM-YYYY").endOf('month').format('YYYY-MM-DD')

  splitExpirationDate: ()->
    if @props.payment_profile?.expiration_date?
      date = moment(@props.payment_profile.expiration_date, 'YYYY-MM-DD')
      expiration_month = date.month()+1
      expiration_year = date.year()
      return {
        expiration_month: expiration_month
        expiration_year: expiration_year
      }
    else
      return {
        expiration_month: undefined
        expiration_year: undefined
      }

  formatFieldErrorMessage: (field, message)->
    switch field
      when 'expiration_date'
        return <div className="col-sm-4"><div className="alert danger text-center">Exp. date is empty or in the past.</div></div>
      when 'cvv'
        return <div className="alert danger text-center">Required</div>
      else
        return <div className="alert danger text-center">{message}</div>

  removeError: ()->
    @setState
      showError: false
      error: undefined

  getCurrentMonth: ()->
    return moment().month() + 1

  getCurrentYear: ()->
    return moment().year()
    
  renderFieldError: (field)->
    if @state.showError
      if @state.error?
        try
          if @state.error.validation_errors[field]?
            messages = @state.error.validation_errors[field]
            output = []
            for message in messages
              output.push @formatFieldErrorMessage(field, message)
            return output
        catch
          return undefined
    return undefined

  renderError: ()->
    if @state.showError
      if @state.error?
        try
          if @state.error.validation_errors['__all__']?
            message = @state.error.validation_errors['__all__']
            return <div className="alert danger text-center">{message}</div>
        catch
          return undefined
    return undefined

  render: ()->
    <Modal show={@props.show} onHide={@onHide} backdrop={'static'} {...@props}>
      <ModalHeader header={@props.mode + " Card"} onHide={@onHide} onDone={@onDone}/>
      <form className="modal-body padding-20" onChange={@updateFormData} onClick={@removeError}>
        <div className="settings-column">
          <h3 className="section-title">Credit Card Info</h3>
          <div className="row">
            <div className="col-sm-6">
              <div className="form-group">
                <label>card Type</label>
                <select defaultValue={@props.payment_profile?.card_type || 0} name="card_type" className="form-control full-width">
                  <option value=0></option>
                  <option value="visa">Visa</option>
                  <option value="mc">MasterCard</option>
                  <option value="discover">Discover</option>
                </select>
                {
                  @renderFieldError('card_type')
                }
              </div>
            </div>
          </div>
          <div className="row">
            <div className="col-sm-6">
              <div className="form-group">
                <label>Card Number</label>
                <input name="card_number" type="text" className="form-control" placeholder={@props.payment_profile?.safe_credit_card_number || "XXXX-XXXX-XXXX-XXXX"} />
              </div>
              {
                @renderFieldError('card_number')
              }
              {
                @renderError()
              }
            </div>
            <div className="col-sm-2">
              <div className="form-group">
                <label>CVV2</label>
                <input name="cvv" type="text" className="form-control" placeholder="XXX" />
              </div>
              {
                @renderFieldError('cvv')
              }
            </div>
            <div className="col-sm-2">
              <div className="form-group">
                <label>Exp. Month</label>
                <select ref='expiration_month' defaultValue={ @splitExpirationDate().expiration_month || @getCurrentMonth()} name="expiration_month" className="form-control full-width">
                  <option value=0></option>
                  <option value=1>1</option>
                  <option value=2>2</option>
                  <option value=3>3</option>
                  <option value=4>4</option>
                  <option value=5>5</option>
                  <option value=6>6</option>
                  <option value=7>7</option>
                  <option value=8>8</option>
                  <option value=9>9</option>
                  <option value=10>10</option>
                  <option value=11>11</option>
                  <option value=12>12</option>
                </select>
              </div>
            </div>
            <div className="col-sm-2">
              <div className="form-group">
                <label>Exp. Year</label>
                <select ref='expiration_year' name="expiration_year" defaultValue={ @splitExpirationDate().expiration_year || @getCurrentYear()}  className="form-control full-width">
                  <option value=0></option>
                  <option value=2015>2015</option> 
                  <option value=2016>2016</option>
                  <option value=2017>2017</option>
                  <option value=2018>2018</option>
                  <option value=2019>2019</option>
                  <option value=2020>2020</option>
                  <option value=2021>2021</option>
                  <option value=2022>2022</option>
                  <option value=2023>2023</option>
                  <option value=2024>2024</option>
                  <option value=2025>2025</option>
                </select>
              </div>
            </div>
            {
              @renderFieldError('expiration_date')
            }
          </div>
          <hr />
          <h3 className="section-title">Billing Info</h3>
          <div className="row">
            <div className="col-sm-6">
              <div className="form-group">
                <label>First Name</label>
                <input defaultValue={@props.payment_profile?.first_name} name="first_name" type="text" className="form-control" placeholder="First Name" />
              </div>
              {
                @renderFieldError('first_name')
              }
            </div>
            <div className="col-sm-6">
              <div className="form-group">
                <label>Last Name</label>
                <input defaultValue={@props.payment_profile?.last_name} name="last_name" type="text" className="form-control" placeholder="Last Name" />
              </div>
              {
                @renderFieldError('last_name')
              }
            </div>
          </div>
          <div className="row">
            <div className="col-sm-12">
              <div className="form-group">
                <label>Street Address</label>
                <input defaultValue={@props.payment_profile?.street} name="street" type="text" className="form-control" placeholder="Street Address" />
              </div>
              {
                @renderFieldError('street')
              }
            </div>
          </div>
          <div className="row">
            <div className="col-sm-6">
              <div className="form-group">
                <label>City</label>
                <input defaultValue={@props.payment_profile?.city} name="city" type="text" className="form-control" placeholder="City" />
              </div>
              {
                @renderFieldError('city')
              }
            </div>
            <div className="col-sm-2">
              <div className="form-group">
                <label>State</label>
                <select ref='state' defaultValue={@props.payment_profile?.state || 0} name="state" className="form-control full-width">
                  <option value=0></option>
                  <option value="AL">AL</option>
                  <option value="AK">AK</option>
                  <option value="AZ">AZ</option>
                  <option value="AR">AR</option>
                  <option value="CA">CA</option>
                  <option value="CO">CO</option>
                  <option value="CT">CT</option>
                  <option value="DE">DE</option>
                  <option value="DC">DC</option>
                  <option value="FL">FL</option>
                  <option value="GA">GA</option>
                  <option value="HI">HI</option>
                  <option value="ID">ID</option>
                  <option value="IL">IL</option>
                  <option value="IN">IN</option>
                  <option value="IA">IA</option>
                  <option value="KS">KS</option>
                  <option value="KY">KY</option>
                  <option value="LA">LA</option>
                  <option value="ME">ME</option>
                  <option value="MD">MD</option>
                  <option value="MA">MA</option>
                  <option value="MI">MI</option>
                  <option value="MN">MN</option>
                  <option value="MS">MS</option>
                  <option value="MO">MO</option>
                  <option value="MT">MT</option>
                  <option value="NE">NE</option>
                  <option value="NV">NV</option>
                  <option value="NH">NH</option>
                  <option value="NJ">NJ</option>
                  <option value="NM">NM</option>
                  <option value="NY">NY</option>
                  <option value="NC">NC</option>
                  <option value="ND">ND</option>
                  <option value="OH">OH</option>
                  <option value="OK">OK</option>
                  <option value="OR">OR</option>
                  <option value="PA">PA</option>
                  <option value="RI">RI</option>
                  <option value="SC">SC</option>
                  <option value="SD">SD</option>
                  <option value="TN">TN</option>
                  <option value="TX">TX</option>
                  <option value="UT">UT</option>
                  <option value="VT">VT</option>
                  <option value="VA">VA</option>
                  <option value="WA">WA</option>
                  <option value="WV">WV</option>
                  <option value="WI">WI</option>
                  <option value="WY">WY</option>
                </select>
                {
                  @renderFieldError('state')
                }
              </div>
            </div>
            <div className="col-sm-2">
              <div className="form-group">
                <label>Zip</label>
                <input defaultValue={@props.payment_profile?.zip_code} name="zip_code" type="text" className="form-control" placeholder="Zip" />
              </div>
              {
                @renderFieldError('zip_code')
              }
            </div>
          </div>
        </div>
      </form>
    </Modal>

AddPaymentProfileModalContainer = AuthenticatedComponent(Marty.createContainer PaymentProfileModal,
  listenTo: ['billingStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn()
      return {
        mode: 'Add'
        error: @props.app.billingStore.getError()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <PaymentProfileModal {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <PaymentProfileModal {...props} />
  failed: (error)->
    console.log error
    return <div>ERROR</div>
)

EditPaymentProfileModalContainer = AuthenticatedComponent(Marty.createContainer PaymentProfileModal,
  listenTo: ['billingStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn()
      return {
        mode: 'Edit'
        error: @props.app.billingStore.getError()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <PaymentProfileModal {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <PaymentProfileModal {...props} />
  failed: (error)->
    console.log error
    return <div>ERROR</div>  
)

module.exports = {
  AddPaymentProfileModal: AddPaymentProfileModalContainer
  EditPaymentProfileModal: EditPaymentProfileModalContainer
}