/*---------------------------------------------------------------------------
  >Interaction
---------------------------------------------------------------------------*/

.interaction {
	display: table;
	table-layout: fixed;
	width: 100%;
  margin-top: 12px;
  margin-bottom: -8px;

  @media @xs {padding: 0 13px 0 15px;}
  @media @sm-lg {padding: 0 13px 0 15px;}
  @media @lg-x {padding: 0 14px 0 16px;}
  
  			
	.col-left .button, .dropdown-toggle {color: @gray7;}
	
	a.button:hover {color: @teal !important;}
	
	.interaction-row {
		.table-row;
		
		.col {
			vertical-align: middle;
			.table-cell;
			
			.button {
				
				.icon {font-size: 14px;}
				
				span {
				  font-size: 12px;
				  display: inline-block;
				  padding-left: 4px;
				  top: -1px;
				}
			}
			
			.dropdown-menu {
		    max-height: 150px;
		    overflow: hidden;
		    overflow-y: auto;
			}
		}
	
		.col-left {
			
			.dropdown-menu {left: 30px;}
			
			.button {
				padding: 0 12px 0 0;
				display: inline-block;
				
				span {
				  @media @xs {display: none !important;}	
				}
			}
		}
		
		.col-right {
			text-align: right;
			
			.dropdown-menu {
				right: -9px !important;
			}
		}
	}
}