/*---------------------------------------------------------------------------
  >Access
---------------------------------------------------------------------------*/

#access {
	padding-top: 5%;
	
	.container {max-width: 320px;}
	
	&.access-share .container {

		@media @xs {width: 100%;}		
		@media @sm-x {max-width: 700px;}
		
		.instructions {
	    background-color: @teal;
	    text-align: center;
	    color: white;	
	    margin: 0 15px 1em;
	    
	    @media @xs {
		    font-size: 16px;
		    font-weight: 400;
		    padding: .8em 1em;
		    letter-spacing: -0.02em;
	    }	    
	    @media @sm-x {
		    font-size: 29px;
		    font-weight: 300;
		    padding: .6em 1em;
		    letter-spacing: -.04em;
	    }
		}
		
		.access-links {
		  margin-top: 15px;
		  text-align: center;
		}
	}
		
	.panel {
		border: 0;
		margin-bottom: 30px;
		width: 100%;
			
		.panel-heading {
			padding: 0;
			border: 0;
			
			.tabs-vertical {	
			  border-radius: 3px 3px 0 0;
				overflow: hidden;
				
				li {
					width: 50%;
					.fl;
				}
		  }
		}
		
		.panel-body {
			padding: 0;
			
			.jrnl-logo {
				margin: 60px 0 30px;
				text-align: center;
				display: block;
				
				img {
					width: 200px;
					margin: 0 auto;
				}
			}
						
			.message-wrapper {
				padding: 0 25px 0;
				text-align: center;
				
				h2 {
			    margin: 0 0 10px;
					.navy-txt;
					.font2;
					.f20;
				}
				
				p {
					font-size: 13px;
					line-height: 17px;
					.gray6-txt;
				}
			}
			
			.form-wrapper {
				padding: 10px 15px 0;
				
				h2 {
			    margin: 0 0 10px;
					color: @navy;
					font-size: 20px;
					.font2;
				}
				
				.form-group {
					border-radius: 3px;
					overflow: hidden;
					margin: 0 0 50px;
					
					.alert {
				    padding: 4px 0 0;
				    color: white;
				    font-weight: 500;
				    font-size: 11px;
				    margin-bottom: 0px;
				    border-radius: 0;
				    text-align: left;
				    background-color: transparent;
				    color: @red;
					}
				}
				
				input {
				  border-color: @gray9;
				  border-width: 0 0 1px 0;
				  border-style: solid;
					height: 37px;
					padding: 8px 0;
					margin-top: 15px;
					//box-sizing: border-box;
					
					&.error {
						border-color: @red;
						border-width: 0 0 2px 0;
					}
					
					&:-webkit-autofill {
						background-color: transparent !important;
						-webkit-box-shadow: 0 0 0px 1000px white inset;
					}
					
					&:focus {
						border-color: @teal !important;
						border-width: 0 0 2px 0;
					}
				}
				
				.btn {
					padding: 12px 15px;
					margin-top: 20px;
					.f16; 
				}
				
				input {
					border-radius: 0;
					box-shadow: none;
				}
			}
			
			.access-links {
				
				&>div {
					padding: 10px 15px;
				}
				
				.left, .right {
					width: 50%;
					float: left;
				}
				
				.right {text-align: right;}
				
				.center {
					text-align: center;
					width: 100%;
				}
				
				a {
					.gray5-txt;
					.f12;
					
					&:hover {.teal-txt;}
				}
			}
		}
	}
	
	.legal {
	  text-align: center;
	  .full-width;
	  .fl;
	  
	  .links {
		  border-radius: 4px;
		  padding: 8px 0 0;
		  .white-bg;
			.shadow;
	  }
	  
	  a {
		  padding: 0 15px;
		  .hover-teal-txt;
		  .uppercase;
		  .gray5-txt;
		  .w600;
		  .f11;
	  }
	  
	  .copyright {
		  margin-top: 10px;
		  padding: 5px 0;
		  border-radius: 0 0 3px 3px;
		  .gradient-g-w;
		  .border-top;
		  .gray4-txt;
		  .f10;
		}
	}

	.form-control:-moz-placeholder					 {color: @gray6; .w400;}
	.form-control::-moz-placeholder    			 {color: @gray6; .w400;}
	.form-control:-ms-input-placeholder			 {color: @gray6; .w400;}
	.form-control::-webkit-input-placeholder {color: @gray6; .w400;}		

}