React = require 'react'
Link = require('react-router').Link

DateRangeHeaderControl = require '../Header/Controls/DateRangeHeaderControl'
DateRangePagerControl = require '../Header/Controls/DateRangePagerControl'
EntryListSortControl = require '../Header/Controls/EntryListSortControl'


AutoAffix = require('react-overlays').AutoAffix

TimelineHeader = React.createClass
  displayName: 'TimelineHeader'
  STICKY_STYLES: {}
    # "left":"100px", 
    # "width":"100%", 
    # "position":"fixed", 
    # "top": "0", 
    # "zIndex": "1"
    
  render: ()->
    <AutoAffix offsetTop={60} container={document.body}>
      <div id="nav_crumb" className="gray">
        <div className="container">
          <div id="crumb-bar" className="pull-left">
            <DateRangeHeaderControl calendar_id={'Timeline'} widgetCallback={@props.widgetCallback} {...@props}/>
            <div id="options" className="pull-right">
              <DateRangePagerControl {...@props} />
              <EntryListSortControl order_by={@props.order_by} onEntryViewModeChange={@props.onEntryViewModeChange} entry_view_mode={@props.entry_view_mode} user={@props.user}/>
            </div>
          </div>
          <div id="tools" className="pull-right text-right">
            <Link to={"/entry/create/"} className="btn btn-new pull-right btn-navy">New Entry</Link>
          </div>
        </div>
      </div>
    </AutoAffix>
    

module.exports = TimelineHeader