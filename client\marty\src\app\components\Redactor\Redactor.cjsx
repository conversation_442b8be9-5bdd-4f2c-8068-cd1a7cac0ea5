React = require 'react'
ReactDOM = require 'react-dom'
Marty = require 'marty'
_ = require 'underscore'
settings = require '../../settings'
redactor = require 'redactor'

Redactor = React.createClass
  displayName: 'Redactor'
  mixins: [Marty.createAppMixin()]
  
  componentWillReceiveProps: (nextProps) ->
    if @props.content != nextProps.content
      $('#redactor').redactor('code.set', nextProps.content)  

  componentDidMount: ->
    mediaUpload = @props.onRedactorOpenMedia
    importInstagram = @props.onRedactorImport
    onRedactorInit = @onRedactorInit

    $('#redactor').redactor
      focus: true
      imageResizable: true
      basicAuth: 
        scheme: 'ApiKey'
        parameter: @.app.authStore.getAuthParameter()
      buttons: ['formatting', 'bold', 'italic', 'underline', 'deleted', 'link', 'unorderedlist', 'orderedlist', 'outdent', 'indent', 'alignment', 'horizontalrule']
      plugins: ['media']
      toolbarFixed: false
      replaceDivs: false
      changeCallback: (content)=>
        if @props.onRedactorChange?
          @props.onRedactorChange($('#redactor').redactor('code.get'))
        return
      initCallback: ()->
        # button = @.button.add 'image','Media'
        # @.button.addCallback button, mediaUpload
        
        # importButton = @.button.add 'gallery','Import'
        # @.button.addCallback importButton, importInstagram

        if onRedactorInit?
          onRedactorInit($('#redactor'))

        return
      blurCallback: (e)=>
        return
    return
  
  componentWillUnmount: (nextProps, nextState) ->
    $('#redactor').redactor('core.destroy')

  shouldComponentUpdate: ()->
    return false

  onRedactorInit: (redactor)->
    if @props.onRedactorInit?
      @props.onRedactorInit(redactor)

  getRedactorValue: ->
    element = ReactDOM.findDOMNode(@)
    $('#redactor').redactor('code.get')
  
  render: ->
    <div>
      <textarea name="redactor" id="redactor" defaultValue={@props?.content}></textarea>
    </div>
   

module.exports = Redactor