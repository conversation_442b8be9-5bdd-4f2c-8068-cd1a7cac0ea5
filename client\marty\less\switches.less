	#switch {
	overflow: hidden;
	
	[type="checkbox"], [type="radio"] {display: none;}
	
	input.switch:empty ~ label
	{
	  position: relative;
	  float: left;
	  line-height: 22px;
	  text-indent: 43px;
	  margin: 0;
	  cursor: pointer;	
	  -webkit-user-select: none;
	  -moz-user-select: none;
	  -ms-user-select: none;
	  user-select: none;
	}
	
	input.switch:empty ~ label:before, 
	input.switch:empty ~ label:after
	{
	  position: absolute;
	  display: block;
	  top: 0;
	  bottom: 0;
	  left: 0;
	  content: ' ';
	  width: 45px;
	  background-color: @gray8;
	  border-radius: 50px;
		box-shadow: inset 0 1px 2px 0 rgba(0,0,0,0.15);
		-webkit-transition: all 100ms ease-out;
		-moz-transition:    all 100ms ease-out;
		-o-transition:      all 100ms ease-out;
		-ms-transition:     all 100ms ease-out;
		transition:         all 100ms ease-out;
	}
	
	input.switch:empty ~ label:after
	{
		width: 16px;
    top: 3px;
    bottom: 3px;
    margin-left: 3px;
    background-color: #fff;
    border-radius: 50px;
		box-shadow: 0 1px 2px rgba(0,0,0,0.2);
	}
	
	&.green {input.switch:checked ~ label:before {background-color: @green;}}
	&.red {input.switch:checked ~ label:before {background-color: @red;}}
	
	input.switch:checked ~ label:after {margin-left: 26px;}	

	input.switch:empty {margin-left: -999px;}
}