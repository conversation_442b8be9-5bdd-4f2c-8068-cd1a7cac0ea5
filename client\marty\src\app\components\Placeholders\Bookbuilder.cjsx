React = require 'react'
Marty = require 'marty'

Drawer = require '../Sidebar/Drawer'
AppHeader = require '../Header/AppHeader'

BookbuilderPlaceholder = React.createClass
  displayName:"Bookbuilder Placeholder"
  mixins:[Marty.createAppMixin()]

  render: ()->
    <div>
      <Drawer />
      <div id="page">
        <AppHeader username={@app.authStore.getUsername()} view_title='Bookbuilder' {...@props}/>
        <div id="nav_crumb" className="gray">
          <div className="container">
            <div id="crumb-bar" className="pull-left">
              <div id="date" className="pull-left">
              </div>
            </div>
            <div id="tools" className="pull-right text-right">
            </div>
          </div>
        </div>
        <div id="content">
          <div className="container">
            <div id="bookbuilder-column" className="pull-left full-width">
              <div className="alert section-intro teal-bg alert-dismissible">
                <img src="/static/images/bbdr-instruction-image.jpg" alt="aam-instruction-image" style={width:"100%", height:"auto"}/>
                <div className="section-intro-body">
                  <h2>Easily create and publish amazing archival-quality books to enjoy for generations to come.</h2>
                  <h3 style={marginBottom: "30px"}>High quality. Print on demand. Fully customizable.</h3>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

module.exports = BookbuilderPlaceholder