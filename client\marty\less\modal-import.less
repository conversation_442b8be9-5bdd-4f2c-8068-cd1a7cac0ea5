.import {
	
	@media @sm-x {
		.flex-container;
		.flex-row;
	}	
}

.import__col {
	
	.select-all {
		border-bottom: 1px solid @black1;
		background: #f5f5f5;
		
		@media (min-width: 620px) {	border-right: 1px solid @black1;}
	}
	
	&.import__col--left {
		
		.search {

			@media (min-width: 620px) {	border-right: 1px solid @black1;}
			
			input[type="text"] {
				width: 100%;
		    padding: .8em 1em;
		    font-size: 1em;
				border-width: 0 0 1px 0;
				border-style: solid;
				border-color: @black1;
			}
		}
		
		.import__content {
			background: #f5f5f5;
			
			@media (min-width: 620px) {	border-right: 1px solid @black1;}
		}
		
		.refresh-button {
			padding: 15px 15px 0;
			
			.btn {width: 100%;}
		}
		
		.load-more {
	    padding: 15px;
	    
	    .btn--load-more {
		    width: 100%;
		    font-size: 13px;
	    }
		}
	}
	
	&.import__col--right {
		background: white;
		.flex1;
		
		@media (max-width: 620px) {display: none;}
	}
}

#modal_import {
	padding: 1em !important;
	
	.modal-dialog {
		margin: 0 auto;
		
		@media (max-width: 490px) {
				
			.row {
				margin: 0;
			
				& > div {padding: 0;}
			}
		}
		
		.btn-flat {
			
			@media (max-width: 490px) {padding: 15px 8px;}
		}
		
		.modal-title {
			
			@media (max-width: 490px) {
				font-size: 13px;
				line-height: 34px;
			}
		}
	}

	&.dialog--connect {
		
		.modal-body {
			text-align: center;
			padding: 2em;
			
			.btn {
				padding: 10px 20px;
				
				.icon {margin-right: 8px;}
			}
		}
	}
	
	&.dialog--import, &.dialog--preview {
		
		.modal-dialog {
			width: 100%;
			
			.modal-body {
		    position: relative;
		    display: inline-block;
		    overflow-y: auto;
		    overflow-x: hidden;
		    display: -webkit-box;
		    display: -webkit-flex;
		    display: -ms-flexbox;
		    display: flex;
		    -webkit-box-flex: 1;
		    -webkit-flex: 1 0 auto;
		    -ms-flex: 1 0 auto;
		    flex: 1 0 auto;
		    
		    @media (min-width: 1025px) and (max-height: 439px) {height: 75px;}
		    @media (min-width: 1025px) and (min-height: 440px) and (max-height: 639px) {height: 80vh;}
		    @media (min-width: 1025px) and (min-height: 640px) and (max-height: 999px) {height: 85vh;}
		    @media (min-width: 1025px) and (min-height: 1000px) {height: 90vh;}
			}
		}
	}
	
	&.dialog--import {
		
		.modal-dialog {
			max-width: 1000px;
		}
	}
	
	&.dialog--preview {
		
		.modal-dialog {
			max-width: 600px;
		}
	}	
}

.dialog--preview {
	
	.modal-body {
		background: #f5f5f5;
		.flex-column;
		
		.preview--raw {

			@media @xs {padding: 1em;}
			@media @sm-x {padding: 2em;}
			
			img {
				width: 100%;
				max-width: 100%;
				height: auto;
		    margin-bottom: 1em;
			}
		}		
	}
}

.dialog--import {
	
	.dialog__content {
    background: #f5f5f5;
		
		.list__divider {margin: -1px 0 0 15px;}
	}
	
	.dialog-content {
		
		.dialog-content__container {
			.flex-container;
			.flex1;

			@media @xs {.flex-column;}
			@media @xs {.flex-row;}
			
			.dialog-filters {
				@media @xs {display: none;}
				@media @sm-x {
			    -webkit-box-flex: 0;
			    -webkit-flex: 0 0 16em;
			    -ms-flex:     0 0 16em;
			    flex:         0 0 16em;
				}
			}
			
			.dialog-content__results {
				.flex1;
			}
		}
	}
}

.dialog--preview {
	
	.dialog__content {
		background: #f5f5f5;
		
		.preview--raw {

			@media @xs {padding: 1em;}
			@media @sm-x {padding: 2em;}
		}		
	}
}

.list-item--select {
	
	.list-item__row {
		
		& > div {vertical-align: top;}
		
		.list-item__left--check {width: 50px;}
		
		.list-item__img {
	    text-align: center;
	    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
			
			img {
				border-radius: 2px;
		    border: 1px solid white;
		    box-shadow: 0 1px 3px @black4;
			}
		}
		
		.list-item__content {
			padding-right: 1em;
			
			.btn--link {
				border: none;
				background: transparent;
				padding: 0;
				font-size: 11px;
				color: @black7;
				
				&:hover, &:focus {color: @teal;}
			}
			
			.fa-instagram {color: @instagram;}
			
			.list-item__date {
				font-size: @caption;
				
				.fa {
					font-size: 18px;
					position: relative;
					top: 2px;
					margin-right: 8px;
				}
			}
			
			.list-item__excerpt {
				font-size: @tiny;
			}
			
			.list-item__info {
				
				.fa {
					margin-right: 5px;
			    font-size: 14px;
			    position: relative;
			    top: 1px;
				}

				button {
				  margin-right: 12px;
				  
				  @media @ss {span {display: none;}}

				}
			}
		}
	}
	
	&.list-item--compact {
		
		.list-item__img {
			width: 75px;
	    padding-right: 15px;
	    
	    img {
		    width: 60px;
		    height: 60px; 
		  }
	  }
	}
	
	&.list-item--large {
		
		.list-item__img {
			width: 315px;
	    padding-right: 15px;

			@media @xs {
				display: block;
				padding-bottom: 0;
				width: 100%;
			}
	    
	    img {
		    width: 300px;
		    height: 300px; 
		    
		    @media @xs {
			    width: 100%;
			    height: auto;
		    }
		  }
		}
		
		.list-item__content {
			
			@media @xs {display: block;}
		}
	}
}

.select-all {
	display: table;
	table-layout: fixed;
	width: 100%;
	padding: 10px 15px;
		
	.select-all__row {
		.table-row;
		
		.select-all__cell {
			.table-cell;
			
			&.select-all__cell--left {}
			
			&.select-all__cell--right {
				text-align: right;
				
				@media @xs {width: 160px;}
				
				select {
					color: @secondary;
					border: 0;
					background: transparent;
					
					&:hover, &:focus {color: @accent;}
				}
				
				.btn {
					width: 34px;
					padding: 0 8px;
					height: 30px;
					margin-left: 10px;
					
					@media (min-width: 621px) {display: none;}
				}
			}
			
			.select-all__button {
				border: 0;
				background: transparent;
				padding: 0;
				font-size: @caption;
				color: @accent;
				
				@media @xs {
					padding: 2px;
					margin-left: 12px;
				}
				@media @sm-x {margin-left: 2em;}
				
				&:hover, &:focus {color: @accent;}
				
							
				&.select-all__button--active {
					color: @primary;
				}
				
				.fa {
					font-size: 14px;
					
					@media @sm-x {
						margin-right: 8px;
				    position: relative;
				    top: 1px;
					}
				}
				
				span {@media @xs {display: none;}}
			}
		}
	}
}