/*---------------------------------------------------------------------------
  >Forms
---------------------------------------------------------------------------*/

.list-search {
  padding: 1em 1.2em;
	width: 100%;
	border-width: 0 0 1px 0;
	border-style: solid;
	border-color: @gray9;
	.radius-top;
}

label {
	color: @gray2;
	.w600;

	.label-help-text {
  	text-transform: none;
		.fr;
  	.f12;
  	.w300;
		.gray5-txt;
		
		@media @ss {
			float: none;
			display: block;
		}
  }

	.icon-info {.f12;}
}

form {
	
	h2.section-title {
	  margin: 0 0 12px;
		.f26;
	  .w500;
	  .font2;
	  .navy-txt;
	}
	
	h3 {
	  margin: 0 0 10px;	
		.font2;
		.w500;
		.f18;
		.navy-txt;
	}
	
	.bullets {
	  padding-left: 20px;
		
		li {
			list-style: disc;
			margin: 8px 0;
		}
	}
	
	.form-control {border: 1px solid @gray7;}
	
	.form-group {
				
		&>label {
		display: table;
		.f11;
		.w700;
		.uppercase;
		}
		
		input[type="text"], input[type="email"], input[type="password"] {.full-width;}
		
	}
	
	.row, .settings-column {	

		&>div .checkbox, &>div .radio {
		  margin: 10px 0;			
		}
		
		.row {
			margin-left: -5px;
			margin-right: -5px;
			
			[class^="col"] {
			  padding-left: 5px;
			  padding-right: 5px;
			}
		}
	}
	
	.options {
		z-index: 5;
		
		.icon {
			color: @gray7;
			font-size: 16px;
			line-height: 0;
			margin-left: 5px;
			
			&:hover {.teal-txt;}
		}
	}
}

.alert-error {
  .white-txt;
  border-radius: 0;
  padding: 10px 15px;
  margin: 0;
}

.field-error {
	
	label {.red-txt;}
	
	.checkbox, .radio {
    background-color: #FFEDED;
    padding: 8px 10px;
    .radius3;
	}
	
	input, select {
		border-color: @red;
		background-color: #FFEDED;
	}
	:-moz-placeholder					  {color: @red;}
	::-moz-placeholder    			{color: @red;}
	:-ms-input-placeholder			{color: @red;}
	::-webkit-input-placeholder {color: @red;}
	
	textarea:-moz-placeholder  				  {color: @red;}
	textarea::-moz-placeholder  				{color: @red;}
	textarea:-ms-input-placeholder			{color: @red;}
	textarea::-webkit-input-placeholder {color: @red;}
}


input.search {height: 45px;}

.help-text {
	.gray5-txt;
	.f12;
}

.select-menu.form-control {height: 34px;}