React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

AuthenticatedComponent = require '../AuthenticatedComponent'
Drawer = require '../Sidebar/Drawer'
AppHeader = require '../Header/AppHeader'
SearchHeader = require './SearchHeader'
SearchSidebar = require './SearchSidebar'

EntryTile = require '../Entry/EntryTile'
AnswerTile = require '../AllAboutMe/AnswerTile'
EmptyTile = require '../Timeline/EmptyTile'
LoadMore = require '../Timeline/LoadMore'

Search = React.createClass
  displayName: 'Search'

  getInitialState: ->
    pager: {
        previous: @props.page?.previous
        next: @props.page?.next
        total_count: @props.page?.total_count
        offset: @props.page?.offset
        limit: @props.page?.limit
    }
    alert: undefined
  
  componentWillReceiveProps: (nextProps) ->
    @setState
      pager: {
        previous: nextProps.page?.previous
        next: nextProps.page?.next
        total_count: nextProps.page?.total_count
        offset: nextProps.page?.offset
        limit: nextProps.page?.limit
      }  
  
  onSearch: (options)->
    if options.q?
      if options.q != ""
        @.props.app.searchQueries.searchEntries(options, 'Search')
      else
        error = 
          user_message: "No search term entered"
        throw error
    else
      error = 
        user_message: "No search term entered"
      throw error
    # catch error
    #   @setState
    #     alert: error

  onLoadMore: ()->
    if @state.pager.next?
      @.app.searchQueries.getPage(@state.pager.next, @props.query, 'Search')
      
  onDismissAlert: ()->
    @setState
      alert: undefined

  renderAlert: ()->
    if @state.alert?
      return <div className="alert warning alert-dismissible"><button type="button" onClick={@onDismissAlert} className="close icon-times"></button>{@state.alert.user_message}</div>

  renderEntriesOrEmpty: ()->
    if @props.results?
      if !_.isEmpty(@props.results)
        output = @props.results.map (result)=>               
          if result.entry_type == "QA"
            return (
              <AnswerTile {...@props} key={result.id} answer={result} />
            )
          else
            return (
              <EntryTile {...@props} key={result.id} entry={result} />
            )
        return output
      else
        if @props.query?
          return @renderEmptySearchResults()
        else
          return @renderInitial()
    else
      return @renderInitial()

  renderEmptySearchResults: ()->
    return (
      <div className="entry tile-view white-bg no-entries">
        <div className="title">  
          No results returned for search term "{@props.query}"
        </div>
       </div>
    )

  renderInitial: ()->
    return (
      <div className="entry tile-view white-bg no-entries">
        <div className="title">  
          Search your journal entries for words or phrases.
        </div>
      </div>
    )  

  renderPager: ()->
    if @state.pager?.next? and @props.results?
      return (
        <div>
          <LoadMore pager={@state.pager} loaded={@props.results.length} onClick={@onLoadMore}/>
        </div>
      )
    else
      return undefined

  render: ()->
    <div>
      <Drawer />
      <div id="page">
        <AppHeader view_title={'Search'} {...@props}/>
        <SearchHeader ref="search_header" onSearch={@onSearch} {...@props} />
        <div id="content">
          <div className="container">  
            <div id="timeline-column" className="pull-left right-padding">
              {
                @renderAlert()
              }
              {
                @renderEntriesOrEmpty()
              }
              {
                @renderPager()
              }
            </div>
          <SearchSidebar {...@props} onLoadMore={@onLoadMore} />
          </div>
        </div>
      </div>
    </div>

SearchContainer = Marty.createContainer Search,
  listenTo: ['searchStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      search_query = @props.location.query.q
      fetchState = {
        firstname: @.props.app.authStore.getFirstNameOrUsername()
        user: @props.app.authStore.fetchUser()
        error: @props.app.searchStore.getError()
        page: @props.app.pageStore.getPage('Search')
        query: @props.app.searchStore.getQuery() || search_query
        results: @props.app.searchStore.getResults(search_query)
        searchId: 1
      }
      return fetchState
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Search {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Search {...props} />
  failed: (error)->
    console.log error
    return <div>Search Error</div>

module.exports = SearchContainer