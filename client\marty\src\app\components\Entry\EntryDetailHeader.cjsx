React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
classnames = require 'classnames'

Link = require('react-router').Link
AutoAffix = require('react-overlays').AutoAffix

DateRangeHeaderControl = require '../Header/Controls/DateRangeHeaderControl'
JournalEntryPagerControl = require '../Header/Controls/JournalEntryPagerControl'

EntryDetailHeader = React.createClass
  displayName: 'EntryDetailHeader'

  renderNewEntry: ()->
    if @props.journal?
      return (
        <Link to={"/entry/create/" + @props.journal?.id} className="btn btn-new pull-right btn-navy">New Entry</Link>
      )
    else
      return undefined

  renderComments: ()->
    if @props.invitations? or @props.entry?.isOwnedByMe
      if 'add_entry_comments' in (@props.invitations?.granted_permissions || []) or @props.entry?.isOwnedByMe
        if @props.entry? and @props.entry?.comments?.length > 0 or @props.sharedComments?.length > 0
            iconClassname = classnames("icon","icon-comment-full")
        else
            iconClassname = classnames("icon","icon-comment")
        
        <a className="button" onClick={@props.showComments}><i className={iconClassname}></i></a>
      else
        iconClassname = classnames("icon","icon-comment")
        <a className="button" onClick={@props.showComments}><i className={iconClassname}></i></a>
  
  renderSharing: ()->
    if @props.entry?.isOwnedByMe
      <a onClick={@props.toggleShareModal} className="button"><i className="icon icon-share"></i></a>

  render: ()->
    <AutoAffix offsetTop={0} container={document.body}>
      <div id="nav_crumb" className="gray">
        <div className="container">
          <div id="crumb-bar" className="pull-left">
            <DateRangeHeaderControl widgetCallback={@props.widgetCallback} calendar_id="EntryDetail" {...@props}/>
            <div id="options" className="pull-right">
              <JournalEntryPagerControl {...@props} />  
              <div className="sharing-buttons fr">
                {
                  @renderComments()
                }
                {
                  @renderSharing()
                }        
              </div>
            </div>
          </div>
          <div id="tools" className="pull-right text-right">
            {
              @renderNewEntry()
            }
          </div>
        </div>
      </div>
    </AutoAffix>
  
EntryDetailHeaderContainer = Marty.createContainer EntryDetailHeader,
  listenTo: ['journalStore', 'entryStore']

  fetch:()->
    if @props.entry?.isOwnedByMe? and not @props.entry?.isOwnedByMe
        fetchState = {}
        if @props.user?.sharing
          fetchState = 
            invitations: @props.app.invitationStore.getInvitationForEntry(@props.entry?.id)
            sharedComments: @props.app.commentStore.getSharedCommentsForEntry(@props.entry?.id)
        return fetchState
    else
      return {}
  done: (results)->
    props = _.extend {}, @props. results
    return <EntryDetailHeader {...props} />
  pending: (fetches)->
    props = _.extend {}, @props. fetches
    return <EntryDetailHeader {...props} />
  failed: (error)->
    console.log error
    return <div>Error</div>

module.exports = EntryDetailHeader