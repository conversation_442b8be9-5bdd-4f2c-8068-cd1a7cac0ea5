/*---------------------------------------------------------------------------
  >Bookbuilder
---------------------------------------------------------------------------*/

#bookbuilder-column {
	width: 100%;
	
	.table-bookbuilder {
		
		thead {
			
			th:first-child {
			  width: 42px;
			}
		}
		
		.col-date, .col-modified {
			width: 130px;
		}
		
		.col-delete {
		  width: 44px;
		}
	}	
}

/*********** >Build Process ***********/

#build-column {
  padding: 40px;
  
  @media @ss {padding: 1em;}
  
	label {width: 100%;}
	
	.radio li, .checkbox li {
	  margin: 8px 0;		
	}
	
	.radio label, .checkbox label {
		margin-bottom: 4px;		
	}
	
	.date-field {
	  padding: 4px 8px;
	  border-radius: 3px;
	  margin: 8px 0;
	  width: 130px;
	}
	
	.checkout .select-menu {
	  height: 34px;
	}

/*
	.row {
		
		.row {
			margin-left: -5px;
			margin-right: -5px;
			
			[class^="col"] {
			  padding-left: 5px;
			  padding-right: 5px;
			}
		}

	}*/
	
	.description {
		.gray5-txt;
		font-style: italic;	
		
		@media @sm-lg {.f12;}
		@media @lg-x {.f14;}
	}
	
	.book-estimate {
	  .white-txt;
	  border: none;
	  display: table;
	  width: 100%;
	  
		h3 {
			.white-txt;
		  margin: 0 0 5px;
		}
	}

	.book-details {
		
		li {
			list-style: none;
			padding: 3px 0;
		}
		
		.icon-check {
			.teal-txt;
		  margin-right: 5px;
		}
		
		.title {
		  .f17;
		}
	}
}

/*********** >Steps ***********/

#steps {
	.fl;
  width: 100%;
  
//   @media @ss {display: none;}
  
	.title {
		.f19;
		.w700;
		
		@media @xs {font-size: 11px;}
	}
	
	.border-right {
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    background-clip: padding-box;
	}
  
	li {display: inline-block;}
	
	.active, .active:hover, .active:focus {.teal-bg;}
	
	.icon-check {.teal-txt;}
	
	a {
		color: @gray6;
		background: @gray10;
		padding-top: 15px;
		padding-bottom: 15px;

		@media @ss {display: none;}		

		@media @xs {
	    width: 25%;
	    margin: 0;
	    float: left;
	    padding: 6px 10px;
		}

		&:hover {
		  .white-bg;
		  
			.icon-angle-right {
			  filter: alpha(opacity=100);
			  -moz-opacity: 1;
			  opacity: 1;
			}
		}

		.icon	{
			font-size: 25px;
			top: 1px;
			right: -5px;
			.gray3-txt;
		}
	}
	
	a.active, a.active .icon {.white-txt;}
  
	.icon-check {
	  filter: alpha(opacity=100);
	  -moz-opacity: 1;
	  opacity: 1;
	  color: @green !important;
	}
}

/*********** >Checkout ***********/

#checkout-table {
	
	th {		
		text-align: left;
	  padding: 5px 10px;
  }

	th:first-child {
		padding-left: 8px;
	}
	
	tbody td {
	  padding: 10px;
	}
	
	.small td {
	  .f12;
	  padding: 7px 10px;
	  background-color: rgba(0, 0, 0, 0.017);
	  border-bottom: 1px solid @gray9;
	}

	.price {
	  text-align: right;
	  width: 110px;
	}

	.quantity {
		text-align: center;
	  width: 90px;
	  
	  @media @ss {width: 50px;}
	}

	.total {
	  .f18;
	  background-color: #FFF9CD;
	}
}