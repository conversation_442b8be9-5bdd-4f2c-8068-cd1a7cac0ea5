React = require 'react'

Dropdown = require('react-bootstrap').Dropdown
  
CommentOptionMenu = React.createClass
  displayName: 'CommentOptionMenu'

  onEdit: (e)->
    if @props.onEdit?
      @props.onEdit()

  onDelete: (e)->
    if @props.onDelete?
      @props.onDelete(@props.comment)

  onTogglePublish: (e)->
    if @props.onTogglePublish?
      @props.onTogglePublish(@props.comment)

  isEntryOwner: ()->
    return @props.entry?.isOwnedByMe
  
  isCommentOwner: ()->
    return @props.comment.user is @props.app.authStore.getMyPublicUser() || @props.comment.user is @props.app.authStore.getUserURI()

  getOptions: ()->
    options = []
    
    if @isCommentOwner()
      options.push <li key={@props.comment.id + '-edit'}><a onClick={@onEdit}>Edit</a></li>
      options.push <li key={@props.comment.id + '-delete'}><a onClick={@onDelete}>Delete</a></li>
    if @isEntryOwner()
      if @isCommentOwner()
        options.push <li key={@props.comment.id + '-divider'} role="separator" className="divider"></li>
      else
        options.push <li key={@props.comment.id + '-delete'}><a onClick={@onDelete}>Delete</a></li>
        options.push <li key={@props.comment.id + '-divider'} role="separator" className="divider"></li>
      options.push @renderPublishToggle()
    return options

  renderPublishToggle: ()->
    if @props.comment?.publishable
      return <li key={@props.comment.id + '-publish'}><a onClick={@onTogglePublish} title={'Click to disable'}>Book Publishing Enabled</a></li>
    else
      return <li key={@props.comment.id + '-publish'}><a onClick={@onTogglePublish} title={'Click to enable'}>Book Publishing Disabled</a></li>

  renderMenuOptions: ()->
    return @getOptions()

  render: ()->
    if (@isEntryOwner() or @isCommentOwner()) and not @props.isEditable
      <Dropdown id={'CommentOptionMenu'} className="pull-right" componentClass={'div'}>
        <Dropdown.Toggle noCaret={true} useAnchor={true} className="icon icon-options">
        </Dropdown.Toggle>
        <Dropdown.Menu className='dropdown-pull-right dropdown-default' style={minWidth: '100px'}>
          {   
            @renderMenuOptions()
          }
        </Dropdown.Menu>
      </Dropdown>
    else
      <span></span>

module.exports = CommentOptionMenu