React = require 'react'
Marty = require 'marty'

AllAboutMeEntryHeader = React.createClass
  displayName: 'EntryHeader'
  mixins: []
  
  getInitialState: ->
    showTagsModal: false
  
  onCancel: (e)->
    if @props.onCancel?
      @props.onCancel()

  onSave: (e)->
    if @props.onSave?
      @props.onSave()
    e.stopPropagation()
    e.preventDefault()

  onSaveAndClose: (e)->
    if @props.onSave?
      @props.onSave(true)
    e.stopPropagation()
    e.preventDefault()

  # TODO Add hidden fields that are used at lower media queries to truncate the day and month names
  render: ()->
    <div id="action-nav" className="teal-bg">
      <div className="container">
        <div className="pull-left">
        </div>
        <div className="pull-right ">
          <ul className="nav navbar-nav">
            <li>
              <a onClick={@onCancel} className="btn-flat">Cancel</a>
            </li>
            <li>
              <a onClick={@onSave} className="btn-flat">Save</a>
            </li>
            <li>
              <a onClick={@onSaveAndClose} className="btn-flat">Save & Close</a>
            </li>
          </ul>
        </div>
      </div>
    </div>

module.exports = AllAboutMeEntryHeader


