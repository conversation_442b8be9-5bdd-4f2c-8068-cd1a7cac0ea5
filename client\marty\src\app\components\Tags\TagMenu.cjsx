React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Link = require('react-router').Link

ButtonToolbar = require('react-bootstrap').ButtonToolbar
MenuItem = require('react-bootstrap').MenuItem
Dropdown = require('react-bootstrap').Dropdown
Button = require('react-bootstrap').Button

classnames = require 'classnames'

utils = require('react-bootstrap').utils.bootstrapUtils
utils.addStyle Dropdown, ['nein']

TagMenuOption = React.createClass
  displayName: 'TagMenuOption'

  renderLink: ()->
    if @props.tag?.id?
      <Link to={"/tag/"+ @props.tag.id}>{@props.tag.name}</Link>
  
  render: ()->
    <span>
    {
      @renderLink()
    }
    </span>
    
TagMenuOptionContainer = Marty.createContainer TagMenuOption,
  listenTo: ['tagsStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      if @props.entry_tag?
        return {
          tag: @props.app.tagsStore.getTagByURI(@props.entry_tag)
        }
      else 
        return {}
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <TagMenuOption {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <TagMenuOption {...props} />
  failed: (error)->
    console.log error
    return <div>Error</div>

TagMenu = React.createClass
  displayName: 'TagMenu'

  getInitialState: ->
    open: false

  setDropdownState: ()->
    @setState
      open: !@state.open

  render: ()->
    <Dropdown id={'TagsMenu'} className="" componentClass={'div'} dropup={true} onToggle={@setDropdownState}>
      <Dropdown.Toggle noCaret={true} useAnchor={true} className="button" title={'Tags'}>
        <i className="icon icon-tag"></i>
        {
          if @props.entry?.tags?
            if @props.entry.tags.length >= 99
              "99+"
            else
              @props.entry.tags.length
        }
      </Dropdown.Toggle>
      <Dropdown.Menu className='dropdown-swing-left dropdown-default'>
        <li className="heading">Tags</li>
        {
          if @props.entry?.tags? and @state.open
            @props.entry.tags.map (tag)=>
              return (
                <li key={tag}>
                  <TagMenuOptionContainer entry_tag={tag} {...@props} />
                </li>
              )
         }
      </Dropdown.Menu>
    </Dropdown>
    

TagMenuContainer = Marty.createContainer TagMenu,
  listenTo: ['tagsStore']

  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <TagMenu {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <TagMenu {...props}/>
  failed: (error)->
    console.log error
    return <div>Error</div>

module.exports = TagMenuContainer