React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

JournalEmail = require '../Journal/JournalEmail'
JournalEmailModal = require '../Journal/JournalEmailModal'

EmailToJRNLSettings = React.createClass
  displayName: 'EmailToJRNLSettings'

  getInitialState: ->
    success: @props.success
    showConfirmation: false
    showSuccess: false
    journal: undefined

  showConfirmation: (journal)->
    @setState
      showConfirmation: true
      journal: journal

  hideConfirmation: ()->
    @setState
      showConfirmation: false
      journal: undefined

  loadMore: ()->
    @props.app.journalQueries.getPage(@props.page.next, 'EmailtoJRNLSettings')
  
  render: ()->
    <div role="tabpanel" className="tab-pane" id="emailToJRNL-settings">
      <div className="settings-wrapper">
        <h2 className="section-title">Email to JRNL Settings</h2>
        {
          if @props.journals
            @props.journals.map (journal)=>
              if journal.journal_type != "AllAboutMe"
                <div key={"journal"+ journal.id}>
                  <JournalEmail as_panel={true} object={journal} {...@props} generate={@showConfirmation}/>
                  {
                    if @state.showConfirmation and journal.id is @state.journal.id
                      <JournalEmailModal show={@state.showConfirmation} onHide={@hideConfirmation} object={journal} {...@props} /> 
                  }
                </div>
        }
        {
          if @props.page?.next?
            <button onClick={@loadMore}>Load More</button>
        }
      </div>
    </div>

EmailToJRNLSettingsContainer = Marty.createContainer EmailToJRNLSettings,
  listenTo: ['authStore', 'journalStore', 'pageStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        journals: @props.app.journalStore.getJournals('EmailtoJRNLSettings')
        page: @props.app.pageStore.getPage('EmailtoJRNLSettings')
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <EmailToJRNLSettings {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    # return <EmailToJRNLSettings {...props} />
    return <div></div>
  failed: (error)->
    console.log error
    return <div>EmailToJRNLSettings error</div>

module.exports = EmailToJRNLSettingsContainer