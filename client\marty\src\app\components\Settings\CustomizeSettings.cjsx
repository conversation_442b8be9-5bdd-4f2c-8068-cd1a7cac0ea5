React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

MediaManagerModal = require '../MediaManager/Modals/MediaManagerModal'
WallpaperMenu = require './WallpaperMenu'

CustomizeSettings = React.createClass
  displayName: 'Customize Settings'

  getInitialState: ->
    showMediaManager: false

  openMediaManager: ()->
    @setState
      showMediaManager: true

  closeMediaManager: ()->
    @setState
      showMediaManager: false

  onSelectWallpaper: ()->
    @openMediaManager()

  onRemoveWallpaper: ()->
    @props.app.userActionCreators.removeWallpaper()

  updateWallpaper: (wallpaper)->
    if wallpaper?
      @props.app.userActionCreators.updateWallpaper(wallpaper)
      @closeMediaManager()

  render: ()->
    <div role="tabpanel" className="tab-pane" id="customize-settings">
      <MediaManagerModal mediaType={'wallpaper'} show={@state.showMediaManager} onHide={@closeMediaManager} onWallpaperSelected={@updateWallpaper} {...@props} />
      <div className="settings-wrapper">
        <h2 className="section-title">Wallpaper</h2>
        <label>Current Wallpaper</label>
        {
          if not _.isEmpty @props.user?.wallpaper_image_thumbnail_url
            <img className="image-library-sample" src={@props.user?.wallpaper_image_thumbnail_url} />
          else
            <div>
              <span>None</span>
            </div>
        }
        <WallpaperMenu onSelectWallpaper={@onSelectWallpaper} onRemoveWallpaper={@onRemoveWallpaper} {...@props} />
      </div>
    </div>

CustomizeSettingsContainer = Marty.createContainer CustomizeSettings,
  listenTo: ['authStore']

  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <CustomizeSettings {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <CustomizeSettings {...props}/>
  failed: (error)->
    console.log error
    return <div>Profile Settings Error</div>

module.exports = CustomizeSettingsContainer