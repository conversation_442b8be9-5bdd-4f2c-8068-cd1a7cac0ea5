React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
classnames = require('classnames')

ListItem = require '../SocialConnect/SocialConnectListItem'
AccountSettingsModal = require '../SocialConnect/AccountSettingsModal'
settings = require '../../settings'

SocialConnectSettings = React.createClass
  displayName: 'SocialConnectSettings'

  getInitialState: ->
    showAccountSettings: false
  
  componentDidMount: ->
    window.addEventListener 'message', @receiveMessage, false

  componentWillUnmount: ->
    window.removeEventListener 'message', @receiveMessage
  
  receiveMessage: (event)->
    if event.origin is settings.ALLOWED_ORIGIN
      if event.data.code?
        if event.data.error?
          console.log event.data
        else
          @app.socialConnectActionCreators.setInstagramCode(event.data.code)
  
  requestMediaImport: ()->
    @app.socialConnectActionCreators.startMediaImport()
  
  openAccountSettings: ()->
    @setState
      showAccountSettings: true
  
  closeAccountSettings: ()->
    @setState
      showAccountSettings: false

  renderConnectedAccounts: ()->
    if !(_.isEmpty @props.instagram_access_token)
      [
        <div key={'1'} className="list__divider">Connected Accounts</div>,
        <div onClick={@openAccountSettings} key={'2'}>
          <ListItem 
            avatarURL={@props.instagram_access_token.ig_profile_picture}
            username={@props.instagram_access_token.ig_username} 
            title="Instagram" 
            connectedStatus={!(_.isEmpty @props.instagram_access_token)}
            onClick={@openAccountSettings}
          />
        </div>
      ]
      
  render: ()->
    <div role="tabpanel" className="tab-pane active" id="social-connect-settings">
      <div className="page list list-link">
        <div className="list__divider">Available Accounts</div>                          
        <ListItem social="Instagram" title="Instagram" connectedStatus={!(_.isEmpty @props.instagram_access_token)} titleURL={@props.instagram_authorization_url?.url} />
        {
          @renderConnectedAccounts()
        }
      </div>
      {
        if @state.showAccountSettings
          <AccountSettingsModal show={@state.showAccountSettings} onHide={@closeAccountSettings} {...@props} />
      }
    </div>

SocialConnectSettingsContainer = Marty.createContainer SocialConnectSettings,
  listenTo: ['authStore', 'socialStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      user: @props.app.authStore.getUser()
      instagram_access_token : @props.app.socialStore.getInstagramAccessToken()
      instagram_authorization_url: @props.app.socialStore.getInstagramAuthorizationURL()
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <SocialConnectSettings {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <SocialConnectSettings {...props}/>
  failed: (error)->
    console.log error
    return <div>Social Connect Settings Error</div>

module.exports = SocialConnectSettingsContainer