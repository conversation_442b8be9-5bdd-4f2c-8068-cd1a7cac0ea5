Marty = require 'marty'
_ = require 'underscore'
allaboutmeConstants = require '../constants/AllAboutMeConstants'

AllAboutMeActionCreators = Marty.createActionCreators

  saveAnswer: (newAnswer, shouldClose, id, options, query, entry_images)->
    return @.app.entryHttpAPI.saveEntry(newAnswer)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @.dispatch allaboutmeConstants.ANSWER_SAVED, success, shouldClose, id, options, query
      if not _.isEmpty entry_images
        @app.socialConnectActionCreators.updateEntryImagesWithEntry success.id, entry_images
    .catch (error)=>
      console.log error
      @.dispatch allaboutmeConstants.ALLABOUTME_ERROR, error

  updateAnswer: (newAnswer, shouldClose, id, options, query, entry_images)->
    return @.app.entryHttpAPI.updateEntry(newAnswer)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @.dispatch allaboutmeConstants.ANSWER_UPDATED, success, shouldClose, id, options, query
      if not _.isEmpty entry_images
        @app.socialConnectActionCreators.updateEntryImagesWithEntry success.id, entry_images
    .catch (error)=>
      console.log error
      @.dispatch allaboutmeConstants.ALLABOUTME_ERROR, error

  updateRandom: ()->
    @.dispatch allaboutmeConstants.UPDATE_RANDOM_QUESTION

module.exports = AllAboutMeActionCreators