/*---------------------------------------------------------------------------
  >Drawer
---------------------------------------------------------------------------*/

#drawer {
  vertical-align: top;
  box-sizing: border-box;
  height: 100%;
  z-index: 300;
  .white-bg;
  .shadow;

  @media @ss-md {
	  min-width: 50px; 
	  width: 50px; 
	  overflow: hidden;
	}  
  @media @md-x {
	  min-width: 180px; 
	  width: 180px;
	}
	
	.logo {
		display: block;
		
	  @media @ss-md {
		  position: fixed; 
		  width: 50px; 
		  padding: 15px 9px; 
		  height: 60px; 
		  text-align: center; 
		  margin-bottom: 6px;
		}
	  @media @md-x {
		  width: 160px; 
		  margin-bottom: 8px; 
		  padding: 15px 20px; 
		  height: 59px;
		}
	
		img {
			width: auto;
			
		  @media @ss-md {
			  height: 25px; 
			  top: 3px; 
			  left: -1px;
			}
		  @media @md-x {
			  height: 30px; 
			  top: -1px;
			}
		}
	}
	
	ul {		
	  @media @ss-md {
		  position: fixed; 
		  top: 66px; 
		  left: 16px;
		}
	  @media @md-x {top: 67px;}
		
		a {
	  	display: block;
	  	.w400;
	  	.navy-txt;
	  	.hover-teal-txt;
	  	
		  @media @ss-md {
			  width: 100%; 
			  text-align: center; 
			  padding: 10px 0;
			}
		  @media @md-x {
			  font-size: 14px; 
			  padding: 10px 20px;
			}
		  
		  i {
			  @media @md-x {
				  width: 20px; 
				  text-align: center; 
				  margin-right: 5px;
			  }
			}
  	}
  	
  	img {
		  width: 18px;
		  height: 18px;
		  
		  @media @md-x {
			  position: relative; 
			  top: -1px; 
			  margin-right: 8px;
			}
  	}
  	
  	.active a {.teal-txt;}
  }
	
	.logo, ul {
		@media @md-x {position: fixed;}
	} 
	 
	.jrnl-version {
	  position: absolute;
	  bottom: 10px;
	  left: 0;
	  right: 0;
	  width: 100%;
	  text-align: center;
	  .gray4-txt;
	  .f11;
	}
}