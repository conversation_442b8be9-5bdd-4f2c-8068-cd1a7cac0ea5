React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
classnames = require 'classnames'

FormData = require 'react-form-data'

CommentButton = (props)->
  CommentButton.displayName = 'CommentButton'
  if props.type is 'one_on_one'
      type = '1on1'
    else
      type = props.type
  classNames = classnames('btn','btn-small','fr', "btn-color-#{type}")
  
  switch props.type
    when 'personal'
      <a onClick={props.onPostComment} className={classNames}>Post Personal Comment</a>
    when 'group'
      <a onClick={props.onPostComment} className={classNames}>Post Group Comment</a>
    when 'one_on_one'
      <a onClick={props.onPostComment} className={classNames}>Post 1 on 1 Comment</a>
    else
      <a></a>

AddComment = React.createClass
  displayName: 'AddComment'
  mixins: [FormData]

  getInitialState: ->
    content: undefined
    contentFocused: false

  onPostComment: (e)->
    content = @formData.content
    if content?
      if content.length > 0
        content = @formData.content.replace(/\r?\n/g, '<br />')

    comment = 
      content: content
      entry: @props.entry.id
      display_channel: @props.type
    if @props.type is 'one_on_one'
      comment.one_on_one_user = @props.channelUser.id
      
    if comment.content? and comment.content != ""
      if @props.postComment?
        @props.postComment(comment)
        @formData.content = ''
        @setState
          content: ''
          contentFocused: false
      
  onCancel:()->
    @.formData = {}
    @setState
      content: ''
      contentFocused: false

  onChange: (e)->
    @setState
      content: e.target.value

  onContentFocus: (e)->
    @setState
      contentFocused: true
    if @props.onAddCommentFocused?
      @props.onAddCommentFocused()

  render: ()->
    <div className="add-comment-box" onChange={@updateFormData}>
      <textarea name='content' rows="1" placeholder="Comment on this entry" value={@state.content} onChange={@onChange} onFocus={@onContentFocus} style={if @state.contentFocused then {minHeight: '120px'} else {}}></textarea>
      <CommentButton onPostComment={@onPostComment} {...@props} />
    </div>

module.exports = AddComment