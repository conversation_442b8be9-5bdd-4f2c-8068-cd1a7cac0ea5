React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'

Drawer = require '../Sidebar/Drawer'
Sidebar = require '../Sidebar/SidebarLayout'
CommentWidget = require '../Sidebar/Widgets/Comments/CommentWidget'

AppHeader = require '../Header/AppHeader'
AllAboutMeEntryDetailHeader = require './AllAboutMeEntryDetailHeader'
JournalsAndEventsModal = require '../Modals/JournalsAndEventsModal'
AnswerTile = require './AnswerTile'
InvitationModal = require '../Modals/Invitation/InvitationModal'

AllAboutMeEntryDetail = React.createClass
  displayName: 'AllAboutMeEntry Detail'

  getInitialState: ->
    showShareModal: false
    showContactModal: false
    showComments: @props.defaultShowComments || false

  openShareModal: ()->
    @setState
      showShareModal: true

  closeShareModal: ()->
    @setState
      showShareModal: false

  openComments: ()->
    @setState
      showComments: true

  closeComments: ()->
    @setState
      showComments: false

  openContactModal: (entry)->
    @setState
      showContactModal: true

  closeContactModal: ()->
    @setState
      showContactModal: false

  toggleComments: ()->
    @setState
      showComments: !@state.showComments

  toggleShareModal: ()->
    @setState
      showShareModal: !@state.showShareModal

  onViewModeChange: (view_mode)->
    switch view_mode
      when 'day'
        if @props.journalId?
          @.app.entryActionCreators.updateViewMode({view_mode: view_mode}, 'Journal' + @props.journalId)  
        else if @props.tagId?
          @.app.entryActionCreators.updateViewMode({view_mode: view_mode}, 'Tag' + @props.tagId)
        else
          @.app.entryActionCreators.updateViewMode({view_mode: view_mode}, 'AAMDetail')
      when 'month','year'
        options = @buildOptions @props.min_date, view_mode    
        if @props.journalId?
          @.app.entryActionCreators.updateEntries(options, 'Journal' + @props.journalId)
          @props.history.pushState null, '/journal/'+ @props.journalId 
        else if @props.tagId?
          @.app.entryActionCreators.updateEntries(options, 'Tag' + @props.tagId)
          @props.history.pushState null, '/tag/' + @props.tagId
        else
          @.app.entryActionCreators.updateEntries(options, 'Timeline')
          @props.history.pushState null, '/'

  onWidgetCallback: (widget, value, view_mode)->
    if widget == 'calendar'
      if not view_mode?
        view_mode = 'day'
      options = @buildOptions(value, view_mode)
      if @props.journalId?
          @.app.entryActionCreators.updateEntries(options, 'Journal' + @props.journalId)
          @props.history.pushState null, '/journal/'+ @props.journalId
      else if @props.tagId?
        @.app.entryActionCreators.updateEntries(options, 'Tag' + @props.tagId)
        @props.history.pushState null, '/tag/' + @props.tagId
      else
        @.app.entryActionCreators.updateEntries(options, 'Timeline')
        @props.history.pushState null, '/'

  buildOptions: (value, view_mode)->
    filter_by = @.app.entryStore.getFilterBy()
    options = 
      order_by: @props.order_by
      min_date: moment.utc(value).startOf(view_mode).format('YYYY-MM-DD'+'T'+'00:00:00')
      max_date: moment.utc(value).endOf(view_mode).format('YYYY-MM-DD'+'T'+'23:59:59')
      filter_by: filter_by
      view_mode: view_mode

    return options

  renderAllAboutMeEntry: ()->
    if @props.answer?
      return (
        <AnswerTile {...@props} showComments={@toggleComments} openContactModal={@openContactModal} entry={@props.answer}/>
      )
    else
      return undefined

  render: ()->
    view_title = @props.journal?.title
    <div>
      <Drawer />
      <div id="page">
        <AppHeader username={@props.username} view_title={view_title} view_title_is_editable={false} {...@props}/>
        <AllAboutMeEntryDetailHeader onViewModeChange={@onViewModeChange} toggleShareModal={@toggleShareModal} showComments={@toggleComments} widgetCallback={@onWidgetCallback} {...@props}/>
        {
          if @state.showShareModal
            <InvitationModal onHide={@closeShareModal} show={@state.showShareModal} {...@props} initialHeader={'Share Entry'}/>
        }
        <div id="content">
          <div className="container">  
            <div id="timeline-column" className="pull-left right-padding">
              {
                @renderAllAboutMeEntry()
              }
            </div>
          {
            if @state.showComments and @props.entry?
              <Sidebar {...@props}>
                <CommentWidget onHide={@closeComments} onShareModal={@openShareModal} {...@props}/>
              </Sidebar>
            else
              <Sidebar widgetCallback={@onWidgetCallback} {...@props}/>  
          }
          </div>
        </div>
      </div>
    </div>
          

AllAboutMeEntryDetailContainer = Marty.createContainer AllAboutMeEntryDetail,
  listenTo: ['entryStore', 'journalStore','calendarStore','allaboutmeStore']

  fetch: ()->
    entryId = @props.params.entryId
    if entryId?
      return {
        answer: @.props.app.entryStore.getEntryById(entryId)
        username: @.props.app.authStore.getUsername()
        firstname: @.props.app.authStore.getFirstNameOrUsername()
        journal: @.props.app.journalStore.getAllAboutMeJournal()
        show_comments: false
        defaultShowComments: @props.location.query.comments? || false
        view_mode: 'day'
        journalId: @props.location.query.journalId
        tagId: @props.location.query.tagId
        searchId: @props.location.query.searchId
        aamId: @props.location.query.aamId
        entryId: entryId
        user: @.props.app.authStore.fetchUser()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    props['entry'] = props['answer']
    return <AllAboutMeEntryDetail {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <AllAboutMeEntryDetail {...props} />
  failed: (error)->
    console.log error
    return <div>AllAboutMeEntry Error</div>

module.exports = AllAboutMeEntryDetailContainer