/*---------------------------------------------------------------------------
  >Carousel Modals
---------------------------------------------------------------------------*/

.modal-carousels {

	.modal-header {
		border: none !important;
	}
	
	.description {
		
		ul, ol {padding-left: 20px;}
		
		ul {
			
			li {list-style: disc;}
		}
		
		ol {
			
			li {list-style: decimal;}
		}
	}
	
	.carousel-indicators {
		top: 17px;
    left: initial;
    right: 30px;
    width: 100%;
    text-align: right;
		
		li {
	    margin-left: 2px;
	    margin-right: 2px;
	    border: none !important;
	    background: rgba(0, 132, 158, 0.5); 
		}
		
		.active {.white-bg;}
	}
	
	.carousel-control {
    position: relative;
    opacity: 1;
    background-image: none !important;
    .teal-bg;
	}
	
	.carousel-inner {
		background-color: white;
		border-radius: 0 0 3px 3px;
		
		.carousel_video {
			@media @ss {height: 155px !important;}
			@media @xs {height: 194px;}
			@media @sm {height: 344px;}
			@media @md {height: 452px;}
			@media @lg-x {height: 539px;}
		}
		
		h2 {
			margin: 0 0 15px;
			letter-spacing: -.5px;
			font-size: 2em;
			line-height: 120%;
			.teal-txt;
			.font2;
		}
	}
	
	.carousel-fade {
	  .carousel-inner {
	    .item {
	      transition-property: opacity;
	    }
	    
	    .item,
	    .active.left,
	    .active.right {
	      opacity: 0;
	    }
	
	    .active,
	    .next.left,
	    .prev.right {
	      opacity: 1;
	    }
	
	    .next,
	    .prev,
	    .active.left,
	    .active.right {
	      left: 0;
	      transform: translate3d(0, 0, 0);
	    }
	  }
	
	  .carousel-control {z-index: 2;}
	}
}


/*---------------------------------------------------------------------------
  >Queries
---------------------------------------------------------------------------*/

/* SM */ @media screen and (max-width: 1200px) and (min-width: 768px) {

	.modal-carousels {
		
		.modal-dialog {
			width: 80% !important;
		}
	}	
	
}

/* LG + */ @media screen and (min-width: 1200px) {
	
	.modal-carousels {
		
		.modal-dialog {
			min-width: 960px;	
			max-width: 960px;			
		}
	}
	
}