React = require 'react'
moment = require 'moment'
Link = require('react-router').Link

EmptyTile = React.createClass
  displayName: 'EmptyTile'

  getPeriod: ()->
    switch @props.view_mode
      when 'day'
        period = moment.utc(@props.min_date)
        return period.format("MMMM D, YYYY")
      when 'month'
        period = moment.utc(@props.min_date)
        return period.format("MMMM YYYY")
      when 'year'
        period = moment.utc(@props.min_date)
        return period.format("YYYY")

  renderCreateNewEntryButton: ()->
    if @props.journalId?
      <Link to={"/entry/journal/create/" + @props.journalId} query={entry_date: @props.min_date, tag_id: @props.tagId} className="btn btn-navy btn-medium">New Entry for {@getPeriod()}</Link>
    else
      <Link to={"/entry/create/"} query={entry_date: @props.min_date, tag_id: @props.tagId} className="btn btn-navy btn-medium">New Entry for {@getPeriod()}</Link>

  render: ()->
    <div className="entry tile-view white-bg no-entries">
      <div className="title">  
        There are no journal entries for {@getPeriod()}.
      </div>
      {
        if @props.view_mode == 'day'
          @renderCreateNewEntryButton()
      }
    </div>

module.exports = EmptyTile