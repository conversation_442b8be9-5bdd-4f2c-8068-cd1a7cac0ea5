Marty = require 'marty'
journalConstants = require '../constants/JournalConstants'

JournalActionCreators = Marty.createActionCreators
  updateJournal: (journal)->
    return @.app.journalHttpAPI.updateJournal(journal)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (success)=>
      @.dispatch journalConstants.JOURNAL_UPDATED, success
    .catch (error)=>
      console.log error
      @.dispatch journalConstants.JOURNALS_ERROR, error
  
  deleteJournal: (journal)->
    return @.app.journalHttpAPI.deleteJournal(journal)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (success)=>
      @.dispatch journalConstants.JOURNAL_DELETED, journal
    .catch (error)=>
      console.log error
      @.dispatch journalConstants.JOURNALS_ERROR, error

  createJournal: (journal)->
    return @.app.journalHttpAPI.createJournal(journal)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (success)=>
      @.dispatch journalConstants.JOURNAL_CREATED, success
    .catch (error)=>
      console.log error
      @.dispatch journalConstants.JOURNALS_ERROR, error
      
  dismissError: ()->
    @.dispatch journalConstants.DISMISS_ERROR

module.exports = JournalActionCreators