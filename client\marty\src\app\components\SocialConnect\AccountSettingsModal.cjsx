React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'

Modal = require('react-bootstrap').Modal

AccountSettingsModal = React.createClass
  displayName: 'AccountSettingsModal'

  updateAccount: ()->
    console.log 'Update Account'

  removeAccount: ()->
    @props.app.socialConnectActionCreators.deleteAccessToken(@props.instagram_access_token.resource_uri)
    @props.onHide()

  updateFrequency: (data)->
    @props.app.socialConnectActionCreators.updateFrequency(@props.instagram_access_token, data.target.value)

  render: ()->
    <Modal backdrop={'static'} enforceFocus={false} id="account_settings" {...@props}>
      <div className="modal-header teal-bg">
        <div className="row">
          <div className="col-xs-2">
            <a onClick={@props.onHide} className="btn-flat pull-left"><i className="icon icon-times"></i></a>
          </div>
          <div className="col-xs-8 text-center">
            <div className="modal-title">Account Settings</div>
          </div>
          <div className="col-xs-2">
          </div>
        </div>
      </div>
      <div className="modal-body">
        <div className="page frame list list-link">
          <div className="list__divider">Account Information</div>
          <div className="account-card">
            <div className="account-card__row">
              <div className="account-card__cell account-card__cell-left">
                <img className="account-card__avatar" src={@props.instagram_access_token.ig_profile_picture} />
              </div>
              <div className="account-card__cell  account-card__cell-right">
                <div className="account-card__network">Instagram</div>
                <div className="account-card__username">{@props.instagram_access_token.ig_username}</div>
              </div>
            </div>
          </div>
          <div className="list-item list-item__field">
            <div className="list-item__row">
              <div className="list-item__content">
                <div className="list-item__title">Update Frequency</div>
              </div>
              <div className="list-item__right list-item__select">
                <select onChange={@updateFrequency} value={@props.instagram_access_token.update_frequency}>
                  <option value="hourly">Hourly</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>
            </div>
          </div>
          <div className="list-item list-item__field">
            <div className="list-item__row">
              <div className="list-item__content">
                <div className="list-item__title">Last Update</div>
              </div>
              <div className="list-item__right list-item__link">
                <a>{moment(@props.instagram_access_token.last_update).format('MM/DD/YYYY [at] h:mma')}</a>
              </div>
            </div>
          </div>
          <div className="list-item list-item__field">
            <div className="list-item__row">
              <div className="list-item__content">
                <div className="list-item__title">Disassociate Credentials</div>
              </div>
              <div className="list-item__right list-item__link">
                <a onClick={@removeAccount} >Remove this account</a>
              </div>
            </div>
          </div>    
        </div>
      </div>
    </Modal>

module.exports = AccountSettingsModal