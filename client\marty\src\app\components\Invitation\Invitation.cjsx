React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
History = require('react-router').History

Invitation = React.createClass
  displayName: 'Invitation'
  mixins: [History]

  componentDidMount: ->
    if @props.transitionToOnError?
      @props.app.registrationActionCreators.resetInvitationId()
      @history.pushState null, @props.transitionToOnError
    else if @props.transitionTo?
      @props.app.registrationActionCreators.setInvitationId(@props.params.invitationId, true)
      @history.pushState null, @props.transitionTo
    else if @props.invitation? and @props.invitation_entry?
      @props.app.registrationActionCreators.resetInvitationId()
      @props.app.invitationActionCreators.associateInvitationWithUser(@props.invitation.unique_id)
      @history.pushState null, "/entry/#{@props.invitation_entry.id}/", {comments: 1}
    else if @props.public_invitation?
      re = /\/api\/v1\/shared_entry\/([0-9]+)\/$/
      match = re.exec(@props.public_invitation.entry)
      if match?
        invitation_entry_id = match[1]
      @props.app.registrationActionCreators.resetInvitationId()
      @props.app.invitationActionCreators.associateInvitationWithUser(@props.public_invitation.unique_id)
      @history.pushState null, "/entry/#{invitation_entry_id}", {comments: 1}
    
  componentDidUpdate: (prevProps, prevState) ->
    if @props.transitionToOnError?
      @props.app.registrationActionCreators.resetInvitationId()
      @history.pushState null, @props.transitionToOnError
    else if @props.invitation? and @props.invitation_entry?
      @props.app.registrationActionCreators.resetInvitationId()
      @props.app.invitationActionCreators.associateInvitationWithUser(@props.invitation.unique_id)
      @history.pushState null, "/entry/#{@props.invitation_entry.id}/", {comments: 1}
    else if @props.public_invitation?
      re = /\/api\/v1\/shared_entry\/([0-9]+)\/$/
      match = re.exec(@props.public_invitation.entry)
      if match?
        invitation_entry_id = match[1]
      @props.app.registrationActionCreators.resetInvitationId()
      @props.app.invitationActionCreators.associateInvitationWithUser(@props.public_invitation.unique_id)
      @history.pushState null, "/entry/#{invitation_entry_id}", {comments: 1}
    
  render: ()->
    <div>
    </div>

InvitationContainer = Marty.createContainer Invitation,
  # listenTo: ['invitationStore', 'entryStore']

  fetch: ()->
    if @app.authStore.isLoggedIn() and @app.authStore.isActive()
      fetchState = {}
      if @props.isNotPublicInvitation
        fetchState = {
          invitation: @props.app.invitationStore.getInvitationByUUID(@props.params.invitationId)
          invitation_entry: @props.app.entryStore.getEntryForInviteUUID(@props.params.invitationId)
        }
      else
        fetchState = {}
      return fetchState
    else
      return {
        transitionTo: "/invitation/"
      }
  done: (results)->
    props = _.extend {}, @props, results
    return <Invitation {...props} />
  pending: (fetches)->
    # props = _.extend {}, @props, fetches
    # return <Invitation {...props} />
    return <div></div>
  failed: (errors)->
    console.log errors
    props = _.extend {}, @props
    props['transitionToOnError'] = "/"
    return <Invitation {...props} />

PublicInvitationContainer = Marty.createContainer InvitationContainer,
  listenTo: ['invitationStore','entryStore']

  fetch: ()->
    if @app.authStore.isLoggedIn() and @app.authStore.isActive()
      fetchState = {
        public_invitation: @props.app.invitationStore.getPublicEntrySharedInvitation(@props.params.invitationId)
      }
    else
      return {}
    return fetchState
  done: (results)->
    props = _.extend {}, @props, results
    props['isNotPublicInvitation'] = false
    return <InvitationContainer {...props} />
  pending: (fetches)->
    # props = _.extend {}, @props, fetches
    return <div></div>
  failed: (errors)->
    props = _.extend {}, @props
    props['isNotPublicInvitation'] = true
    return <InvitationContainer {...props} />

module.exports = PublicInvitationContainer