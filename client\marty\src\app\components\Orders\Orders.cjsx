React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

AppHeader = require '../Header/AppHeader'
Drawer = require '../Sidebar/Drawer'
LoadMore = require '../Timeline/LoadMore'

BookOrderRow = require './BookOrderRow'

Orders = React.createClass
  displayName: 'Orders'


  getInitialState: ()->
    pager: {
        previous: @props.page?.previous
        next: @props.page?.next
        total_count: @props.page?.total_count
        offset: @props.page?.offset
        limit: @props.page?.limit
      }

  componentWillReceiveProps: (nextProps) ->
    @setState
      pager: {
        previous: nextProps.page?.previous
        next: nextProps.page?.next
        total_count: nextProps.page?.total_count
        offset: nextProps.page?.offset
        limit: nextProps.page?.limit
      }

  formatAppHeader: ()->
    if @props.book?
      return "Book Orders for " + @props.book?.title
    else
      return "Book Orders "
  
  onLoadMore: ()->
      if @state.pager.next?
        viewId = 'Orders'
        if @props.params.bookId
          viewId = 'Order' + @props.params.bookId
        @.app.bookOrderQueries.getPage(@state.pager.next, viewId)

  renderPager: ()->
    if @state.pager?.next? and @props.orders?
      return (
        <div className="frame full-width">
          <LoadMore pager={@state.pager} loaded={@props.orders?.length} onClick={@onLoadMore}/>
        </div>
      )
    else
      return undefined


  render: ()->
    <div>
      <Drawer />
      <div id="page">
        <AppHeader view_title={@formatAppHeader()}  {...@props}/>
        <div id="content">
          <div className="container">
            <div id="bookbuilder-column" className="pull-left full-width">
              <table className="table frame table-bookbuilder">
                <thead>
                  <tr>
                    <th className="col-check">
                      <a>Order<span className=""></span></a>
                    </th>
                    <th className="col-name">
                      <a>Book <span className=""></span></a>
                    </th>
                    <th className="col-name">
                      <a>Total <span className=""></span></a>
                    </th>
                    <th className="col-name">
                      <a>Created <span className=""></span></a>
                    </th>
                    <th className="col-name">
                      <a>Status<span className=""></span></a>
                    </th>
                    <th className="col-date">
                      <a>Date<span className=""></span></a>
                    </th>
                  </tr>
                </thead>
                <tbody>
                { 
                  if @props.orders?
                    _.sortBy @props.orders, (order)->
                      return (parseInt(order.id) * -1)
                    .map (order)=>
                      return <BookOrderRow key={order.id} order={order} {...@props} />
                 
                }
                </tbody>
              </table>
              {
                @renderPager()
              }
            </div>
          </div>
        </div>
      </div>
    </div>

OrdersContainer = Marty.createContainer Orders,
  listenTo: ['bookOrderStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      if @props.params.bookId
        return {
          orders: @props.app.bookOrderStore.getBookOrdersByBook('Order' + @props.params.bookId, @props.params.bookId)
          book: @props.app.bookStore.getBook(@props.params.bookId)
          user: @props.app.authStore.fetchUser()
          firstname: @props.app.authStore.getFirstNameOrUsername()
          page: @props.app.pageStore.getPage('Order' + @props.params.bookId)
        }
      else
        return {
          orders: @props.app.bookOrderStore.getBookOrders('Orders')
          user: @props.app.authStore.fetchUser()
          firstname: @props.app.authStore.getFirstNameOrUsername()
          page: @props.app.pageStore.getPage('Orders')
        }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Orders {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Orders {...props} />
  failed: (error)->
    console.log error
    return <div>Orders Error</div>

module.exports = OrdersContainer