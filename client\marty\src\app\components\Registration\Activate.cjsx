React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Link = require('react-router').Link
settings = require '../../settings'

fbevents = require '../../utils/fbevents'
XScript = require '../Utility/Xscript'

Activate = React.createClass
  displayName: "Activate"
  
  componentDidMount: ->
    activation_id = @props.params.activationId
    if activation_id
      @.app.activationActionCreators.activate(activation_id)

  componentWillUnmount: ->
    @.app.activationActionCreators.reset()

  renderConversionPixels: ()->
    if settings.conversion_pixels?
      return (
        <div>
          {
            settings.conversion_pixels.map (pixel)->
              return <img key={pixel.name} src={pixel.src} style={pixel.style} />
          }
        </div>
      )
    return undefined

  renderConversionTag: ()->
    if settings.conversion_script_tags?
      return (
        <div>
          {
            settings.conversion_script_tags.map (tag)->
              return <XScript key={tag.name} url={tag.src} />
          }
        </div>
      )
    return undefined
  
  renderSuccess: ()->
    <div id="welcome_page">
      <div className="bg-pattern" style={backgroundImage: "url('/static/images/backgrounds/patterns/pattern-001.png')"}></div>
      <div className="welcome-wrapper">
        <div id="welcome_panel" className="panel frame">
          <div className="panel-heading">
            <Link to={"/"} className="jrnl-logo"><img src="/static/images/jrnl-logo-white.png" /></Link>
          </div>
          <div className="panel-body">
            <h2>Welcome to JRNL!</h2>
            <h3>
            Your account has been activated.
            </h3>
          </div>
          <div className="panel-footer">
            <Link to={'/login/'} className="btn btn-orange btn-large">Login to your account!</Link>
              {
                @renderConversionPixels()
              }
              {
                @renderConversionTag()
              }
          </div>
        </div>
      </div>
    </div>
    
  renderError: ()->
    <div id="welcome_page">
      <div className="bg-pattern" style={backgroundImage: "url('/static/images/backgrounds/patterns/pattern-001.png')"}></div>
      <div className="welcome-wrapper">
        <div id="welcome_panel" className="panel frame">
          <div className="panel-heading">
            <Link to={"/"} className="jrnl-logo"><img src="/static/images/jrnl-logo-white.png" /></Link>
          </div>
          <div className="panel-body">
            <h2>Invalid activation key!</h2>
            <h3>
            Have you already activated this account?
            </h3>
          </div>
          <div className="panel-footer">
            <Link to={'/login/'} className="btn btn-orange btn-large">Login to your account!</Link>
          </div>
        </div>
      </div>
    </div>

  render: ()->
    <div>
      {
        if @props.isActivated
          @renderSuccess()
        else if @props.activationError
          @renderError()
      }
    </div>

ActivationContainer = Marty.createContainer Activate,
  listenTo: ['activationStore']

  fetch: ()->
    return {
      isActivated: @props.app.activationStore.isActivated()
      activationError: @props.app.activationStore.isError()
    }
  done: (results)->
    props = _.extend {}, @props, results
    return <Activate {...props} />
  pending: (fetches)->
    return <Activate {...@props} {...fetches}/>
  failed: (error)->
    return <div>Activation Error</div>

module.exports = ActivationContainer