React = require 'react'
classnames = require 'classnames'

ToggleSwitch = React.createClass
  displayName: 'ToggleSwitch'

  confirm: ()->
    # console.log 'Add inline confirmation'
    
  onConfirm: ()->
    if @props.confirmToggleStateChange
      # console.log 'onConfirm'
      if @props.confirmHandler?
        @props.confirmHandler()
      else
        @confirm()

  onToggle: ()->
    if @props.onToggle?
      @props.onToggle()

  render: ()->
    classes = classnames "fr", if @props.enabled then "green" else "red"
    
    <div id="switch" className={classes}>
      <input type="hidden" name={@props.switchId} value="1" />
      <input type="checkbox" id={@props.switchId} name={@props.switchId} className="switch" checked={@props.enabled} onChange={@onToggle}/>
      <label htmlFor={@props.switchId} onClick={@onConfirm}>&nbsp;</label>
    </div>

module.exports = ToggleSwitch