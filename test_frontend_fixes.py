#!/usr/bin/env python
"""
Test script to verify frontend fixes for AMEX support
"""
import re

def test_payment_profile_modal():
    """Test that PaymentProfileModal.cjsx has AMEX option"""
    print("Testing PaymentProfileModal.cjsx...")
    
    try:
        with open('client/marty/src/app/components/Modals/PaymentProfileModal.cjsx', 'r') as f:
            content = f.read()
        
        # Check for AMEX option in dropdown
        amex_option_pattern = r'<option value="amex">American Express</option>'
        has_amex_option = bool(re.search(amex_option_pattern, content))
        
        print(f"  ✓ AMEX option found: {has_amex_option}")
        
        # Check that all expected card types are present
        expected_options = [
            'value="visa">Visa',
            'value="mc">MasterCard', 
            'value="amex">American Express',
            'value="discover">Discover'
        ]
        
        all_present = True
        for option in expected_options:
            if option not in content:
                print(f"  ✗ Missing option: {option}")
                all_present = False
            else:
                print(f"  ✓ Found option: {option}")
        
        return has_amex_option and all_present
        
    except FileNotFoundError:
        print("  ✗ File not found")
        return False

def test_billing_settings():
    """Test that BillingSettings.cjsx has AMEX in CARD_TYPES"""
    print("\nTesting BillingSettings.cjsx...")
    
    try:
        with open('client/marty/src/app/components/Settings/BillingSettings.cjsx', 'r') as f:
            content = f.read()
        
        # Check for AMEX in CARD_TYPES object
        amex_card_type_pattern = r"'amex':\s*'American Express'"
        has_amex_type = bool(re.search(amex_card_type_pattern, content))
        
        print(f"  ✓ AMEX in CARD_TYPES: {has_amex_type}")
        
        # Check that all expected card types are present
        expected_types = [
            "'mc': 'Mastercard'",
            "'visa': 'Visa'", 
            "'amex': 'American Express'",
            "'discover': 'Discover'"
        ]
        
        all_present = True
        for card_type in expected_types:
            if card_type not in content:
                print(f"  ✗ Missing type: {card_type}")
                all_present = False
            else:
                print(f"  ✓ Found type: {card_type}")
        
        return has_amex_type and all_present
        
    except FileNotFoundError:
        print("  ✗ File not found")
        return False

def test_payment_method():
    """Test that PaymentMethod.cjsx has AMEX in CARD_TYPES"""
    print("\nTesting PaymentMethod.cjsx...")
    
    try:
        with open('client/marty/src/app/components/Bookbuilder/PaymentMethod.cjsx', 'r') as f:
            content = f.read()
        
        # Check for AMEX in CARD_TYPES object
        amex_card_type_pattern = r"'amex':\s*'American Express'"
        has_amex_type = bool(re.search(amex_card_type_pattern, content))
        
        print(f"  ✓ AMEX in CARD_TYPES: {has_amex_type}")
        
        # Check that all expected card types are present
        expected_types = [
            "'mc': 'Mastercard'",
            "'visa': 'Visa'", 
            "'amex': 'American Express'",
            "'discover': 'Discover'"
        ]
        
        all_present = True
        for card_type in expected_types:
            if card_type not in content:
                print(f"  ✗ Missing type: {card_type}")
                all_present = False
            else:
                print(f"  ✓ Found type: {card_type}")
        
        return has_amex_type and all_present
        
    except FileNotFoundError:
        print("  ✗ File not found")
        return False

def main():
    print("Frontend AMEX Support Test Suite")
    print("=" * 50)
    
    results = []
    
    # Test all frontend components
    results.append(test_payment_profile_modal())
    results.append(test_billing_settings())
    results.append(test_payment_method())
    
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    if all(results):
        print("✅ ALL TESTS PASSED!")
        print("✓ AMEX option added to payment form dropdown")
        print("✓ AMEX type added to billing settings display")
        print("✓ AMEX type added to payment method display")
        print("\nThe user should now be able to:")
        print("1. Select 'American Express' from the card type dropdown")
        print("2. Enter a valid AMEX card number (34xx or 37xx, 15 digits)")
        print("3. Enter a 3 or 4 digit CVV code")
        print("4. See 'American Express' displayed in their billing settings")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the files and ensure AMEX support is properly added.")
    
    return all(results)

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
