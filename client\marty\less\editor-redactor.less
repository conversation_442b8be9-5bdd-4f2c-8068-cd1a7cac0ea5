/*---------------------------------------------------------------------------
  >Redactor
---------------------------------------------------------------------------*/

.redactor-toolbar,
.redactor-editor,
#editor_column .entry-title
{
	border-color: @gray3;
}

#editor_column .entry-title {box-shadow: none;}

.redactor-toolbar, 
.redactor-dropdown {
  z-index: 1025 !important;
}

.redactor-box {
	background: transparent;
	margin-bottom: 0 !important;
		
	.redactor-toolbar {
	  //border-radius: 3px 3px 0 0;
		border-top: 1px solid @gray9;
		border-bottom: 1px solid @gray9;
		box-shadow: none !important;
	  .gradient-w-g;
	 
	  li a:active, li a.redactor-act {
		  color: white;
		  .teal-bg;
	  }
		
		li {
			.fl
		}
		
		li:nth-child(13),
		li:nth-child(14)
		{
		  .fr
		}
		
		li a {
			color: @gray5;
			padding: 10px;
		  display: inline-block;
		  border-width: 0 1px 0 1px;
		  border-style: solid;
		  border-color: transparent;
			
			&:hover {
				.teal-txt;
				.white-bg;
			  border-width: 0 1px 0 1px;
			  border-style: solid;
			  border-color: rgba(0, 0, 0, 0.08);
			}
		}
		
		.redactor-act, .redactor-act:hover {
			.white-txt;
			.teal-bg;
		}
	}
}

/************** >Redactor Editor **************/

.redactor-editor {
  min-height: 400px;
	border: none;
  padding: 20px;
	.full-width;
	.white-bg;
	.font1;
	
	ul li {
		list-style: disc;
	}
	ol li {
		list-style: decimal;
	}
}

