React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
FormData = require('react-form-data')
classnames = require('classnames')

Lifecycle = require('react-router').Lifecycle

AvatarMenu = require './AvatarMenu'
MediaManagerModal = require '../MediaManager/Modals/MediaManagerModal'
DisableSharingModal = require '../Modals/DisableSharingModal'

ProfileSettings = React.createClass
  displayName: 'ProfileSettings'
  mixins: [FormData, Lifecycle]
  PROFILE_KEYS: ['first_name', 'last_name', 'sharing', 'show_avatar', 'show_name', 'email']

  getInitialState: ->
    showMediaManager: false
    showDisableSharing: false
    sharing: @props.user?.sharing
    isSaved: false
    initialForm: @getInitialFormData()

  componentWillReceiveProps: (nextProps) ->
    if nextProps.user? and not @props.user?
      @setState
        sharing: nextProps.user.sharing
        initialForm: @getInitialForm()

  routerWillLeave: (nextLocation)->
    if @formDataHasChanged() and not @state.isSaved
      return "Your changes will be lost. Are you sure you want to leave?"

  getInitialFormData: ()->
    initialForm = {}
    if @props.user?
      initialForm = _.pick @props.user, @PROFILE_KEYS
    return initialForm
  
  componentDidMount: ->
    @props.app.authActionCreators.resetUserSuccess()
    @props.app.authActionCreators.resetUserError()

  componentWillUnmount: ->
    @props.app.authActionCreators.resetUserSuccess()
    @props.app.authActionCreators.resetUserError()      

  formDataHasChanged: ()->
    KEYS = ['first_name','last_name','sharing','show_avatar','show_name', 'email']
    form = _.pick @formData, KEYS
    form.sharing = @state.sharing
    
    if _.isEqual form, _.pick @state.initialForm, KEYS
      return false
    else
      return true

  updateProfile: (e)->
    e.stopPropagation()
    e.preventDefault()
    
    user = {}
    if @props.user.first_name != @formData.first_name and @formData.first_name != ""
      user.first_name = @formData.first_name
    if @props.user.last_name != @formData.last_name and @formData.last_name != ""
      user.last_name = @formData.last_name
    if @props.user.sharing != @state.sharing
      user.sharing = @state.sharing
    if @props.user.show_avatar != @formData.show_avatar
      user.show_avatar = @formData.show_avatar
    if @props.user.show_name != @formData.show_name
      user.show_name = @formData.show_name
    
    if !_.isEmpty(user)
      @app.authActionCreators.updateProfile(user, 'Settings')
      @setState
        isSaved: true

  onUploadAvatar: ()->
    @openMediaManager()

  openMediaManager: ()->
    @setState
      showMediaManager: true

  closeMediaManager: ()->
    @setState
      showMediaManager: false

  openDisableSharingConfirmation: ()->
    if @props.user?.sharing
      @setState
        showDisableSharing: true

  closeDisableSharingConfirmation: ()->
    @setState
      showDisableSharing: false

  updateAvatar: (files)->
    if files?
      avatar = files[0]
      @.app.avatarActionCreators.createAvatar avatar
      @.app.userActionCreators.updateCurrentAvatar avatar
      @closeMediaManager()
    else
      @closeMediaManager()

  removeAvatar: ()->
    avatar = null
    @.app.userActionCreators.removeCurrentAvatar()
    @closeMediaManager()

  onConfirmDisableSharing: ()->
    @setState
      sharing: false

  onCancelDisableSharing: ()->
    @setState
      sharing: true

  onChange: (e)->
    @setState
      sharing: !@state.sharing

  onFormUpdate: (e)->
    @updateFormData(e)

  renderSuccess: ()->
    if @props.success
      <div className="alert success">Profile Updated</div>

  render: ()->
    <div role="tabpanel" className="tab-pane active" id="profile-settings">
      {
        if @state.showMediaManager
          <MediaManagerModal mediaType={'avatar'} show={@state.showMediaManager} onHide={@closeMediaManager} onDone={@updateAvatar} {...@props} />
      }
      {
        if @state.showDisableSharing
          <DisableSharingModal {...@props} show={@state.showDisableSharing} onHide={@closeDisableSharingConfirmation} onConfirm={@onConfirmDisableSharing} onCancel={@onCancelDisableSharing} />
      }
      <form onChange={@onFormUpdate}>
        <div className="settings-wrapper">
          <h2 className="section-title">Profile</h2>
            <div className="row">
              <div className="col-sm-12">
                <div className="avatar-area table-wrapper">
                  <div className="avatar table-cell">           
                    <img src={@props.user?.avatar_image_url} />
                  </div>
                  <div className="table-cell">
                    <div className="profile-info">
                      <div className="name">{"#{@props.user?.first_name} #{@props.user?.last_name}"}</div>
                      <div className="email">{@props.user?.email}</div>
                    </div>
                    <AvatarMenu onRemoveCurrentAvatar={@removeAvatar} onUploadAvatar={@onUploadAvatar} {...@props} />
                  </div>
                </div>
                <hr />
                <div className="row">
                  <div className="col-xs-12 col-sm-6">
                    <div className="form-group">
                      <label>First Name</label>
                      <input type="text" className="form-control" name="first_name" placeholder={@props.user?.first_name} />
                    </div>
                  </div>
                  <div className="col-xs-12 col-sm-6">
                    <div className="form-group">
                      <label>Last Name</label>
                      <input type="text" className="form-control" name="last_name" placeholder={@props.user?.last_name} />
                    </div>
                  </div>
                </div>
                <div className="row">
                  <div className="col-xs-12 col-sm-6">              
                    <div className="form-group">
                      <label>Email</label>
                      <input type="email" className="form-control" id="" placeholder={@props.user?.email} disabled/>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <hr />
            {
              @renderSuccess()
            }
            <h2 className="section-title">Privacy Settings</h2>
            <div className="panel panel-default">
              <div className="panel-body">
                <div id="switch" className={classnames("fr", if @state.sharing then "green" else "red")}>
                  <input type="hidden" value="1" />
                  <input type="checkbox" id="sharing" name='sharing' className="switch" checked={@state.sharing} onChange={@onChange}/>
                  <label onClick={if @state.sharing then @openDisableSharingConfirmation else undefined} htmlFor="sharing">&nbsp;</label>
                </div>
                  <label>Sharing {if @state.sharing then 'enabled' else 'disabled'} </label><br />
                  {
                    if @state.sharing
                      <span className="help-text">Sharing is enabled, you may share entries with others and receive invitations from other JRNL users. When disabled you cannot share and all outstanding shares will be disabled.</span>
                    else
                      <span className="help-text">Sharing is disabled, you cannot share. If you accept an invitation when disabled, sharing is enabled.</span>
                  }
              </div>
            </div>
            <div className="row">
              <div className="col-xs-12 col-sm-6">
                <label>Profile display options</label>
                <div className="form-group">
                  <p className={classnames({disabled: !@state.sharing})}>
                    <input ref='avatarCheckbox' type="checkbox" defaultChecked={@props.user?.show_avatar} name='show_avatar'/>  Display my avatar
                  </p>
                  <p className={classnames({disabled: !@state.sharing})}>
                    <input ref='fullnameCheckbox' type="checkbox" defaultChecked={@props.user?.show_name} name='show_name'/>  Display my full name
                  </p>
                </div>
              </div>
              <div className="col-xs-12 col-sm-6">
                <label>Profile preview</label>
                <p className="help-text">This is what others will see.</p>
                <div className="panel panel-default">
                  {
                    if @state.sharing
                      <div id="list"> 
                        <div className="list-item">
                          <div className="action action-primary">
                            <div className="item-wrapper">
                              <div className="item-row">
                                <div className="col avatar">
                                  <img className="img-circle" src={@props.user?.public_avatar_image_url} />
                                </div>
                                <div className="col text">
                                  <div>{@props.user?.public_display_name}</div>
                                  <div className="subtext">{@props.user?.email}</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    else
                      <div className="text">
                        Nothing
                      </div>
                  }
                </div>
              </div>
            </div>
            <div className="row">
              <div className="col-sm-12">
                <hr />
                <button onClick={@updateProfile} className="btn btn-navy btn-medium">Save</button>
              </div>
            </div>
          </div>
      </form>
    </div>

ProfileSettingsContainer = Marty.createContainer ProfileSettings,
  listenTo: ['authStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        success: @props.app.authStore.getSuccess()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <ProfileSettings {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <ProfileSettings {...props}/>
  failed: (error)->
    console.log error
    return <div>Profile Settings Error</div>

module.exports = ProfileSettingsContainer