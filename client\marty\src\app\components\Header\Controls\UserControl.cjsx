React = require 'react'
Marty = require 'marty'
Link = require('react-router').Link

UserControl = React.createClass
  displayName: 'UserControl'
  mixins: [Marty.createAppMixin()]

  propTypes:
    user_name: React.PropTypes.string

  logout: ()->
    @.app.authActionCreators.logout()

  onWelcome: ()->
    @.app.userActionCreators.updateWelcomeStatus(false)

  render: ()->
    <ul className="nav navbar-nav">
      <li id="user" className="dropdown">
        <a href="#" className="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">{@props.user_name} <i className="fa fa-angle-down teal"></i><img className="avatar avatar-sm" src={@props.user?.avatar_image_url} /></a>
        <ul className="dropdown-menu dropdown-swing-left dropdown-default">
          <li>
            <Link to={"/settings/"}>Settings</Link>
          </li>
          <li role="separator" className="divider"></li>
          <li>
            <a onClick={@logout}>Logout</a>
          </li>
        </ul>
      </li>
    </ul>

module.exports = UserControl