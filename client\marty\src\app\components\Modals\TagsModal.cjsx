React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

FormData = require 'react-form-data'

Modal = require('react-bootstrap').Modal
OptionsListHeader = require './OptionsList/OptionsListHeader'
OptionsListSearch = require './OptionsList/OptionsListSearch'
OptionsList = require './OptionsList/OptionsList'

LoadMore = require '../Timeline/LoadMore'
CreateModal = require './CreateModal'
TagForm = require './Forms/TagForm'

TagOption = React.createClass
  displayName: "Tag Option Item"

  propTypes: 
    option: React.PropTypes.object
    onOptionSelected: React.PropTypes.func
    onUpdateTags: React.PropTypes.func

  onClick: (option)->
    @props.onOptionSelected(option)
    
  renderSelected: ()->
    if @props.isSelected?
      return (
        <div className="item-icon-right">
          <span className="icon icon-check pull-right"></span>
        </div>
      )
    else
      return undefined

  render: ()->
    <a onClick={@onClick.bind null,@props.option}>
      <div className="item-icon-left">
        <span className="icon icon-tag"></span>
      </div>{@props.option.name}
      <span className="sub-text">{@props.option.entry_count || 0} Entries</span> 
      {@renderSelected()}
    </a>

TagsModal = React.createClass
  displayName: "Tags Modal"

  getInitialState: ->
    selectedOptions: @getInitialSelectedOptions()
    query: undefined
    isEmpty: undefined
    pager: {
      previous: @props.page?.previous
      next: @props.page?.next
      total_count: @props.page?.total_count
      offset: @props.page?.offset
      limit: @props.page?.limit
    }
  
  getInitialSelectedOptions: ()->
    selectedOptions = []
    if @props.entryTags?
      for tag in @props.entryTags
        selectedOptions.push _.findWhere @props.tags, {resource_uri: tag}
    
    return selectedOptions
    
  componentWillReceiveProps: (nextProps) ->
    isEmpty = false
    if not nextProps.tags? or nextProps.tags?.length == 0 
      isEmpty = true
    
    @setState
      isEmpty: isEmpty
      pager: {
        previous: nextProps.page?.previous
        next: nextProps.page?.next
        total_count: nextProps.page?.total_count
        offset: nextProps.page?.offset
        limit: nextProps.page?.limit
      }

  onOptionSelected: (selectedOption)->
    if _.findWhere @state.selectedOptions, {resource_uri: selectedOption.resource_uri}
      selectedOptions = _.filter @state.selectedOptions, (option)->      
        return selectedOption.resource_uri != option.resource_uri
    else
      selectedOptions = @state.selectedOptions
      selectedOptions.push selectedOption
    
    @setState
      selectedOptions: selectedOptions
  
  onDone: ()->
    if @props.params.entryId?
      newTags = []
      for tag in @state.selectedOptions
        newTags.push tag.resource_uri
      newEntry = @props.entry
      newEntry.tags = newTags
      @.app.entryActionCreators.updateEntry(newEntry)
    else
      newTags = []
      for tag in @state.selectedOptions
        newTags.push tag.id
      if @props.onUpdateTags?
        @props.onUpdateTags(newTags)
    @props.onHide()

  onCreateTag: (tag)->
    @.app.tagActionCreators.createTag(tag)

  onSearch: (query)->
    if query != @state.query
      @setState
        query: query

  onLoadMore: ()->
    if @state.pager.next?
      @.app.tagsQueries.getPage(@state.pager.next, 'Tags')

  renderPager: ()->
    if @state.pager?.next? and @props.tags?
      return (
        <div>
          <LoadMore pager={@state.pager} loaded={@props.tags.length} onClick={@onLoadMore}/>
        </div>
      )
    else
      return undefined

  render: ()->
    <Modal {...@props} enforceFocus={false}>
      <OptionsListHeader title={'All Tags'} onHide={@props.onHide} onDone={@onDone} />
      <div className="modal-body">
        <OptionsListSearch onSearch={@onSearch} searchPlaceholder={'Search Tags'} query={@state.query}/>
        <OptionsList optionsSortBy={'name'} optionComponent={TagOption} header={'Tags'} options={@props.tags} selectedOptions={@state.selectedOptions} onOptionSelected={@onOptionSelected} query={@state.query} allowNew={true} newType={'Tag'} createForm={TagForm} createAction={@onCreateTag}/>        
        {
          @renderPager()
        }
      </div>
    </Modal>

TagsModalContainer = Marty.createContainer TagsModal,
  listenTo: ['tagsStore']

  fetch: 
    tags: ()->
      return @.app.tagsStore.getTags('Tags')
    page: ()->
      return @.app.pageStore.getPage('Tags')
  done: (results)->
    props = _.extend {},@props,results
    return <TagsModal {...props} /> 
  pending: ()->
    return <div></div>
  failed: (error)->
    console.log error
    return <div>MODAL ERROR</div>

module.exports = TagsModalContainer