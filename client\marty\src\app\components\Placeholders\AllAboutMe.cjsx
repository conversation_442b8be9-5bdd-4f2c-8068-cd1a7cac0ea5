React = require 'react'
Marty = require 'marty'

Drawer = require '../Sidebar/Drawer'
AppHeader = require '../Header/AppHeader'

AllAboutMePlaceholder = React.createClass
  displayName:"All About Me Placeholder"
  mixins:[Marty.createAppMixin()]

  render: ()->
    <div>
      <Drawer />
      <div id="page">
        <AppHeader username={@app.authStore.getUsername()} view_title='All About Me' {...@props}/>
        <div id="nav_crumb" className="gray">
          <div className="container">
            <div id="crumb-bar" className="pull-left">
              <div id="date" className="pull-left">
                <a className="icon crumb-icon icon-all-about-me" ></a><i className="icon icon-angle-right"></i><span>Accomplishments</span>
              </div>
            </div>
            <div id="tools" className="pull-right text-right">
            </div>
          </div>
        </div>
        <div id="content">
          <div className="container">
            <div id="timeline-column" className="pull-left right-padding">
              <div className="alert section-intro teal-bg alert-dismissible">
                <img src="/static/images/aam-instruction-image.jpg" alt="aam-instruction-image" style={width: "100%"} />
                <div className="section-intro-body">
                  <h2>All About Me provides you with hundreds of questions that span across many different aspects of your life.</h2>
                  <h3 style={marginBottom: "30px"}>Whether you have zero journaling experience or are a seasoned pro, this section will help you to answer key questions that cover some of the most significant moments in life.</h3>
                </div>
              </div>
            </div>
            <div id="default-sidebar" className="pull-right">
              <div id="category_tile" className="frame">
              
                <ul className="list">
                  <li>
                    <a>
                      <div className="cat-icon"><img src="/static/images/icons/icon-personal.svg"/></div>
                      <h2>General Facts About Me</h2>
                      <span className="sub-text">A snapshot of who you are</span>
                    </a>
                  </li>
                  <li>
                    <a>
                      <div className="cat-icon"><img src="/static/images/icons/icon-accomplish.svg"/></div>
                      <h2>Accomplishments</h2>
                      <span className="sub-text">Proud moments and achievements</span>
                    </a>
                  </li>
                  <li>
                    <a>
                      <div className="cat-icon"><img src="/static/images/icons/icon-education.svg"/></div>
                      <h2>Education</h2>
                      <span className="sub-text">Stuff you've dun learnt</span>
                    </a>
                  </li>
                  <li>
                    <a>
                      <div className="cat-icon"><img src="/static/images/icons/icon-adversity.svg"/></div>
                      <h2>Challenges</h2>
                      <span className="sub-text">Enduring and conquering difficult times</span>
                    </a>
                  </li>
                  <li>
                    <a>
                      <div className="cat-icon"><img src="/static/images/icons/icon-grow.svg"/></div>
                      <h2>Growing Up</h2>
                      <span className="sub-text">Dive deep into nostalgia</span>
                    </a>
                  </li>
                  <li>
                    <a>
                      <div className="cat-icon"><img src="/static/images/icons/icon-family.svg"/></div>
                      <h2>Family</h2>
                      <span className="sub-text">Like a box of chocolates...</span>
                    </a>
                  </li>
                  <li>
                    <a>
                      <div className="cat-icon"><img src="/static/images/icons/icon-parenthood.svg"/></div>
                      <h2>Parenthood</h2>
                      <span className="sub-text">Ah, the joys of raising children</span>
                    </a>
                  </li>
                  <li>
                    <a>
                      <div className="cat-icon"><img src="/static/images/icons/icon-relationships.svg"/></div>
                      <h2>Relationships</h2>
                      <span className="sub-text">The good, the bad and the...</span>
                    </a>
                  </li>
                  <li>
                    <a>
                      <div className="cat-icon"><img src="/static/images/icons/icon-spiritual.svg" /></div>
                      <h2>Spiritual</h2>
                      <span className="sub-text">Your views on the big picture</span>
                    </a>
                  </li>
                  <li>
                    <a>
                      <div className="cat-icon"><img src="/static/images/icons/icon-world.svg" /></div>
                      <h2>The World Around Me</h2>
                      <span className="sub-text">History is remembered because of those that write it</span>
                    </a>
                  </li>
                </ul>
              </div>          
            </div>
          </div>
        </div>
      </div>
    </div>
  

module.exports = AllAboutMePlaceholder