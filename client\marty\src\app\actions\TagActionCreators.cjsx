Marty = require 'marty'
tagsConstants = require '../constants/TagsConstants'

TagActionCreators = Marty.createActionCreators
  updateTag: (tag)->
    return @.app.tagsHttpAPI.updateTag(tag)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (success)=>
      @.dispatch tagsConstants.TAG_UPDATED, success
    .catch (error)=>
      console.log error
      @.dispatch tagsConstants.TAGS_ERROR, error

  deleteTag: (tag)->
    return @.app.tagsHttpAPI.deleteTag(tag)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (success)=>
      @.dispatch tagsConstants.TAG_DELETED, tag
    .catch (error)=>
      console.log error
      @.dispatch tagsConstants.TAGS_ERROR, error

  createTag: (tag)->
    return @.app.tagsHttpAPI.createTag(tag)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (success)=>
      @.dispatch tagsConstants.TAG_CREATED, success
    .catch (error)=>
      console.log error
      @.dispatch tagsConstants.TAGS_ERROR, error

module.exports = TagActionCreators