React = require 'react'
Marty = require 'marty'
ga = require 'react-ga'
_ = require 'underscore'

AuthenticatedComponent = require './AuthenticatedComponent'

BackgroundFrame = React.createClass
  displayName: 'Background Frame'
  
  render: ()->
    <div id="background"></div>

JRNL = 
  React.createClass
    displayName: 'JRNL'
    mixins: [Marty.createAppMixin()]

    componentDidMount: ->
      if @.app.authStore.isLoggedIn() and @app.authStore.isActive()
        if @props.user?.wallpaper_image_url? 
          wallpaper = @props.user?.wallpaper_image_url
          if not _.isEmpty wallpaper
            wallpaper_url = "url(#{wallpaper})"
            background = document.getElementById('background')
            if wallpaper_url != background.style.backgroundImage
              background.className = 'bg-fill'
              background.style.backgroundImage = wallpaper_url
          else
            background = document.getElementById('background')  
            background.className = 'bg-pattern'
            background.style.backgroundImage = "url(/static/images/backgrounds/patterns/pattern-001.png)"
        @login()
      else
        @logout(@props.location?.pathname)

      if @props.location?.pathname?
        if ga?
          ga.pageview(@props.location.pathname)

      if @props.location?.query?.ja?
        if not _.isEmpty @props.location.query.ja
          @.app.registrationActionCreators.set_jrnl_affiliate(@props.location.query.ja)

      if @props.location?.query?.aff?
        if not _.isEmpty @props.location.query.aff
          @.app.registrationActionCreators.set_hasoffer_affiliate(@props.location.query.aff)

      
    componentWillReceiveProps: (nextProps) ->
      if nextProps.location?.pathname?
        if ga?
          ga.pageview(nextProps.location.pathname)
      
      if @.app.authStore.isLoggedIn() and @app.authStore.isActive()
        if nextProps.user?.wallpaper_image_url? 
          wallpaper = nextProps.user?.wallpaper_image_url
          if not _.isEmpty wallpaper
            wallpaper_url = "url(#{wallpaper})"
            background = document.getElementById('background')
            if wallpaper_url != background.style.backgroundImage
              background.className = 'bg-fill'
              background.style.backgroundImage = wallpaper_url
          else
            background = document.getElementById('background')  
            background.className = 'bg-pattern'
            background.style.backgroundImage = "url(/static/images/backgrounds/patterns/pattern-001.png)"  
        @login()
      else
        @logout(nextProps.location?.pathname)
      
    login: ()->
      if @props.params.nextPath?
        @.props.history.pushState null, nextPath
      return

    logout: (activeRoutename)->      
      allowed_paths = ['/login/','/signup/','/signup/success/','/invitation/' + @props.params.invitationId ,'/invitation/','/activate/' + @props.params.activationId, '/forgot-password/', '/forgot-password/success/','/password_set_confirm/' + @props.params.uid + '/' + @props.params.token + '/']
      if activeRoutename not in allowed_paths
        if @props.registrationAttempted
          @.props.history.pushState null, '/signup/'
        else
          @.props.history.pushState null, '/login/', {nextPath: activeRoutename}
      return
    
    renderApp: ()->
      <div id="stage">
        {
          React.cloneElement(@props.children, {app:@app})
        }
      </div>
      
    render: ->
      <div style={display: 'table', width: '100%', height: '100%'}>
        <BackgroundFrame />
        { 
          @renderApp()
        }
      </div>

JRNLContainer = Marty.createContainer JRNL,
  listenTo: ['authStore','timelineApprovalStore']

  fetch: ()->
    if @app.authStore.isLoggedIn() and @app.authStore.isActive()
      return {
        user: @app.authStore.fetchUser()
        registrationAttempted: @app.registrationStore.getExternalRegistration()
        timelineApprovals: @app.timelineApprovalStore.fetchTimelineApprovals()
      }
    else
      return {
        registrationAttempted: @app.registrationStore.getExternalRegistration()
      }
  done: (results)->
    props = _.extend {}, @props, results
    return <JRNL {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    # return <JRNL {...props} />
    return <div></div>
  failed: (errors)->
    console.log errors
    return <div>Error</div>

module.exports = JRNLContainer