/*---------------------------------------------------------------------------
  >Action Nav
---------------------------------------------------------------------------*/

#action-nav {
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);

	.nav {
		
		@media @xs {	margin: 0;}
	}
	
	.date {
		background-color: #00a7c4;
	  padding: 5px 12px;
	  margin: 9px 0;
	  .f15;
		.font2;
	  .white-txt;
	  .w600;
		.radius3;
		
		&:hover {background-color: rgba(0, 132, 158, 0.5);}
		
		@media @ss {width: 100%;}
		@media @xs {
			text-align: center;
			margin-bottom: 0;
		}
	}
	
	.pull-left {
		
		@media @xs {width: 100%;}
		
		.nav {
			
			@media @xs {	width: 100%;}
		}
		
		.category {
			margin: 11px 0;
			.white-txt;
			.w600;
			.f18;
		}
	}
	
	.pull-right {
		
		@media @xs {width: 100%;}
		
		.icon {
		  font-size: 21px;
		  line-height: 25px;
		  padding: 13px 0;
		  height: 50px;
		  width: 46px;
		  text-align: center;
		  color: #00829c;
	  }
	  
	  .icon.on, .icon:hover {.white-txt;}
	  
	  .nav {
		  
			@media @xs {
				width: 100%;
				text-align: center;
				
			  &>li {
		      display: inline-block;
		      
		      .btn-flat {
			      border: none;
			      height: inherit;
			    }
			  }
			}
	  }
	}
}