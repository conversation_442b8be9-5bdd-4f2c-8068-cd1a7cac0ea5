Marty = require 'marty'
billingConstants = require '../constants/BillingConstants'

BillingActionCreators = Marty.createActionCreators
  createPaymentProfile: (paymentProfile)->
    return @app.billingHttpAPI.createPaymentProfile(paymentProfile)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @.dispatch billingConstants.PAYMENT_PROFILE_CREATED, success
    .catch (error)=>
      console.log error
      @.dispatch billingConstants.BILLING_ERROR, error

  updatePaymentProfile:(paymentProfile, id)->
    return @app.billingHttpAPI.updatePaymentProfile(paymentProfile, id)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @.dispatch billingConstants.PAYMENT_PROFILE_UPDATED, success
    .catch (error)=>
      console.log error
      @.dispatch billingConstants.BILLING_ERROR, error

  deletePaymentProfile: (paymentProfile)->
    return @app.billingHttpAPI.deletePaymentProfile(paymentProfile)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (success)=>
      @.dispatch billingConstants.PAYMENT_PROFILE_DELETED, paymentProfile
    .catch (error)=>
      console.log error
      @.dispatch billingConstants.BILLING_ERROR, error

module.exports = BillingActionCreators