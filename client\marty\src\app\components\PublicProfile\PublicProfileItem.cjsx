React = require 'react'
Marty = require 'marty'
_ = require 'underscore'


ConfirmationButton = React.createClass
  displayName: 'confirmationButton'

  render: ()->
    <a onMouseDown={@props.onMouseDown} onMouseUp={@props.onMouseUp} className="btn btn-red btn-tiny">Yes</a>

PublicProfileItem = React.createClass
  displayName: 'PublicProfileItem'

  THEMES:
    item:
      main: "item-info"
      sub: "item-status"
    action:
      main: "action action-primary"
      sub: "action action-secondary"
    owner:
      main: "entry-owner"
      sub: undefined
    confirm:
      main: "delete-list-item"
      sub: undefined
  
  getInitialState: ->
    defaultAvatarImage: '/static/images/avatar.png'
    defaultUsername: ''
    requestConfirmation: undefined
    actionConfirmed: undefined
    theme: 'action'

  getDefaultProps: ->
    defaultConfirmMessage: "Unshare entry with this person?"
    theme: 'action'

  componentDidMount: ->
    window.addEventListener 'mousedown', @pageClick, false

  componentWillUnmount: ->
    window.removeEventListener 'mousedown', @pageClick, false

  pageClick: (e)->
    if @mouseIsDownOnConfirmationButton
      @onConfirm()
      return
    @onReject()

  mouseDownHandler: ()->
    @mouseIsDownOnConfirmationButton = true

  mouseUpHandler: ()->
    @mouseIsDownOnConfirmationButton = false

  onReject: ()->
    @setState
      requestConfirmation: undefined
      actionConfirmed: false
      theme: @props.theme

  onConfirm: ()->
    @setState
      actionConfirmed: true
      theme: @props.theme
    , @onDelete

  onRequestDeleteConfirmation: ()->
    @setState
      requestConfirmation: true
      actionConfirmed: false
      theme: 'confirm'

  onDelete: ()->
    if @props.onDelete?
      @props.onDelete(@props.invitation)
      @setState
        requestConfirmation: undefined
        theme: @props.theme

  renderSubsection: ()->
    theme = @THEMES[@state.theme]
    if theme?.sub?
      <div className={theme.sub}>
        {
          if @props.onDelete?
            <div>
              <a onClick={@onRequestDeleteConfirmation} className="icon icon-times"></a>
            </div>
          else if @props.showSuccess
            <div className="icon icon-checkbox-full"></div>
          else if @props.showError
            <div><a className="icon icon-no-full" title={@props.errorMessage}></a></div>
        }   
      </div>
    else
      return undefined

  renderConfirm: ()->
    if @state.requestConfirmation and not @state.actionConfirmed
      <div className="col text confirm">
        <div>
          {@props.defaultConfirmMessage}
        </div>
        <div className="subtext">
          <a onClick={@onReject} className="btn btn-gray btn-tiny">No</a>
          <ConfirmationButton onClick={@onConfirm} onMouseDown={@mouseDownHandler} onMouseUp={@mouseUpHandler} />
        </div>
      </div>

  render: ()->
    theme = @THEMES[@state.theme||@props.theme]
    <div className="list-item">
      <div className={theme.main}>
        <div className="item-wrapper">
          <div className="item-row">
            <div className="col avatar">
              <img className="img-circle" src={@props.profile?.avatar_image_url || @state.defaultAvatarImage} />
            </div>
            <div className="col text">
              <div>{@props.profile?.username || @state.defaultUsername}</div>
              <div className="subtext">{if @props.isOwner then 'Entry Owner' else @props.profile?.email}</div>
            </div>
            {
              @renderConfirm()
            }  
          </div>
        </div>
      </div>
      {
        @renderSubsection()
      }
    </div>

PublicProfileItemContainer = Marty.createContainer PublicProfileItem,
  listenTo: ['publicUserStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      if @props.user?
        if @props.isUser or @props.isOwner
          return {
            profile: @props.app.publicUserStore.getPublicUser(@props.user)
          }
        else if not @props.isUser
          return {
            profile: 
              avatar_image_url: undefined
              username: @props.user
          }
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <PublicProfileItem {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <PublicProfileItem {...props} />
  failed: (error)->
    console.log error
    props = {}
    return <PublicProfileItem {...props} />

module.exports = PublicProfileItemContainer