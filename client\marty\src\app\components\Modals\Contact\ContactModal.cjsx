React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

{
  Modal
} = require('react-bootstrap')

ToggleSwitch = require '../../Utility/ToggleSwitch'

ContactModal = React.createClass
  displayName: 'ContactModal'

  onHide: ()->
    if @props.onHide?
      @props.onHide()

  onSave: ()->
    if @props.onHide?
      @props.onHide()

  onToggleVisibilityState: ()->
    if @props.timelineApproval?
      @props.app.timelineApprovalActionCreators.toggleVisibility(@props.timelineApproval)
  
  render: ()->
    <Modal id="modal_edit_contact" show={@props.show} backdrop={'static'} keyboard={false} onHide={@onHide} {...@props}>
      <div className="modal-header teal-bg">
        <div className="row">
          <div className="col-sm-3">
            <a className="btn-flat pull-left" onClick={@onHide}>Close</a>
          </div>
          <div className="col-sm-6 text-center">
            <div className="modal-title">Contact</div>
          </div>
          <div className="col-sm-3">
            <a className="btn-flat pull-right" onClick={@onSave}>Save</a>
          </div>
        </div>
      </div>
      <div className="modal-body padding-20">
        <div className="contact-wrapper">         
          <div className="col col-left avatar">
            <img className="img-circle" src={@props.public_user.avatar_image_url} />
          </div>
          <div className="col col-right"> 
            <div className="name">{@props.public_user.public_display_name}</div>
            <div className="email">{@props.public_user.email}</div>
          </div>
        </div>
        <hr />
        <ToggleSwitch switchId="toggleVisibility" confirmToggleStateChange={true} onToggle={@onToggleVisibilityState} enabled={@props.timelineApproval?.approval_status} {...@props}/>      
        <label>Visibility</label>
        {
          if @props.timelineApproval?.approval_status
            <p className="help-text">Entries from this person are visible on your timeline.</p>        
          else
            <p className="help-text">Entries from this person are not visible on your timeline.</p>
        }
        <hr />
        
      </div>
    </Modal>

# <label>Shared content and comments</label>
# <p>
#   <a>XX shared Entries</a>
#   <br />
#   <a>XX comments</a>
# </p>

ContactModalContainer = Marty.createContainer ContactModal,
  listenTo: ['publicUserStore', 'timelineApprovalStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()  
      if @props.entry?.public_user?
        return {
          public_user: @props.app.publicUserStore.getPublicUser(@props.entry.public_user)
          timelineApproval: @props.app.timelineApprovalStore.fetchTimelineApprovalsForUser(@props.entry.public_user)
        }
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <ContactModal {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <ContactModal {...props} />
  failed: (error)->
    console.log error
    return <div>Error</div>

module.exports = ContactModalContainer