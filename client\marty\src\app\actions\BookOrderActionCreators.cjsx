Marty = require 'marty'
bookOrderConstants = require '../constants/BookOrderConstants'

BookOrderActionCreators = Marty.createActionCreators
  id: 'BookOrderActionCreators'

  createBookOrder: (bookOrder)->
    return @.app.bookOrderHttpAPI.createBookOrder(bookOrder)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @dispatch bookOrderConstants.BOOK_ORDER_CREATED, success
    .catch (error)=>
      @dispatch bookOrderConstants.BOOK_ORDER_ERROR, error
  
  updateBookOrder: (bookOrder)->
    return @.app.bookOrderHttpAPI.updateBookOrder(bookOrder)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @dispatch bookOrderConstants.BOOK_ORDER_UPDATED, success
    .catch (error)=>
      @dispatch bookOrderConstants.BOOK_ORDER_ERROR, error
  
  deleteBookOrder: (bookOrder)->
    return @.app.bookOrderHttpAPI.updateBookOrder(bookOrder)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (success)=>
      @dispatch bookOrderConstants.BOOK_ORDER_DELETED, bookOrder
    .catch (error)=>
      @dispatch bookOrderConstants.BOOK_ORDER_ERROR, error

  resetBookOrderError: ()->
    @dispatch bookOrderConstants.BOOK_ORDER_RESET_ERROR

  resetBookOrderSuccess: ()->
    @dispatch bookOrderConstants.BOOK_ORDER_RESET_SUCCESS

module.exports = BookOrderActionCreators
