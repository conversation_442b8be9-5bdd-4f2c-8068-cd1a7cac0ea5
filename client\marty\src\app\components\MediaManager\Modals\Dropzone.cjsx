React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

DropZoneComponent = require 'react-dropzone-component'
settings = require '../../../settings'
dropzoneConfig = require './DropzoneConfigs'

Dropzone = React.createClass
  displayName: 'Dropzone'
  
  getInitialState: ->
    dropzone: undefined

  getDefaultProps: ()->
    mediaType: 'default'

  config: ()->
    if dropzoneConfig?
      config = {}
      config = _.defaults config, dropzoneConfig[@props.mediaType].config
      return config

  djsConfig:()->
    if dropzoneConfig?
      config =
        previewTemplate: dropzoneConfig[@props.mediaType].previewTemplate()
        headers:
          'Authorization': @props.app.authStore.getAuth()
        error: (file, message, xhr) ->
          if typeof message != "String" and not message.error?
            message.error = "Upload Failed!"
          
          if file.previewElement
            file.previewElement.classList.add "dz-error"
            message = message.error if typeof message != "String" and message.error
            node.textContent = message for node in file.previewElement.querySelectorAll("[data-dz-errormessage]")
      config = _.defaults config, dropzoneConfig[@props.mediaType].djsConfig
      return config

  dropzoneEventHandlers: ()->
    return {
      init: (dropzone)=>
        # console.log "Dropzone init"
        @setState
          dropzone: dropzone
      complete: (file)=>
        # console.log "Dropzone complete"
        if @props.onComplete?
          @props.onComplete(file)
          console.log file
      processing: (file)=>
        # console.log "Dropzone processing"
        if @props.onProcessing?
          @props.onProcessing(file)
      error: (error)=>
        # console.log "Dropzone error listener"
        if @props.onError?
          @props.onError(error)
      removedfile: (file)=>
        if @props.onRemove
          @props.onRemove(file)
    }
 
  render: ()->
    <div className="dropzone">
      <div className="thumbnail-scroller" id="previews">
      </div>
      <div className="upload-area">
        <div className="dotted-line">
          <DropZoneComponent config={@config()} eventHandlers={@dropzoneEventHandlers()} djsConfig={@djsConfig()}>
            <div className="upload-instructions dz-message">               
              <div className="title">{@config().dropMessage}</div>
              <div className="help-text">OR</div>
              <button type="button" className="btn btn-gray btn-medium">{@config().uploadButtonLabel}</button>
              <div className="help-text">Maximum upload file size: {@djsConfig().maxFilesize} MB.</div>
              <div className="help-text">Available formats: {@djsConfig().acceptedFiles}</div>
            </div>
          </DropZoneComponent>
        </div>
      </div>
    </div>

DropzoneContainer = Marty.createContainer Dropzone,
  # listenTo: ['']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        user: @props.app.authStore.fetchUser()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Dropzone {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Dropzone {...props} />
  failed: (error)->
    console.log error
    return <div>Dropzone Error</div>

module.exports = DropzoneContainer
    
