React = require 'react'
Marty = require 'marty'

ModalHeader = React.createClass
  displayName: "Modal Header"

  propTypes: 
    onDone: React.PropTypes.func
    header: React.PropTypes.string

  onDone: (e)->
    if @props.onDone?
      @props.onDone(e)

  render: ()->
    <div className="modal-header teal-bg">
      <div className="row">
        <div className="col-sm-3">
          <a onClick={@props.onHide} className="btn-flat pull-left"><i className="icon icon-times"></i></a>
        </div>
        <div className="col-sm-6 text-center">
          <div className="modal-title">{@props.header}</div>
        </div>
        <div className="col-sm-3">
          <a onClick={@onDone} className="btn-flat pull-right"><i className="icon icon-check"></i></a>
        </div>
      </div>
    </div>

module.exports = ModalHeader