React = require 'react'
Marty = require 'marty'
moment = require 'moment'
Link = require('react-router').Link
_ = require 'underscore'

# TODO npm package for global namespace access
AuthenticatedComponent = require '../AuthenticatedComponent'
CommentsList = require '../Comment/CommentsList'
TagMenu = require '../Tags/TagMenu'
DeleteModal = require '../Modals/DeleteEntryModal'

InteractionBar = require '../Entry/InteractionBar'
EntryOptionsMenu = require '../Entry/EntryOptionsMenu'

AnswerTile = React.createClass
  displayName: 'AnswerTile'
  mixins: [Marty.createAppMixin()]

  propTypes:
    answer: React.PropTypes.object

  getInitialState: ->
    showDeleteModal: false

  query: ()->
    query = {}  
    if @props.aamId?
      query = {aamId: 1}
    else if @props.journalId?
      query = {journalId: @props.journalId}
    else if @props.tagId?
      query = {tagId: @props.tagId}
    else if @props.searchId?
      query = {searchId: 1}

    return query

  openDeleteModal: ()->
    @setState
      showDeleteModal: true

  closeDeleteModal: ()->
    @setState
      showDeleteModal: false

  renderHeader: ()->
    if @props.public_user?
      <div className="heading-row">
        <div className="col col-avatar">
          <img className="avatar" src={@props.public_user.avatar_image_url} />
        </div>
        <div className="col">
          <div className="name">
            <a onClick={@props.openContactModal.bind null, @props.answer}>
              {
                @props.public_user.public_display_name
              }
            </a>
          </div>
            {
              @renderJournal()
            }
        </div>
      </div>
    else
      <div className="heading-row">
        <div className="col col-avatar">
          <img className="avatar" src={@props.user?.avatar_image_url} />
        </div>
        <div className="col">
          <div className="name">
            <a>
              {
                @app.authStore.getFullNameOrUsername()
              }
            </a>
          </div>
            {
              @renderJournal()
            }
        </div>
      </div>

  renderJournal: ()->
    if @props.journal?.id?
      return (
        <div className="journal">
          <span>in </span> 
          <Link to={"/journal/" + @props.journal?.id}>{@props.journal?.title}</Link>
        </div>
      )
    else
      if @props.public_user?
        return (
          <div className="journal">
            Shared Entry
          </div>
        ) 
      else
        return undefined
    
  renderCategoryHeader: ()->
    <Link to={"/all-about-me/category/" + @props.question?.category.id} className="aam-category"><img className="icon" src="/static/images/icon-all-about-me.svg" />{@props.question?.category.name}</Link>

  render: ()->
    query = @query()
    detailURL = "/all-about-me/answer/"
    if @props.answer?.entry_date and @props.answer?.created
      entry_date = moment.utc(@props.answer?.entry_date)
      created = moment.utc(@props.answer?.created)
    else
      entry_date = undefined
      created = undefined
    <div className="entry tile-view white-bg">
      <div className="top-bar">
        {
          if @state.showDeleteModal
            <DeleteModal show={@state.showDeleteModal} type={'Entry'} object={@props.answer} onHide={@closeDeleteModal} {...@props} />
        }
        <EntryOptionsMenu openDeleteModal={@openDeleteModal} query={@props.query} entry={@props.answer} detailURL={"/all-about-me/answer/"} />
        <div className="entry-date teal-bg fl">
          <Link to={"#{detailURL}" + @props.answer?.id} query={query}><span className="entry-date-date">{entry_date?.local().format('MMM D, YYYY')} </span>
          {
            if @props.answer?.display_time
              <span className="entry-date-time">{entry_date?.local().format('h:mma')}</span>
          }
          </Link>
        </div>
        <div className="date-posted fl">{created.local().fromNow()}</div>
      </div>
      <div id="tile-heading">
        {
          @renderHeader()
        }
      </div>
      <div className="content">
        <div className="main">
          <div className="aam-question-wrapper">
            <div className="inner-wrapper">
              {
                if @props.question?
                  @renderCategoryHeader()
              }
              <div className="aam-tile-question">{@props.answer?.title}</div>
            </div>
          </div>
          <div dangerouslySetInnerHTML={{__html: @props.answer?.content}}></div>
        </div>
      </div>
      <InteractionBar {...@props} entry={@props.answer} resourceURL={detailURL} />
    </div>

AnswerTileContainer = Marty.createContainer AnswerTile, 
  listenTo: ['journalStore','allaboutmeStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      state = {}
      if not @props.answer.isOwnedByMe
         state = _.extend state, {
          public_user: @props.app.publicUserStore.getPublicUser(@props.answer.public_user)
        }

      if @props.answer.journal?
        if _.isString @props.answer.journal
          state = _.extend state, {
            journal: @props.app.journalStore.getAllAboutMeJournal()
          }
        else if _.isNumber(@props.answer.journal)and @props.answer.isOwnedByMe
          state = _.extend state, {
            journal: @props.app.journalStore.getAllAboutMeJournal()
          }
      
      state = _.extend state, {
        question: @props.app.allaboutmeStore.getQuestionByURI(@props.answer.question)
      }
      
      return state
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <AnswerTile {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <AnswerTile {...props}/>
  failed: (error)->
    console.log error
    return <div>Error</div>

module.exports = AnswerTileContainer
