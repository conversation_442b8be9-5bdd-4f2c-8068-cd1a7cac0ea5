React = require 'react'
Marty = require 'marty'
moment = require 'moment'
Link = require('react-router').Link
_ = require 'underscore'

InteractionBar = require './InteractionBar'
EntryOptionsMenu = require './EntryOptionsMenu'

DeleteModal = require '../Modals/DeleteEntryModal'
InvitationModal = require '../Modals/Invitation/InvitationModal'

EntryTile = React.createClass
  displayName: 'Entry'
  mixins: [Marty.createAppMixin()]

  propTypes:
    entry: React.PropTypes.object
    
  getInitialState: ->
    showDeleteModal: false

  query: ()->
    query = {}  
    
    if @props.aamId?
      query = {aamId: 1}
    else if @props.journalId?
      query = {journalId: @props.journalId}
    else if @props.tagId?
      query = {tagId: @props.tagId}
    else if @props.searchId?
      query = {searchId: 1}

    return query

  openDeleteModal: ()->
    @setState
      showDeleteModal: true

  closeDeleteModal: ()->
    @setState
      showDeleteModal: false

  renderTitle: (query)->
    if @props.entry?.title != ""
      return <Link to={"/entry/" + @props.entry?.id} query={query} className="entry-title">{@props.entry.title}</Link>
    else
      return undefined

  renderJournal: ()->
    if @props.journal?.id?
      return (
        <div className="journal">
          <span>in </span> 
          <Link to={"/journal/" + @props.journal?.id}>{@props.journal?.title}</Link>
        </div>
      )
    else
      if @props.public_user?
        return (
          <div className="journal">
            Shared Entry
          </div>
        ) 
      else
        return undefined

  renderHeader: ()->
    if @props.public_user?
      <div className="heading-row">
        <div className="col col-avatar">
          <img className="avatar" src={@props.public_user.avatar_image_url} />
        </div>
        <div className="col">
          <div className="name">
            <a onClick={@props.openContactModal.bind null, @props.entry}>
              {
                @props.public_user.public_display_name
              }
            </a>
          </div>
            {
              @renderJournal()
            }
        </div>
      </div>
    else
      <div className="heading-row">
        <div className="col col-avatar">
          <img className="avatar" src={@props.user?.avatar_image_url} />
        </div>
        <div className="col">
          <div className="name">
            <a>
              {
                @app.authStore.getFullNameOrUsername()
              }
            </a>
          </div>
            {
              @renderJournal()
            }
        </div>
      </div>

  render: ()->
    query = @query()
    if @props.entry?.entry_date and @props.entry?.created
      entry_date = moment.utc(@props.entry?.entry_date)
      created = moment.utc(@props.entry?.created)
    else
      entry_date = undefined
      created = undefined
    <div className="entry tile-view white-bg">
      <div className="top-bar">
        {
          if @state.showDeleteModal
            <DeleteModal show={@state.showDeleteModal} type={'Entry'} object={@props.entry} onHide={@closeDeleteModal} {...@props} />
        }
        <EntryOptionsMenu openDeleteModal={@openDeleteModal} query={@props.query} entry={@props.entry} />
        <div className="entry-date teal-bg fl">
          <Link to={"/entry/" + @props.entry?.id} query={_.extend query, {comments:1}}><span className="entry-date-date">{entry_date?.local().format('MMM D, YYYY')} </span>
          {
            if @props.entry?.display_time
              <span className="entry-date-time">{entry_date?.local().format('h:mma')}</span>
          }
          </Link>
        </div>
        <div className="date-posted fl">{created.local().fromNow()}</div>
      </div>
      <div id="tile-heading">
        {
          @renderHeader()
        }
      </div>
      <div className="content">
        {
          @renderTitle(query)
        }
        <div className="main" dangerouslySetInnerHTML={{__html: @props.entry?.content}}>
        </div>
      </div>
      <InteractionBar {...@props}/>
    </div>

EntryTileContainer = Marty.createContainer EntryTile, 
  listenTo: ['entryStore','journalStore','publicUserStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      state = {}
      
      if @props.entry.journal?
        if _.isString @props.entry.journal
          state = _.extend state, {
            journal: @props.app.journalStore.getJournalByURI(@props.entry.journal)
          }
        else if _.isNumber(@props.entry.journal) and @props.entry.isOwnedByMe
          state = _.extend state, {
            journal: @props.app.journalStore.getJournalById(@props.entry.journal)
          }

      if not @props.entry.isOwnedByMe
         state = _.extend state, {
          public_user: @props.app.publicUserStore.getPublicUser(@props.entry.public_user)
        }
      
      return state
    else
      return {}
  done: (results)->
    props = _.extend {},@props,results
    return <EntryTile {...props}/>
  pending: (fetches)->
    props = _.extend {},@props,fetches
    return <EntryTile {...props}/>
  failed: (error)->
    console.log error
    return <div>Error</div>

module.exports = EntryTileContainer
