React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Link = require('react-router').Link

FormData = require 'react-form-data'

ForgotPassword = React.createClass
  displayName: 'Forgot Password'
  mixins: [FormData, Marty.createAppMixin()]
  
  getInitialState: ->
    success: @props.success

  componentWillMount: ->
    @props.app.authActionCreators.logout()  

  componentWillReceiveProps: (nextProps) ->
    if nextProps.success != @state.success
      @setState
        success: true

  componentWillUnmount: ->
    @props.app.authActionCreators.resetPasswordState()
  
  onRequestPasswordReset: (e)->
    e.stopPropagation()
    e.preventDefault()
    options = {}
    
    if !_.isEmpty(@formData.email)
      options.email = @formData.email
    
    if !_.isEmpty(options)
      @props.app.authActionCreators.requestPasswordReset(options)

  renderError: ()->
    if @props.error
      return <div className="alert danger text-center">{@props.error.user_message}</div>
    else
      return undefined

  removeError: ()->
    # console.log 'removeError'
    @props.app.authActionCreators.resetPasswordState()

  onKeyDown: (e)->
    if e.keyCode == 13
      @onRequestPasswordReset(e)
  
  renderSuccess: ()->
    return (
      
        <div role="tabpanel" className="tab-pane" id="password">
          <div className="message-wrapper">
            <h2>Thank you!</h2>
            <p>Please see your email for instructions on completing the reset of your password.</p>
          </div>
          <div className="form-wrapper">
          </div>
          <div className="access-links">
            <div className="left">
              <Link to={"/login/"}>Login to my account</Link>
            </div>
            <div className="right">
              <Link to={"/signup/"}>Signup Here!</Link>
            </div>
          </div>
        </div>
      
    )
  

  renderForm: ()->
    return (
      <form onChange={@updateFormData} autoComplete={"off"}>
        <div role="tabpanel" className="tab-pane" id="password">
          <div className="message-wrapper">
            <h2>Trouble logging in?</h2>
            <p>No worries, you can reset your password by entering your email address below and clicking "reset password."</p>
          </div>
          <div className="form-wrapper">
            {
              @renderError()
            }
            <div className="form-group">
              <input type="email" className="form-control" name="email" placeholder="Email" onClick={@removeError} onKeyDown={@onKeyDown} />
              <a onClick={@onRequestPasswordReset} className="btn btn-navy btn-med full-width">Reset Password</a>
            </div>
          </div>
          <div className="access-links">
            <div className="left">
              <Link to={"/login/"}>Login to my account</Link>
            </div>
            <div className="right">
              <Link to={"/signup/"}>Signup Here!</Link>
            </div>
          </div>
        </div>
      </form>
    )
  
  render: ()->
    if @props.success
      content = @renderSuccess()
    else
      content = @renderForm()
    <div>
      <div id='access'>
        <div className='container' style={maxWidth: "350"}>
          <div className="panel frame">
            <div className="panel-body">
              <Link to={"/"} className="jrnl-logo"><img src="/static/images/jrnl-logo.png"/></Link>      
              {
                content
              }
            </div>
          </div>
        </div>
      </div>
    </div>

ForgotPasswordContainer = Marty.createContainer ForgotPassword,
  listenTo: ['authStore']

  fetch: ()->
    return {
      success: @props.location.pathname == '/forgot-password/success/'
      error: @app.authStore.getError()
    }
  done: (results)->
    props = _.extend {}, @props, results
    return <ForgotPassword {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <ForgotPassword {...props}/>
  failed: (error)->
    console.log error
    return <div>Error</div>
module.exports = ForgotPasswordContainer