React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
createFragment = require 'react-addons-create-fragment'

Select = require 'react-select'
TagValue = require './TagValue'

TagSelect = React.createClass
  displayName: 'TagSelect'

  getInitialState: ->
    isActive: false
    isLoading: false
  
  componentWillReceiveProps: (nextProps) ->
    # Load all the tags until no more remain
    if nextProps.page?
      if nextProps.page.next != null
        if nextProps.tags?
          if nextProps.page.total_count == nextProps.tags.length
            @setState
              isLoading: false    
          else if nextProps.page.total_count > nextProps.tags.length
            if nextProps.page.next != @props.page.next or !@state.isLoading
              @setState
                isLoading: true
              , ()-> @app.tagsQueries.getPage(nextProps.page.next, 'Tags')
      else
        @setState
          isLoading: false
          
    if nextProps.tags?
      if (nextProps.entryTags? or @props.entryTags?) and not @state.selectedTags?
        entryTags = nextProps.entryTags || @props.entryTags
        tags = nextProps.tags || @props.tags
        @setState
          selectedTags: @getTags(entryTags, tags)

      if nextProps.tags?.length != @state.number_of_tags
        @checkSelectedTags(nextProps.tags)

    if nextProps.lastSaved?
      if @props.lastSaved?
        if not nextProps.lastSaved.isSame(@props.lastSaved)
          @setState
            isActive: false
      else
        @setState
          isActive: false
  
  componentDidUpdate: (prevProps, prevState) ->
    # For some damn reason this is necessary to detect when tags has changed...it's also not solid or good in anyway. Investigate
    @state.number_of_tags = @props.tags?.length
    if @state.focusAfterUpdate and @state.isActive
      @refs.select.focus()
      @setState
        focusAfterUpdate: false
  
  
  checkSelectedTags: (newTags)->
    if @state.selectedTags?
      newSelectedTags = []
      tags = _.pluck @state.selectedTags, 'id'
      for tag in tags
        type = tag.split('::')[0]
        switch type
         when 'tag'
            id = tag.split('::')[1]
            selectedTag = _.findWhere newTags, {id: parseInt(id)}
            if selectedTag?
              option = 
                id: 'tag::' + selectedTag.id
                name: selectedTag.name
              
              newSelectedTags.push option
            break
           when 'create'
            name = tag.split('::')[1]
            selectedTag = _.findWhere newTags, {name: name}
            if selectedTag?
              option = 
                id: 'tag::' + selectedTag.id
                name: selectedTag.name          
              newSelectedTags.push option
            else
              option = 
                id: "create::" + name
                name: name
              newSelectedTags.push option
            break
      @setState
        selectedTags: newSelectedTags
      , @onUpdateTags

  options: (tags, prefix)->
    options = []
    prefix = prefix || ''

    if tags?
      for tag in tags
        option = 
          value: prefix + tag.id
          label: tag.name
        options.push option
      return options
    return undefined

  getAllTagsAsOptions: ()->
    if @props.tags?
      return @options(@props.tags, 'tag::')
    else
      return undefined

  getTags: (entryTags, allTags)->
    allTags = allTags || @props.tags
    if entryTags? and allTags?
      tags = []
      for entryTag in entryTags
        tag = _.findWhere allTags, {resource_uri: entryTag}
        if tag?
          option = 
            id: 'tag::' + tag.id
            name: tag.name
          
          tags.push option
      return tags
    else
      return undefined

  getSelectedTagsAsOptions: ()->
    if @state.selectedTags?
      return @options(@state.selectedTags)
    else
      return undefined

  getValues: ()->
    if @props.tags? and not @state.isLoading
      return @getSelectedTagsAsOptions()
    else
      return undefined

  newOptionCreator: (inputValue)->
    return {
      label: inputValue
      value: 'create::'+inputValue
      create: true
    }

  filterOptions: (options, filterString, values)->
    filteredOptions = options
    
    if filterString.length > 0 
      filteredOptions = filteredOptions.map (option, i)=>      
        filter = option.label.substring 0, (filterString.length)        
        if filter isnt filterString
          return undefined
        else
          return option
    
    filteredOptions = _.reject filteredOptions, (option)->
      if option?
        if option.value in values
          return true
    
    filteredOptions = _.compact filteredOptions
    filteredOptions = _.sortBy filteredOptions, 'label'

    return filteredOptions

  onChange: (newValue)->
    selectedTags = @state.selectedTags || []
    newSelectedTags = []
    if newValue.length > 0
      values = newValue.split(',')

      for value in values
        type = value.split('::')[0]
        switch type
          when 'tag'
            id = value.split('::')[1]
            selectedTag = _.findWhere @props.tags, {id: parseInt(id)}
            if selectedTag?
              option = 
                id: 'tag::' + selectedTag.id
                name: selectedTag.name
              
              newSelectedTags.push option
            break
          when 'create'
            name = value.split('::')[1]
            selectedTag = _.findWhere @props.tags, {name: name}
            if selectedTag?
              option = 
                id: 'tag::' + selectedTag.id
                name: selectedTag.name          
              newSelectedTags.push option
            else
              @.app.tagActionCreators.createTag({name: name})
              option = 
                id: "create::" + name
                name: name
              newSelectedTags.push option
            break

      newSelectedTags = _.uniq newSelectedTags, (item)->
        return item.id
    
    @setState
      selectedTags: newSelectedTags
    , @onUpdateTags
    

  onUpdateTags: ()->
    if @props.onUpdateTags?
      addTags = []
      createTags = []
      tags = _.pluck @state.selectedTags, 'id'
      for tag in tags
        type = tag.split('::')[0]
        switch type
          when 'tag'
            id = tag.split('::')[1]
            selectedTag = _.findWhere @props.tags, {id: parseInt(id)}
            if selectedTag?
              addTag =
                id: selectedTag.id
                resource_uri: selectedTag.resource_uri
              addTags.push addTag
          when 'create'
            name = tag.split('::')[1]
            createTag = 
              name: name
            createTags.push createTag
      @props.onUpdateTags(addTags, createTags)

  onAddTag: ()->
    if @props.tags? and (@props.entry? or @props.mode == 'add')
      if not @state.isActive
        @setState
          isActive: true
          focusAfterUpdate: true

  renderAddTag: (tags)->
    if @state.selectedTags?
      if @state.selectedTags.length > 0
        tags.push <a key={'tag-plus-icon'} className="add-tag" onClick={@onAddTag}><i className="icon-plus"></i></a>
        return tags
    else
      tags.unshift <a key={'add-tags'} className="add-tag" onClick={@onAddTag}>Add tags</a>
      return tags

  renderSelect: ()->
    if @state.isActive
      return (
        <Select 
          ref="select"
          options={@getAllTagsAsOptions()} 
          multi={true}
          placeholder={''}
          className={'search'}
          filterOptions={@filterOptions}
          isLoading={@state.isLoading}
          onChange={@onChange}
          newOptionCreator={@newOptionCreator}
          allowCreate={true}
          valueKey={'value'}
          value={@getValues()}
          valueComponent={TagValue}
          inputProps={placeholder: 'Add tags'}
        />
      )
    else
      tags = undefined
      if @state.selectedTags?.length > 0
        tags = @state.selectedTags.map (tag) ->
          return <div key={tag.name} className="tag">{tag.name}<a className=""></a></div>
        tags = @renderAddTag(tags)
      else
        if @props.mode == 'add' or @props.entryTags?.length == 0
          tags = <a className="add-tag" onClick={@onAddTag}>Add tags</a>
      return (
        <div className="tag-wrapper">
          {tags}
        </div>
      )
      
  render: ()->
    <div id="tag_box">
      {
        @renderSelect()
      }  
    </div>
    
TagSelectContainer = Marty.createContainer TagSelect,
  listenTo: ['tagsStore']

  fetch: ()->
    return {
      tags: @.app.tagsStore.getTags('Tags')
      page: @.app.pageStore.getPage('Tags')
    }
  done:(results)->
    props = _.extend {}, @props, results
    return <TagSelect {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <TagSelect {...props} />
  failed: (error)->
    console.log error
    return <div>Tag Select Error</div>


module.exports = TagSelectContainer