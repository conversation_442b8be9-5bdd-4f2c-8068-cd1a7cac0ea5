/*---------------------------------------------------------------------------
  >Calendar
---------------------------------------------------------------------------*/

#calendar-widget {
	.white-bg;
	
	.dp-year, .dp-month, .dp-day {
		background-clip: content-box;
    border-radius: 0px;
    
		&.dp-cell {
			margin: 0;
			.hover-teal-txt;
			.navy-txt;
			.font2;
			.f12;
			
			&.entry {border-bottom-color: @teal;}
		}
	}
	
	.dp-value, .dp-selected {
		background-color: @teal !important;
		.white-txt !important;
		.w600;
	} 
	
	.dp-force-display-none {
		display: none !important;
	}
	
	.dp-header, .dp-body, .dp-footer {.full-width;}
	
	.dp-header {
		border-bottom: 1px solid @gray9;
		
		.dp-cell {
			cursor: pointer;
			.navy-txt;
			.hover-teal-txt;
			.font1;
			
			.icon {
				.navy-txt;
				
				&:hover {
					.teal-txt;
				}
			}
		}
		
		.dp-prev-nav {
			text-align: left;
		}
		
		.dp-nav-view {
			text-align: center;
			.w600;
		}
		
		.dp-next-nav {
			text-align: right;
		}
	}
	
	.dp-body {
		
		.dp-week-day-names {
			
			.dp-cell {
				color: @gray5;
				.w700;
				.font1;
			}
		}
		
		.dp-current {
			.white-txt;
			.navy-bg;
		}
		
		.dp-day {border-bottom: 2px solid @gray10;}
				
		.dp-next, .dp-prev {
	    //border-radius: 0 !important;
			//background-color: @gray1;
			color: @gray7;
			.font1;
			.w300;
		}
				
		.dp-table {
			
			.dp-week-day-names {
				border-bottom: 1px solid @gray9;
			  padding: 2px 0;
			  cursor: default;
				.f9;
				.font1;
				.w600;
				.uppercase;
				.gradient-g-w;
				
				.dp-week-day-name {color: @gray6;}
			}
		}
		
		.dp-before-min, .dp-after-max {
		  color: white;
		  border-color: white;
			background-color: white !important;
		}
	}
	
	.dp-footer {
		
		> div {
		  text-align: center;
		  width: 100%;
		  .f12;
			.w600;
			.navy-txt;
			
			&:hover {
				cursor: pointer;
				.teal-txt;
			}
		}
	}
}

/*---------------------------------------------------------------------------
  >React Date Picker
---------------------------------------------------------------------------*/

// Override inline styles to ensure that IE11/IE10 flex bug does not occur
div[style="flex:1 1 0px;"][class="dp-body"] {
  flex: 1 0 auto !important;
  -ms-flex: 1 0 auto !important;
}

.box-flex {
  flex: 1;
  -moz-box-flex: 1;
  -ms-box-flex: 1 0 auto;
  -ms-flex: 1 0 auto;
  -webkit-flex: 1;
  -webkit-box-flex: 1;  
}

.flex-flow-column {
  flex-direction: column;
  -webkit-flex-flow: column;
  -moz-flex-flow: column;
  -ms-flex-flow: column;
  -o-flex-flow: column;
  flex-flow: column;
  flex-flow: column;
}

.flex-flow-row {
  flex-direction: row;
  -webkit-flex-flow: row;
  -moz-flex-flow: row;
  -ms-flex-flow: row;
  -o-flex-flow: row;
  flex-flow: row;
  flex-flow: row;
}

.flex-display {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

.date-picker {
  .flex-display;
  .flex-flow-column;
  .box-flex;
}
.date-picker,
.date-picker * {
  box-sizing: border-box;
}
.date-picker .dp-footer {
  .flex-flow-row;
  .flex-display;
}
.date-picker .dp-body {
  .flex-display;
  .flex-flow-column;
  .box-flex;
}
.date-picker .dp-table {
  width: 100%;
  height: 100%;
  .flex-display;
  .flex-flow-column;
	.box-flex;
}
.date-picker .dp-row {
  .flex-display;
  .flex-flow-row;
  .box-flex;
}
.date-picker .dp-cell {
  .flex-display;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  -webkit-justify-content: center;
  -webkit-box-pack: center;
  flex-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  .box-flex;
}
.date-picker .dp-nav-table {
  .flex-display;
  .flex-flow-row;
  .box-flex;
  width: 100%;
}
.date-picker .dp-nav-table .dp-cell {
  flex: 7;
  -moz-box-flex: 7;
  -ms-box-flex: 7 0 auto;
  -ms-flex: 7 0 auto;
  -webkit-flex: 7;
  -webkit-box-flex: 7;
}
.date-picker .dp-nav-table .dp-cell:first-child,
.date-picker .dp-nav-table .dp-cell:last-child {
  .box-flex;
}
.date-picker {
  overflow: hidden;
  background: #fff;
  font-size: 14px;
  width: 100%;
  height: 100%;
}
.date-picker .dp-table .dp-cell {
  cursor: pointer;
  padding: 5px;
  background: inherit;
}
.date-picker .dp-table .dp-cell.dp-prev,
.date-picker .dp-table .dp-cell.dp-next {
  color: #5c5c5c;
  background: inherit;
}
.date-picker .dp-table .dp-cell.dp-in-range {
  background: #e2f0ff;
}
.date-picker .dp-table .dp-cell:hover {
  color: inherit;
  font-weight: inherit;
}
.date-picker .dp-table .dp-cell.dp-disabled {
  cursor: default;
  color: #adadad;
  background: inherit;
}
.date-picker .dp-table .dp-cell.dp-value {
  color: #000;
  font-weight: bold;
}
.date-picker .dp-table .dp-cell.dp-current {
  color: #2e99eb;
  background: inherit;
}
.date-picker .dp-table .dp-cell.dp-in-range.dp-current,
.date-picker .dp-table .dp-cell.dp-in-range.dp-value {
  background: #e2f0ff;
}
.date-picker .dp-table .dp-cell.dp-month {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.date-picker .dp-table .dp-cell.dp-week-day-name {
  font-weight: bold;
  cursor: default;
  background: inherit;
}
.date-picker .dp-footer {
  -webkit-justify-content: center;
  -webkit-box-pack: center;
  flex-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  border-top-width: 0px;
}
.date-picker .dp-footer .dp-footer-selected,
.date-picker .dp-footer .dp-footer-today {
  cursor: pointer;
}
.date-picker .dp-body {
  overflow: hidden;
}
.date-picker .dp-nav-view,
.date-picker .dp-nav-cell,
.date-picker .dp-week-day-name {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}
.date-picker .dp-nav-view,
.date-picker .dp-nav-cell {
  cursor: pointer;
}
.date-picker .dp-nav-cell {
  background: inherit;
}
.date-picker .dp-nav-view {
  background: inherit;
}
.date-picker .dp-nav-view:hover {
}
.date-picker .dp-nav-table .dp-cell {
  padding: 8px;
  font-weight: bold;
}
.date-picker .dp-decade-view,
.date-picker .dp-year-view,
.date-picker .dp-month-view {
  touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}
