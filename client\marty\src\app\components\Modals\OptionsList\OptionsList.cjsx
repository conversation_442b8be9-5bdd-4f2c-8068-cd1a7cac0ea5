React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

OptionsList = React.createClass
  displayName: "Options List"

  propTypes:
    header: React.PropTypes.string
    optionComponent: React.PropTypes.func
    options: React.PropTypes.array
    selectedOptions: React.PropTypes.array
    onOptionSelected: React.PropTypes.func
    optionsSortBy: React.PropTypes.string
    allowNew: React.PropTypes.bool
    newType: React.PropTypes.string
    
  
  getInitialState: ->
    showNew: false
  
  onNew: ()->
    @setState
      showNew: !@state.showNew

  onDone: ()->
    if @.refs.form.formData?
      if @.refs.form.formData != ""
        object = @.refs.form.formData
        if @props.createAction?
          @props.createAction(object)
          @setState
            showNew: false

  renderNewButton: ()->
    if @props.allowNew? and @props.newType?
      if @state.showNew
        return (
          <span className="pull-right"><button className="btn btn-navy btn-small" onClick={@onDone}>Add Tag</button></span>
        )
      else
        return (
          <span className="pull-right" onClick={@onNew}>New {@props.newType}</span>
        )

  renderNewForm: ()->
    if @state.showNew
      if @props.createForm?
        return (
          <li>
            {
              React.createElement @props.createForm, {"ref":"form", 'showSave': true, 'onSave': @onDone}
            }
          </li>
        )
      else
        return undefined
    else
      return undefined
        
  renderHeader: ()->
    header = @props.header
    newButton = @renderNewButton()
    if @props.header? and newButton?
      return (
        <li key='sub-heading' className="sub-heading">{header} {newButton}</li>
      )
    else
      return undefined

  render: ()->
    if @props.isEditable?
      listClassName = 'list editable'
    else
      listClassName = 'list'
    
    <div>
      <ul className={listClassName}>
        {
          @renderHeader()
        }
        {
          @renderNewForm()
        }
        {
          if @props.optionsSortBy? and @props.options?
            options = _.sortBy @props.options, (option)=>
              return option[@props.optionsSortBy].toLowerCase()
          else
            options = @props.options

          if @props.optionsSortBy? and options?
            options.map (option, i)=>      
              if @props.query?
                filter = option[@props.optionsSortBy].substring 0, @props.query.length
                if filter isnt @props.query
                  return undefined

              if _.findWhere @props.selectedOptions, {id: option.id}
                selected = true
                className = "selected"

              return (
                <li key={"option" + option.id} className={className}>
                  {
                    React.createElement @props.optionComponent, _.extend({},@props, {key: 'option'+ option.id, option:option, isSelected:selected, onOptionSelected: @props.onOptionSelected})
                  }
                </li>
            )
        }
      </ul>
    </div>

module.exports = OptionsList