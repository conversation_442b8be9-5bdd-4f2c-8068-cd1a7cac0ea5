/*---------------------------------------------------------------------------
  >Lists
---------------------------------------------------------------------------*/

.list {
	color: @gray7;
	
	li>a, li>div {
	  .full-width;
	  display: block;
	  padding: 14px 70px 14px 44px;
	}		
	
	li {
		.border-btm;
		.full-width;
			
		.sub-text {
			display: block;
			color: @gray5;
		  .f11;
			.w400;
		}
		
		.icon {
			.f16;
		}
		
		a, div {
			.navy-txt;
			.w600;
			.hover-teal-txt;
		}
		
		a:hover .icon, div:hover .item-icon-left .icon {
			.teal-txt;
		}	
		
		.item-icon-left .icon, .item-icon-right .icon {
			top: 12px;
		}
		
		.item-icon-left {
		  position: absolute;
		  top: 0;
		  left: 10px;
		  width: 25px;
		  text-align: center;
		  
			.icon {
				.navy-txt;
			}
		}
		
		.item-icon-right {
		  position: absolute;
		  right: 14px;
		  top: 0;
		}
		
		.icon-share {
		  margin-left: 15px;
		}
	}
	
	.icon-angle-right {
		.fr;
		color: @gray8;
		margin-left: 10px;
	}
}

#page .list {
	display: table;
	.radius3;
	.full-width;
	.white-bg;
	
	li {
		.fl;
		.full-width;
		
		a, .item {
			.w600;
			padding-top: 14px;
			padding-bottom: 14px;
		}
		
		.sub-text {.f13;}
	}	
}

/************** >Other Styles **************/

.sub-heading {
  color: @gray5;
  padding: 5px 12px;
  .gradient-w-g;
  .uppercase;
  .border-btm;
  .w700;
  .f11;
}

li:last-child {
	border-bottom: none !important;
}

.list.multiselect li a:hover {
  border: none;
}

.search input[type="text"] {
  .full-width;
  border-width: 0 0 1px 0;
  border-style: solid;
  border-color: @gray9;
  padding: 12px;
}
