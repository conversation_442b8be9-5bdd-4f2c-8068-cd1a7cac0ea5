React = require 'react'
Marty = require 'marty'
FormData = require 'react-form-data'

OptionsListSearch = React.createClass
  displayName: "Option Search"
  mixins: [FormData]

  propTypes:
    onSearch: React.PropTypes.func
    searchPlaceholder: React.PropTypes.string
  
  formDataDidChange: (formData) ->
    # console.log "Form Data changed"
    # console.log @formData
    return

  onSearch: (e)->
    query = e.target.value
    if @props.onSearch
      @props.onSearch(query)

  render: ()->
    <div className="search" onChange={@.updateFormData}>
      <input ref="optionSearchInput" name="optionSearchInput" className="full-input" type="text" placeholder={@props.searchPlaceholder}} value={@props.query} onChange={@onSearch}/>
    </div>

module.exports = OptionsListSearch