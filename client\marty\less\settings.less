/*---------------------------------------------------------------------------
  >Settings
---------------------------------------------------------------------------*/

.settings-column {
	
	.frame {
		display: table;
		height: 100%;
	}
	
	.btn {z-index: 5;}
	
	.settings-wrapper {
		padding: 30px 40px;
		.table-wrapper;
		
		.progress {max-width: 70% !important;}
	}
	
	.nav-tabs, .tab-content {.table-cell;}
	
	.nav-tabs {
		width: 220px;
    border-radius: 3px 0 0 3px;
    vertical-align: top;
    
    @media @xs {
	    width: 100%;
	    display: block;
    }
    
    li:first-child a {
	    border-radius: 3px 0 0 0 !important;
    }
	}
	
	.tab-content {
    border-radius: 0 3px 3px 0;
		.white-bg;
		
    @media @xs {
	    width: 100%;
	    display: block;
    }
		
		.form-group {
			
			label {display: block;}
			
			input[type="text"] {.full-width;}
		}
	}

	.avatar-area {
			
		.table-cell {
			vertical-align: middle;
			
			@media @xs {display: block;}
		}
		
		.avatar {
			width: 200px;
			
			@media @xs {height: 190px;}
			
			img {
				margin: 20px 0;
				border-radius: 100px;
			  width: 150px;
				height: 150px;
			}
		}
		
		.profile-info {
			padding: 0 0 1em;
			
	    @media @xs {
		    width: 100%;
		    display: block;
	    }
			
			.name {
				font-size: 1.5em; 
				font-weight: 600;
				padding: 0 0 .1em;
			}
			
			.email {
				font-size: 1.1em; 
				.gray5-txt;
			}
		}
	}
	
	.panel {
		
		.options {display: none;}
		
		&:hover .options {
			display: block;
		}
	}

	.input-group-addon {
	    min-width: 100px;
	    font-size: 88%;
	}
}

.panel {
	
	.panel__title {font-weight: 700;}
	
	.panel__text {}
}

.panel--etj {
	
	.panel__title {
		margin-bottom: 3px;
	}
}

.panel__options {
	margin-top: 3px;

	button {
		text-transform: uppercase;
		font-size: 10px;
    height: 34px;
    padding-left: 10px;
    padding-right: 10px;
		
		.fa, .icon {
			
			@media @x-lg {font-size: 14px;}
			@media @lg-x {margin-right: 5px;}
		}
		
		&:hover, &:focus {color: @teal;}
	}

	&.panel__options--small {
		
		@media @lg-x {display: none;}
	}
	
	&.panel__options--large {
		
		@media @x-lg {display: none;}
	}
}