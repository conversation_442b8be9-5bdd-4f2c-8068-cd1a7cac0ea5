React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Modal = require('react-bootstrap').Modal
ImportMain = require './ImportMain'
ImportPreview = require './ImportPreview'
ImportConnect = require './ImportConnect'

ImportModal = React.createClass
  displayName: 'ImportModal'

  getInitialState: ->
    step: 'main'

  onSetStep: (step)->
    @setState
      step: step
  
  render: ()->
    if _.isEmpty @props.instagram_access_token
      dialogClass = "modal-import dialog--connect"
    else
      switch @state.step
        when 'main'
          dialogClass = "modal-import dialog--import"
        when 'preview'
          dialogClass = "modal-import dialog--preview"
      
    <Modal className={dialogClass} enforceFocus={false} backdrop={'static'} id="modal_import" onEntered={@onEntered} {...@props}>
      {
        if _.isEmpty @props.instagram_access_token
          <ImportConnect {...@props} onSetStep={@onSetStep}/>
        else 
          switch @state.step
            when 'main'
              <ImportMain {...@props} onSetStep={@onSetStep}/>
            when 'preview'
              <ImportPreview {...@props} onSetStep={@onSetStep}/>
        
      }
    </Modal>

ImportModalContainer = Marty.createContainer ImportModal,
  listenTo: ['socialStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        instagram_access_token: @props.app.socialStore.getInstagramAccessToken()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <ImportModal {...props} /> 
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <ImportModal {...props} />
  failed: (error)->
    return <div>MODAL ERROR</div>


module.exports = ImportModalContainer