React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

Link = require('react-router').Link

CategoryTile = React.createClass
  displayName: 'CategoryTile'

  getCategorySVG: ()->
    if @props.category?
      category_src = "/static/images/icons/icon-" + @props.category.name.split(" ").join('').toLowerCase() + ".svg"
      return <img src={category_src}/>
    else
      return undefined

  render: ()->
    <li>
      <Link to={"/all-about-me/category/" + @props.category.id}>
        <div className="cat-icon">
          {
            @getCategorySVG()
          }
        </div>
        <h2>{@props.category.name}</h2>
        <span className="sub-text">{@props.category.description}</span>
      </Link>
    </li>

AllAboutMeSidebar = React.createClass
  displayName: 'AllAboutMe Sidebar'

  render: ()->
    <div id="default-sidebar" className="pull-right">
      <div id="category_tile" className="frame">
        <ul className="list">
          {
            if @props.categories?
              @props.categories.map (category)->
                if category.name not in ['Favorites','Other']
                  <CategoryTile key={category.id} category={category} />
          }
          
        </ul>
      </div>
    </div>

AllAboutMeSidebarContainer = Marty.createContainer AllAboutMeSidebar,
  listenTo: ['allaboutmeStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn()
      return {
        categories: @.props.app.allaboutmeStore.getCategories()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <AllAboutMeSidebar {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <AllAboutMeSidebar {...props} />
  failed: (error)->
    console.log error
    return <div>AllAboutMe Sidebar error</div>

module.exports = AllAboutMeSidebarContainer