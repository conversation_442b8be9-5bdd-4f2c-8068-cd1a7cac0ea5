React = require 'react'
classnames = require 'classnames'

InvitationSelectValue = React.createClass
  displayName: 'InvitationSelectValue'

  blockEvent: (e)->
    e.stopPropagation()

  handleOnRemove: (e)->
    if !@props.disabled
      @props.onRemove(e)

  render: ()->
    label = @props.option?.label
    <div className={classnames('Select-item', @props.option?.className)}
       style={@props.option?.style}
       title={@props.option?.title}>
      <span className="Select-item-icon icon-times-circle"
        onMouseDown={@blockEvent}
        onClick={@handleOnRemove}
        onTouchEnd={@handleOnRemove}></span>
      <span className="Select-item-label">{label}</span>
    </div>
    

module.exports = InvitationSelectValue