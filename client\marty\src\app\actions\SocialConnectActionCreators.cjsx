Marty = require 'marty'
_ = require 'underscore'
socialConnectConstants = require '../constants/SocialConnectConstants'

SocialConnectActionCreators = Marty.createActionCreators
  setInstagramCode: (code)->
    @dispatch socialConnectConstants.INSTAGRAM_CODE_RECEIVED, code
    @authorizeInstagram(code)
    
  authorizeInstagram: (code)->
    return @app.socialConnectHttpAPI.authorizeInstagram(code)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (success)=>
      @.dispatch socialConnectConstants.INSTAGRAM_AUTHORIZATION_SUCCESS, success
    .catch (error)=>
      console.log error
      @.dispatch socialConnectConstants.SOCIAL_CONNECT_ERROR, error

  startMediaImport: ()->
    @dispatch socialConnectConstants.TOGGLE_REFRESH_CONTENT
    return @app.socialConnectHttpAPI.startMediaImport()
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      return response.json()
    .then (success)=>
      @.dispatch socialConnectConstants.REQUEST_INSTAGRAM_MEDIA_IMPORT, success
    .catch (error)=>
      console.log error
      @.dispatch socialConnectConstants.INSTAGRAM_ACCESS_TOKEN_ERROR, error

  deleteAccessToken: (resource_uri)->
    return @app.socialConnectHttpAPI.deleteAccessToken(resource_uri)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (success)=>
      @.dispatch socialConnectConstants.INSTAGRAM_ACCESS_TOKEN_DELETE_SUCCESS, success
    .catch (error)=>
      @.dispatch socialConnectConstants.SOCIAL_CONNECT_ERROR, error

  selectInstagramMedia: (media, checked)->
    @dispatch socialConnectConstants.SELECT_INSTAGRAM_MEDIA, media, checked

  bulkSelectInstagramMedia: (media)->
    @dispatch socialConnectConstants.BULK_SELECT_INSTAGRAM_MEDIA, media

  createEntryImages: (entry_images)->
    @dispatch socialConnectConstants.ENTRY_IMAGES_PENDING
    promises = entry_images.map (entry_image)=> 
        @app.socialConnectHttpAPI.createEntryImage entry_image
    return Promise.all(promises)
    .then (responses)=>
      errors = _.findWhere(responses, {ok: false})
      if errors?
        throw responses
      else
        return responses
    .then (responses)=>
      Promise.all(responses.map (response)-> response.json())
      .then (entry_images)=>
        @dispatch socialConnectConstants.ENTRY_IMAGES_RECEIVED, entry_images
    .catch (error)=>
      @dispatch socialConnectConstants.SOCIAL_CONNECT_ERROR, error

  updateEntryImagesWithEntry: (entry, entry_images)->
    promises = entry_images.map (entry_image)=> 
        @app.socialConnectHttpAPI.updateEntryImageWithEntry entry, entry_image
    return Promise.all(promises)
    .then (responses)=>
      errors = _.findWhere(responses, {ok: false})
      if errors?
        throw responses
      else
        return responses
    .then (responses)=>
      Promise.all(responses.map (response)-> response.json())
      .then (entry_images)=>
        @dispatch socialConnectConstants.ENTRY_IMAGES_RECEIVED, entry_images
        @app.socialConnectQueries.getInstagramMedia()
    .catch (error)=>
      @dispatch socialConnectConstants.SOCIAL_CONNECT_ERROR, error

  updateImportFilter: (value)->
    @dispatch socialConnectConstants.IMPORT_FILTER_UPDATED, value

  searchWithinResults: (value)->
    @dispatch socialConnectConstants.SEARCH_WITHIN_RESULTS, value

  updateFrequency: (access_token, value)->
    return @app.socialConnectHttpAPI.updateFrequency(access_token.resource_uri, {update_frequency: value})
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (response)=>
      @.dispatch socialConnectConstants.INSTAGRAM_ACCESS_TOKEN_RECEIVED, response
    .catch (error)=>
      console.log error
      @.dispatch socialConnectConstants.SOCIAL_CONNECT_ERROR, error

  toggleRefreshContentLoadingState: ()->
    @dispatch socialConnectConstants.TOGGLE_REFRESH_CONTENT
  
module.exports = SocialConnectActionCreators