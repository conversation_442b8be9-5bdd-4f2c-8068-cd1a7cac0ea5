React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'

CommentOptionMenu = require './CommentOptionMenu'

Avatar = (props)->
  <div className="col avatar">
    <img className="avatar" src={props.avatarImageURL} />
  </div>

CommentHeader = React.createClass
  displayName: 'CommentHeader'

  getCreated:()->
    created_duration = moment.duration moment.utc(@props.comment?.created).local() - moment.utc().local()
    if created_duration.asMilliseconds() > 0
      return 'just now'
    return created_duration.humanize(true)

  render: ()->
    <div className="comment-heading">
      <div className="ch-row">
        <Avatar avatarImageURL={@props.commentUser?.avatar_image_url} />
        <div className="col content">
          <a className="name">{@props.commentUser?.public_display_name}</a>
          <span className="date">{@getCreated()}</span>
        </div>
        <div className="col options">
          <CommentOptionMenu app={@props.app} comment={@props.comment} entry={@props.entry} onEdit={@props.onEdit} onDelete={@props.onDelete} onTogglePublish={@props.onTogglePublish} isEditable={@props.isEditable}/>
        </div>
      </div>
    </div>

CommentHeaderContainer = Marty.createContainer CommentHeader,
  listenTo: ['publicUserStore', 'commentStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      fetchState = {}
      
      if @props.comment?
        if @props.comment?.user is @props.app.authStore.getUserURI()
          fetchState = 
            commentUser: @props.app.authStore.getUser()
        else
          fetchState =         
            commentUser: @props.app.publicUserStore.getPublicUser(@props.comment?.user)

      return fetchState
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <CommentHeader {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <CommentHeader {...props} />
  failed: (error)->
    console.log error
    return <div>CommentHeader Error</div>

module.exports = CommentHeaderContainer