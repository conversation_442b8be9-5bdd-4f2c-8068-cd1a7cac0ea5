React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

CalendarWidget = require './Widgets/CalendarWidget'
AutoAffix = require('react-overlays').AutoAffix

SidebarLayout = React.createClass
  displayName: 'SidebarLayout'

  widgetCallback: (widget, value)->
    if @props.widgetCallback?
      @props.widgetCallback(widget, value)
  
  render: ()->
    <div id="default-sidebar" className="pull-right">
      <AutoAffix container={document.body} viewportOffsetTop={60} autoWidth={true}>
        <div>
          {
            if React.Children.count(@props.children) is 0
              <CalendarWidget widgetCallback={@onWidgetCallback} {...@props} />          
            else
              @props.children
          }
        </div>
      </AutoAffix>
    </div>
    

module.exports = SidebarLayout