/*---------------------------------------------------------------------------
  >Drawer Comments Header
---------------------------------------------------------------------------*/

#drawer-comments-header {
  border-radius: 3px 3px 0 0;
	background: white;
  display: table;
  table-layout: fixed;
  z-index: 20;
	
	&.affix-top {
    top: 0px;
    bottom: 0;
	}
	
	&.affix {
    top: 70px;
    bottom: 0;
	}

	&.comments-header-list .subtitle-row {background: white;}	
	&.comments-header-personal .subtitle-row {background: @color-personal;}
	&.comments-header-group .subtitle-row {background: @color-group;}
	&.comments-header-1on1 .subtitle-row {background: @color-1on1;}
			
	.btn-close {
		color: @gray7;
		
		&:hover {color: @teal;}
	}
	
	.header-row {
		.table-row;
		
		.col {
			vertical-align: middle;
			.table-cell;
			}
	}
	
	.title-row {
	  border-radius: 3px 3px 0 0;
		color: @gray1;
		
		.col {height: 40px;}
				
		.col-title {
	    padding-left: 13px;
			
			a {
				color: @gray7;
		    margin-left: -6px;
		    margin-right: 4px;

				&:hover {color: @teal;}
			}

			.title {
				font-weight: 600;				
		    .truncate;

		    @media @sm-lg {font-size: 14px;}
		    @media @lg-x {font-size: 16px;}
			}
		}
		
		.col-close {
	    padding-right: 7px;
			text-align: right;
	    width: 35px;
	    
			.btn-close {
				font-weight: 600;
				
		    @media @sm-lg {font-size: 16px;}
		    @media @lg-x {font-size: 18px;}
								
				&:hover {opacity: 1;}
			}
		}
	}
		
	.subtitle-row {
		color: white;
		
		.col {padding: 7px 0;}
	
		.col-subtitle {
			padding-left: 11px;

	    .subtitle {
		    font-size: 14px;
		    .truncate;
		    
		    .icon {
			    font-size: 18px;
			    top: 1px;
			  }
		    
		    a {
			    color: white;
			    font-weight: 700;
			    
			    &:hover {text-decoration: underline;}
		    }
	    }
		}
		
		.col-option {
			padding-right: 7px;
			text-align: right;
			
			.dropdown-menu {
		    top: 22px;
		    right: 9px !important;
			}
			
			.dropdown-toggle {
				color: white !important;
				font-size: 17px;
				opacity: .5;
		    top: 1px;
				
				&:hover {opacity: 1;}
			}
		}
	}
}