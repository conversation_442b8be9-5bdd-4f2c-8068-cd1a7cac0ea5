/*---------------------------------------------------------------------------
  >Billing
---------------------------------------------------------------------------*/

#checkout {
	display: table;
	width: 100%;
		
	.col-left, .col-right {
    display: table-cell;
    float: none !important;
	}
	
	.col-left {
    border-right: 1px solid @gray2;
    border-radius: 3px 0 0 3px;
    padding: 20px;
		.fl;
		
		@media @sm {width: 60%;}
		@media @md {width: 65%;}
		@media @lg-x {width: 70%;}
	}
	
	.col-right {
    padding: 20px;
		.fr;
		
		@media @sm {width: 40%;}
		@media @md {width: 35%;}
		@media @lg-x {width: 30%;}
	}
	
	.title {
		.font2;
		.f18;
  }
}

#billing_header {
	
	.logo {
		
		img {
			margin: 12px 0;
			height: auto;
		}
	}
	
	.pull-right {
		
		h2 {
	    margin: 0;
	    top: 14px;
			.navy-txt;
			.font2;
			.f23;
			
			a {margin-left: 10px;}
		}
	}
}

.billing-tile {
	width: 100%;
	
	.panel-heading {
    padding: 8px 15px;
		font-size: 16px;
		line-height: 31px;
		.gradient-w-g;
		.font2;
		.f18;
		
		.button-wrapper {
			top: -2px;
			right: -6px;
			.fr;
			
			.btn {margin-left: 10px;}
		}
	}
	
	.panel-body {
	
		.section-title {
			margin: 0 0 7px;
			.navy-txt;
			.font2;
			.f16;
		}
		
		.row {
	    margin-left: -5px;
	    margin-right: -5px;
	    
			[class^=col] {
			    padding-left: 5px;
			    padding-right: 5px;
			}
		}
		
		.summary {.fl;}
	}
}

#logo_checkout {

	.dropdown-menu {
		min-width: 320px;
		text-align: center;
		padding: 20px;
		
		.btn {margin: 0 5px;}
	}
}

#order_summary {
	
	table {
		width: 100%;
		.f12;
		
		hr {
			margin: 10px 0;
			border-color: @gray2;
		}
		
		td:last-child {text-align: right;}
		
		tfoot {
			.w700;
			.f15;
		}
	}
		
	.btn {
		margin: 15px 0;
		width: 100%;
	}
	
	.help-text {
		text-align: center;
    font-size: 10px;
    line-height: 14px;
	}
	
	.alert {
		margin: 15px 0 0;
		text-align: center;
		font-size: 13px;
		line-height: 17px;
	}
}

#payment_method {
	
	#coupon_code {
		width: 230px;
	}	
}

#place_order {
	
	.panel {margin: 0;}
	
	.btn {margin-right: 15px;}
	
	.summary {
		display: table-row-group;
		float: none !important;
	}
}