React = require 'react'
classes = require 'classnames'

TagValue = React.createClass
  displayName: 'TagValue'

  blockEvent: (e)->
    e.stopPropagation()

  handleOnRemove: (e)->
    if !@props.disabled
      @props.onRemove(e)

  render: ()->
    label = @props.option.label
    return (
      <div className={classes('Select-item', @props.option.className)}
         style={@props.option.style}
         title={@props.option.title}>
        <span className="Select-item-icon icon-times-circle"
          onMouseDown={@blockEvent}
          onClick={@handleOnRemove}
          onTouchEnd={@handleOnRemove}></span>
        <span className="Select-item-label">{label}</span>
      </div>
    )

module.exports = TagValue