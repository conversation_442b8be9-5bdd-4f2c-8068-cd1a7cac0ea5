React = require 'react'
<PERSON> = require 'marty'
Modal = require('react-bootstrap').Modal

DeleteModal = React.createClass
  displayName: 'Delete Tag Modal'
  mixins: [Marty.createAppMixin()]

  propTypes:
    type: React.PropTypes.string
    object: React.PropTypes.object
    onHide: React.PropTypes.func

  onCancel: ()->
    @props.onHide()

  onConfirm: ()->
    # TODO Determine why this requires context, appears the mixin is not working. Throws owner vs parent context warning
    @.context.app.tagActionCreators.deleteTag(@props.object)
    @props.onHide()

  render: ()->
    <Modal onHide={@props.onHide} show={@props.show} {...@props}>
      <div className="text-center">
        <div className="modal-body">
          <div className="confirm-dialogue">
            <h2>You are about to delete {@props.type} <strong className="teal">"{@props.object.name}"</strong></h2>
            <p><strong>Are you sure?</strong></p>
            <ul className="share-buttons">          
              <li>
                <a onClick={@onCancel} className="btn btn-teal btn-large share-2">No</a>
              </li>
              <li>
                <a onClick={@onConfirm} className="btn btn-navy btn-large">Yes</a>
              </li>
            </ul>
          </div>    
        </div>
      </div>
    </Modal>

module.exports = DeleteModal