React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'
$ = require 'jquery'

FormData = require 'react-form-data'

Lifecycle = require('react-router').Lifecycle

Drawer = require '../Sidebar/Drawer'
AppHeader = require '../Header/AppHeader'
EntryHeader = require './EntryHeader'

# TODO npm package for global namespace access
AuthenticatedComponent = require '../AuthenticatedComponent'

Redactor = require '../Redactor/Redactor'

MediaManagerModal = require '../MediaManager/Modals/MediaManagerModal'
ImportModal = require '../Import/ImportModal'
TagSelect = require './TagSelect'

Entry = React.createClass
  displayName: 'EntryEdit'
  mixins: [FormData, Lifecycle]

  getInitialState: ->
    entryValue: @props.entry?.title
    journal: undefined
    tags: @getInitialTags()
    entryDate: if @props.entry?.entry_date? then moment.utc(@props.entry.entry_date).local() else if @props.location.query.entry_date? then @defaultEntryDate(moment(@props.location.query.entry_date)) else moment() 
    display_time: @defaultDisplayTime()
    hasChanged: false
    success: undefined
    shouldClose: false
    showMediaManager: false
    showImport: false
    redactor_element: undefined
    inserted_entry_images: []
    replaceImagesAndSave: undefined

  componentWillReceiveProps: (nextProps) ->
    if nextProps.success?
      @setState
        success: nextProps.success

    if nextProps.entry?.title?
      @setState
        entryValue: nextProps.entry.title

    if nextProps.journal?.title
      @setState
        journal: nextProps.journal

    if nextProps.entry?.entry_date?
      @setState
        entryDate: moment.utc(nextProps.entry.entry_date).local()

    if nextProps.entry?.display_time?
      @setState
        display_time: nextProps.entry.display_time  

    if @state.replaceImagesAndSave? and nextProps.session_entry_images?
      content = @refs.redactor.getRedactorValue()
      entry_images = @findEntryImages(content, nextProps.session_entry_images)
      content = @replaceEntryImages(content, entry_images)
      
      if @props.entry?.id?
        @updateEntry(@state.replaceImagesAndSave.shouldClose, content, entry_images)
      else
        @createEntry(@state.replaceImagesAndSave.shouldClose, content, entry_images)
      
      @setState
        replaceImagesAndSave: undefined
        hasChanged: false
        shouldClose: @state.replaceImagesAndSave.shouldClose
        last_saved: moment()

  componentWillUpdate: (nextProps, nextState) ->
    if nextState.success and @state.shouldClose
      @props.history.pushState null, @getOriginatingLocation()

  routerWillLeave: (nextLocation)->
    if @state.hasChanged and not @state.isSaved
      return "Your changes will be lost. Are you sure you want to leave?"
  
  defaultEntryDate: (date)->
    now = moment()
    entryDate = moment date.format('YYYY-MM-DD'+'T') + now.format('HH:mm:ss')
    return entryDate

  defaultDisplayTime: ()->
    if @props.entry?.display_time?
      return @props.entry.display_time
    else if @props.location.query.entry_date? 
      now = moment()
      entry_date = @defaultEntryDate(moment(@props.location.query.entry_date))
      if entry_date.isAfter(now) or entry_date.isSame(now,'day')
        return true
      else
        return false
    else
      return true

  openMediaManager: ()->
    @setState
      showMediaManager: true

  closeMediaManager: ()->
    @setState
      showMediaManager: false

  openImport: ()->
    @setState
      showImport: true

  closeImport: ()->
    @setState
      showImport: false

  getInitialTags: ()->
    if @props.location.query.tag_id?
      tags = [@props.location.query.tag_id]
      return tags

  getOriginatingViewId: ()->
    viewId = undefined
    if @props.params.journalId?
      viewId = 'Journal' + @props.params.journalId
    else if @props.params.tagId?
      viewId = 'Tag' + @props.tagId
    else
      viewId = 'Timeline'
    return viewId

  getOriginatingLocation: ()->
    location = "/"
    if @props.params.journalId?
      return '/journal/' + @props.params.journalId
    else if @props.params.tagId?
      return '/tag/' + @props.params.tagId
    else
      return location

  getOptions: ()->
    options = undefined
    if @props.params.journalId?
      options = {journal: @props.params.journalId}
    else if @props.params.tagId?
      options = {tags__id: @props.params.tagId}
    else
      options = {}
    return options   
      
  replaceEntryImages: (content, entry_images)->
    for image in entry_images
      imageId = "entry_image_pending_media_#{image.instagram_media}"
      pendingRegex = '<img id="'+imageId+'" src="(.*?)">'
      url = content.match pendingRegex
        
      if url?
        content = content.replace(url[1], image.image)
        replace_id = "entry_image_pending_media_" + image.instagram_media
        full_entry_image_id = "entry_image_#{image.id}"  
        content = content.replace(replace_id, full_entry_image_id)

    return content

  findEntryImages: (content, session_entry_images)->
    entry_images = []
    if session_entry_images?
      for image in session_entry_images
        imageId = "entry_image_pending_media_#{image.instagram_media}"
        if content.indexOf(imageId) >= 0
          entry_images.push image
    return entry_images

  updateEntry: (shouldClose, content, entry_images)->
    newEntry = 
      id: @props.entry.id
      title: @formData.entryTitle
      content: content
      entry_date: moment(@state.entryDate).utc().format('YYYY-MM-DD'+'T'+'HH:mm:ss')
      display_time: @state.display_time
      tags: @state.tags || @props.entry.tags
    
    @.app.entryActionCreators.updateEntry newEntry, shouldClose, @getOriginatingViewId(), @getOptions(), entry_images
    
  createEntry: (shouldClose, content, entry_images)->
    newEntry = 
      title: @formData.entryTitle
      content: content
      journal: if @state.journal? then @state.journal.id else if @props.journal? then @props.journal.id else @props.defaultJournal.id
      tags: @state.tags
      entry_date: moment(@state.entryDate).utc().format('YYYY-MM-DD'+'T'+ 'HH:mm:ss')
      display_time: @state.display_time
    
    @.app.entryActionCreators.saveEntry newEntry, shouldClose, @getOriginatingViewId(), @getOptions(), entry_images

  handleSubmit: (shouldClose, options)->
    content = @refs.redactor.getRedactorValue()
    
    if @state.inserted_entry_images.length > 0
      entry_image_queue = @state.inserted_entry_images.map (media)=>
        if content.indexOf "entry_image_pending_media_#{media.id}" >= 0
          return media
        else
          return undefined
      
      entry_image_queue = _.compact entry_image_queue
    
      if not _.isEmpty entry_image_queue
        @.app.socialConnectActionCreators.createEntryImages entry_image_queue
        @setState
          replaceImagesAndSave: {shouldClose: shouldClose, options: options}
      else
        if @.props.params.entryId?
          @updateEntry(shouldClose, content)
          @setState
            hasChanged: false
            shouldClose: shouldClose
            last_saved: moment()
        else
          @createEntry(shouldClose, content)
          @setState
            hasChanged: false
            shouldClose: shouldClose
            last_saved: moment()
    else
      content = @refs.redactor.getRedactorValue()
      if @.props.params.entryId?
        @updateEntry(shouldClose, content)
        @setState
          hasChanged: false
          shouldClose: shouldClose
          last_saved: moment()
      else
        @createEntry(shouldClose, content)
        @setState
          hasChanged: false
          shouldClose: shouldClose
          last_saved: moment()

  onCancel: ()->
    @props.history.pushState null, @getOriginatingLocation()

  onUpdateTitle: (e)->
    title = e.target.value
    newState = {}
    newState.entryValue = title
    if title != @state.title
      newState.hasChanged = true
      newState.debug_event = 'Title updated'
    @setState newState

  onUpdateJournal: (journal)->
    newState = {}
    newState.journal = journal
    if journal?.id != @state.journal?.id
      newState.hasChanged = true
      newState.debug_event = 'Journal updated'
    @setState newState

  onUpdateTags: (addTags, createTags)->
    newState = {}
    newState.addTags = addTags
    newState.createTags = createTags
    
    currentTags = @state.tags || @props.entry?.tags
    
    if @props.params.entryId?
      newTags = addTags.map (tag)->
        return tag.resource_uri
    else
      newTags = addTags.map (tag)->
        return tag.id

    if _.difference currentTags, newTags
      newState.tags = newTags
      newState.hasChanged = true
      newState.debug_event = 'Tags updated'
    
    if createTags?
      newState.hasChanged = true
      newState.debug_event = 'Tags updated'

    @setState newState

  onEntryDateUpdate: (newEntryDate, display_time)->
    newState = {}
    newState.entryDate = newEntryDate
    newState.display_time = display_time
    if !@state.entryDate.isSame(newEntryDate) or (@state.display_time != display_time)
      newState.hasChanged = true
      newState.debug_event = 'Entry Date updated'
    @setState newState
      
  onEntryChange: (content)->
    @setState
      hasChanged: true
      debug_event: 'Entry Body updated'
    return

  onInsertMedia: (files)->
    if @state.redactor_element?
      for file in files
        image = '<p><img src="' + file.filelink + '" /></p>'
        # @state.redactor_element.redactor('selection.restore')
        @state.redactor_element.redactor('insert.html', image, false)
      @state.redactor_element.redactor('observe.images')
    @closeMediaManager()

  onInsertInstagramMedia: (media)->
    if @state.redactor_element?
      image = ""
      for file in media
        image = image.concat "<p><img id=\"entry_image_pending_media_#{file.id}\"src=\"#{file.image_url}\" /></p><p>#{file.caption}</p>"
      @state.redactor_element.redactor('insert.html', image, false)
      @state.redactor_element.redactor('observe.images')
      
      @setState
        inserted_entry_images: @state.inserted_entry_images.concat media
        waitForEntryImageBeforeSave: true
    @closeImport()

  onRedactorInit: (redactor)->
    @setState
      redactor_element: redactor

  getTags: ()->
    tags = []
    tags = tags.concat @props.entry?.tags
    # This is not ideal, should get this from the store instead of assuming the resource_uri
    if @props.tagId?
      tags = tags.concat ["/api/v1/tag/#{@props.tagId}/"]
    tags = _.compact tags
    tags = _.uniq tags
    return tags      

  getViewTitle: ()->
    if @state.journal?
      return @state.journal.title 
    else if @props.journal?
      return @props.journal.title 
    else if @props.defaultJournal? 
      return @props.defaultJournal.title 
    else 
      return ""

  renderTags: ()->
    if @props.entry? or @props.mode =='add'
      return <TagSelect entryTags={@getTags()} onUpdateTags={@onUpdateTags} lastSaved={@state.last_saved} {...@props} />
      # return undefined
  
  render: ()->
    if @props.entry?.modified?
      modifiedDate = moment.utc(@props.entry?.modified)
      lastUpdated = modifiedDate.local().format('MMMM Do, YYYY [at] hh:mm:ss a')
    else
      lastUpdated = 'Never'

    view_title = @getViewTitle()
    <div id="editor">
      <Drawer />
      <div id="page">
        <AppHeader firstname={@props.firstname} view_title={view_title} view_title_is_editable={true} onUpdateJournal={@onUpdateJournal} {...@props}/>
        <EntryHeader entryDate={@state.entryDate} onSave={@handleSubmit} onCancel={@onCancel} entryTags={@state.tags || @props.entry?.tags} onUpdateTags={@onUpdateTags} onDone={@onEntryDateUpdate} display_time={@state.display_time} {...@props}/>
        <div id="content">
          {
            if @state.showMediaManager
              <MediaManagerModal show={@state.showMediaManager} onHide={@closeMediaManager} onDone={@onInsertMedia} {...@props} />
          }

          {
            if @state.showImport
              <ImportModal show={@state.showImport} onHide={@closeImport} {...@props} onInsertInstagramMedia={@onInsertInstagramMedia}/>
          }
          <div onChange={@.updateFormData}>
            <div className="container">
              <div className="header__buttons">
                <a className="trigger-modal-import btn btn-gray" onClick={@openImport}>
                  <img className="icon" src="/static/images/icon-import-social.svg" />
                  <span>Import</span> Social <span>Content</span>
                </a>
                <a className="trigger-modal-media btn btn-gray" onClick={@openMediaManager}>
                  <img className="icon" src="/static/images/icon-import-media.svg" />
                  <span>Add</span> Photos</a> 
              </div>
              <div id="editor_column" className="pull-left">
                <div className="form-group">
                  <input ref="entryTitle" name="entryTitle" type="text" className="entry-title form-control full-width" placeholder="Entry Title (Optional)" value={@state.entryValue} onChange={@onUpdateTitle}/>
                </div>
                {
                  if @props.entry?.content? or @props.mode == 'add'
                    <Redactor ref="redactor" content={@props?.entry?.content} onRedactorChange={@onEntryChange} onRedactorInit={@onRedactorInit} onRedactorOpenMedia={@openMediaManager} onRedactorImport={@openImport} {...@props} />
                  else
                    undefined
                }
                {
                  @renderTags()
                }
                <div className="post-stats">
                  <div className="pull-left">Last updated: {lastUpdated}</div>
                </div>
              </div>          
            </div>
          </div>
        </div>
      </div>
    </div>

NewEntryContainer = AuthenticatedComponent(Marty.createContainer Entry,
  listenTo: ['entryStore', 'journalStore', 'socialStore']
  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      tagId = @props.location.query.tag_id
      if @props.params.journalId?
        return {
          username: @.props.app.authStore.getUsername()
          firstname: @.props.app.authStore.getFirstNameOrUsername()
          user: @props.app.authStore.fetchUser()
          journal: @.props.app.journalStore.getJournalById(@props.params.journalId)
          tagId: tagId
          success: @props.app.entryStore.getSuccess()
          mode: 'add'
          session_entry_images: @props.app.socialStore.getSessionEntryImages()
        }
      else 
        return {
          defaultJournal: @.props.app.journalStore.getDefaultJournal()
          username: @.props.app.authStore.getUsername()
          firstname: @.props.app.authStore.getFirstNameOrUsername()
          user: @props.app.authStore.fetchUser()
          tagId: tagId
          success: @props.app.entryStore.getSuccess()
          mode: 'add'
          session_entry_images: @props.app.socialStore.getSessionEntryImages()
        }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Entry {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Entry {...props} />
  failed: (error)->
    return <div>ENTRY ERROR</div>
)

EntryContainer = AuthenticatedComponent(Marty.createContainer Entry,
  listenTo: ['entryStore', 'journalStore', 'socialStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      entryId = @props.params.entryId
      return {
        entry: @.props.app.entryStore.getEntryById(entryId)
        username: @.props.app.authStore.getUsername()
        firstname: @.props.app.authStore.getFirstNameOrUsername()
        user: @props.app.authStore.fetchUser()
        journal: @props.app.journalStore.getJournalByEntryId(entryId)
        success: @props.app.entryStore.getSuccess()
        mode: 'edit'
        session_entry_images: @props.app.socialStore.getSessionEntryImages()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Entry {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Entry {...props}/>
  failed: (error)->
    console.log error
    return <div>ENTRY ERROR</div>
)

module.exports = {
  EntryContainer: EntryContainer
  NewEntryContainer: NewEntryContainer
}
