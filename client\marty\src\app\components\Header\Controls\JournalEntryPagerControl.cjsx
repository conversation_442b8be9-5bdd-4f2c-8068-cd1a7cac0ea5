React = require 'react'
_ = require 'underscore'
Marty = require 'marty'

Link = require('react-router').Link

JournalEntryPagerControl = React.createClass
  displayName: 'JournalEntryPagerControl'
  mixins: [Marty.createAppMixin()]

  getPrevious: ()->
    if @props.previous_entry?
      return (
        <Link to={"/entry/" + @props.previous_entry?.id} className="left btn btn-gray"><i className="icon icon-angle-left"></i></Link>
      )
    else
      return (
        <a className="disabled left btn btn-gray"><i className="icon icon-angle-left"></i></a>
      )
  
  getNext: ()->
    if @props.next_entry?
      return (
        <Link to={"/entry/" + @props.next_entry?.id} className="right btn btn-gray"><i className="icon icon-angle-right"></i></Link>
      )
    else
      return (
        <a className="disabled right btn btn-gray"><i className="icon icon-angle-right"></i></a>
      )
        
  render: ()->
    return (
        <div id="pager" className='pager-small fr'>  
          {
            @getPrevious()
          }
          {
            @getNext()
          }
        </div>
      
    )

JournalEntryPageControlContainer = Marty.createContainer JournalEntryPagerControl,
  listenTo: ['entryStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      if @props.entry?
        if @props.entry.previous_entry? and @props.entry.previous_entry != null and @props.entry.next_entry? and @props.entry.next_entry != null
          return {
            previous_entry: @props.app.entryStore.getEntryByResourceURI(@props.entry.previous_entry)
            next_entry: @props.app.entryStore.getEntryByResourceURI(@props.entry.next_entry)
          }
        else if @props.entry.previous_entry? and @props.entry.previous_entry != null
          return {
            previous_entry: @props.app.entryStore.getEntryByResourceURI(@props.entry.previous_entry)
          }
        else if @props.entry.next_entry? and @props.entry.next_entry != null
          return {
            next_entry: @props.app.entryStore.getEntryByResourceURI(@props.entry.next_entry) 
          }
      return {}
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <JournalEntryPagerControl {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <JournalEntryPagerControl {...props} />
    # return <div>Pending</div>
  failed: (error)->
    console.log error
    return <div>JournalEntryPagerControl Error</div>

module.exports = JournalEntryPageControlContainer