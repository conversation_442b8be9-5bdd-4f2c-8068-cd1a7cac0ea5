#!/usr/bin/env python
"""
Comprehensive card validation debugger
This tool helps identify exactly why a specific AMEX card is failing validation
"""
import re
from datetime import datetime

# Copy the exact validation logic from the codebase
CARD_TYPES = {
    'visa': r'4\d{12}(\d{3})?$',
    'amex': r'3[47]\d{13}$',
    'mc': r'5[1-5]\d{14}$',
    'discover': r'6011\d{12}',
    'diners': r'(30[0-5]\d{11}|(36|38)\d{12})$'
}

CREDIT_CARD_RE = r'^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|6(?:011|5[0-9][0-9])[0-9]{12}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\\d{3})\d{11})$'

def debug_card_validation(card_number, exp_year, exp_month, cvv):
    """
    Debug the complete card validation process step by step
    """
    print("=" * 60)
    print("COMPLETE CARD VALIDATION DEBUG")
    print("=" * 60)
    
    # Step 1: Clean the card number (like line 102 in util.py)
    original_card = card_number
    cleaned_card = re.sub(r'\D', '', str(card_number))
    
    print(f"Original input: {original_card}")
    print(f"Cleaned card:   {cleaned_card}")
    print(f"Length:         {len(cleaned_card)}")
    print()
    
    # Step 2: Check if it contains only digits
    try:
        num = [int(n) for n in cleaned_card]
        print("✓ Card contains only digits")
    except ValueError:
        print("✗ FAIL: Card contains non-numeric characters")
        return False
    
    # Step 3: Luhn algorithm check (line 118 in util.py)
    print("\nLuhn Algorithm Check:")
    print("-" * 30)
    
    odd_positions = num[::-2]
    even_positions = num[-2::-2]
    doubled_evens = [sum(divmod(d * 2, 10)) for d in even_positions]
    total_sum = sum(odd_positions + doubled_evens)
    luhn_valid = total_sum % 10 == 0
    
    print(f"Digits: {num}")
    print(f"Odd positions (from right):  {odd_positions}")
    print(f"Even positions (from right): {even_positions}")
    print(f"Doubled evens: {doubled_evens}")
    print(f"Total sum: {total_sum}")
    print(f"Luhn valid (sum % 10 == 0): {luhn_valid}")
    
    if not luhn_valid:
        print("✗ FAIL: Card fails Luhn algorithm check")
        print("This means the card number has an incorrect check digit")
        return False
    else:
        print("✓ PASS: Card passes Luhn algorithm check")
    
    # Step 4: Expiration date check (line 120 in util.py)
    print("\nExpiration Date Check:")
    print("-" * 30)
    
    try:
        exp_year_int = int(exp_year)
        exp_month_int = int(exp_month)
        
        # Calculate expiration date (same logic as util.py)
        import calendar
        last_day = calendar.monthrange(exp_year_int, exp_month_int)[1]
        expiration = datetime(exp_year_int, exp_month_int, last_day, 23, 59, 59)
        now = datetime.now()
        
        print(f"Expiration: {expiration}")
        print(f"Now:        {now}")
        print(f"Expired:    {now > expiration}")
        
        if now > expiration:
            print("✗ FAIL: Card is expired")
            return False
        else:
            print("✓ PASS: Card is not expired")
            
    except (ValueError, calendar.IllegalMonthError) as e:
        print(f"✗ FAIL: Invalid expiration date - {e}")
        return False
    
    # Step 5: CVV format check (line 122 in util.py)
    print("\nCVV Format Check:")
    print("-" * 30)
    
    cvv_str = str(cvv)
    cvv_valid = bool(re.match(r'^\d{3,4}$', cvv_str))
    
    print(f"CVV: {cvv_str}")
    print(f"Length: {len(cvv_str)}")
    print(f"Valid format (3-4 digits): {cvv_valid}")
    
    if not cvv_valid:
        print("✗ FAIL: CVV is not 3-4 digits")
        return False
    else:
        print("✓ PASS: CVV format is valid")
    
    # Step 6: Card type detection (line 124-125 in util.py)
    print("\nCard Type Detection:")
    print("-" * 30)
    
    detected_type = None
    for card_type, pattern in CARD_TYPES.items():
        if re.match(pattern, cleaned_card):
            detected_type = card_type
            print(f"✓ Matches {card_type} pattern: {pattern}")
            break
    
    if not detected_type:
        print("✗ FAIL: Card doesn't match any known card type patterns")
        print("Patterns tested:")
        for card_type, pattern in CARD_TYPES.items():
            matches = bool(re.match(pattern, cleaned_card))
            print(f"  {card_type}: {pattern} -> {matches}")
        return False
    else:
        print(f"✓ PASS: Detected as {detected_type}")
    
    # Step 7: Frontend regex check (fields.py)
    print("\nFrontend Regex Check:")
    print("-" * 30)
    
    frontend_valid = bool(re.match(CREDIT_CARD_RE, cleaned_card))
    print(f"Frontend pattern: {CREDIT_CARD_RE}")
    print(f"Matches frontend pattern: {frontend_valid}")
    
    if not frontend_valid:
        print("✗ FAIL: Card doesn't match frontend validation pattern")
        return False
    else:
        print("✓ PASS: Card matches frontend validation pattern")
    
    print("\n" + "=" * 60)
    print("VALIDATION SUMMARY")
    print("=" * 60)
    print("✅ ALL VALIDATIONS PASSED!")
    print("The card should be accepted by the system.")
    print("If it's still failing, the issue might be:")
    print("1. Stripe API rejection")
    print("2. Network/connectivity issues")
    print("3. Other server-side validation")
    
    return True

def main():
    print("CARD VALIDATION DEBUGGER")
    print("This tool helps identify why specific cards fail validation")
    print()
    
    # Example with a known failing case
    print("Example 1: Card that fails Luhn check")
    debug_card_validation("***************", 2025, 12, "1234")
    
    print("\n" + "=" * 80)
    print()
    
    print("Example 2: Valid AMEX test card")
    debug_card_validation("***************", 2025, 12, "1234")
    
    print("\n" + "=" * 80)
    print("INSTRUCTIONS FOR DEBUGGING USER'S CARD")
    print("=" * 80)
    print("1. Replace the card number in the function call above")
    print("2. Use the user's actual expiration date and CVV")
    print("3. Run this script to see exactly where validation fails")
    print("4. The most common failure is the Luhn algorithm check")
    print()
    print("COMMON SOLUTIONS:")
    print("- Ask user to double-check the card number")
    print("- Verify each digit was entered correctly")
    print("- Try typing the number again slowly")
    print("- If Luhn fails, the card number itself is invalid")

if __name__ == '__main__':
    main()
