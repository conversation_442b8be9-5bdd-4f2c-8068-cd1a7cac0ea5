.table {
  overflow: hidden;
	width: 100%;
  .radius3;

	thead {

		th {
			text-align: left;
		  padding: 6px 12px !important;
			background: @gray10;
			color: @gray5;
		  .border-btm;
		  .uppercase;
		  .f12;

			a {
				color: @gray5;
			  .f12;
			  .hover-teal-txt;
			  
				.icon {
					display: none;
			    top: -1px;					
					.f10;
				}
				
			  &:hover .icon {
					display: inline-block;
				}
			}
		}	
	}
	
	tbody {
	
		tr:last-child td {border-bottom: none;}
		
		td {
		  .white-bg;
		  padding: 12px 12px !important;
		  vertical-align: top;
		  .border-btm;

			&:hover .options a {
			  display: inline-block;
			}
		}
		
		.col-check input[type="checkbox"] {
		  position: relative;
		  top: -1px;
		}
		
		.title a {
		  .f15;
		  display: block;
		  margin-bottom: 3px; 
		  .font2;
		  .navy-txt;
		  .hover-teal-txt;
	  }
		
		.options {
			.fl;
		  height: 20px;
		  width: 100%;
		  
		  a {
			  .f12;
			  margin-right: 10px;
			  display: inline-block;
				color: gray;
				display: none;
				.hover-teal-txt;
			}
		}
		
		.col-date span, .col-modified span {.f12;}
	}
}