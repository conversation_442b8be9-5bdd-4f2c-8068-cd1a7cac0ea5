React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

Modal = require('react-bootstrap').Modal
PublicProfileItem = require '../../PublicProfile/PublicProfileItem'

ManageSharingModal = React.createClass
  displayName: 'ManageSharingModal'

  onDelete: (invitationId)->
    @props.app.invitationActionCreators.deleteInvitation(invitationId)
  
  renderFooter: ()->
    # if @props.entry?.isOwnedByMe
    #   <div className="modal-footer">
    #     {
    #       if @props.type isnt 'one_on_one'
    #         <div className="checkbox pull-left">
    #           <label>
    #             <input type="checkbox" checked={true}/> Allow commenting
    #           </label>
    #         </div>        
    #     }
    #     {
    #       if @props.type isnt 'one_on_one'
    #         <a className="btn btn-navy btn-medium">Save</a>
    #     }
    #   </div>
    <div className="modal-footer">
    </div>

  renderStatus: ()->
    if @props.error
      <div>{@props.error.user_message}</div>

  render: ()->
    type = @props.type
    
    if @props.entry?.isOwnedByMe
      title = "Manage Sharing"
      switch @props.type
        when 'one_on_one'
          type = '1on1'
          title = "Manage 1 on 1 Sharing"
        when 'group'
          type = @props.type
          title = 'Manage Group Sharing'
    else
      title = "Shared with"

    <Modal className="manage-sharing-modal" backdrop={'static'} {...@props}>
      <div className={"modal-header color-#{type}-bg"}>
        <div className="row">
          <div className="col-sm-3"></div>
          <div className="col-sm-6 text-center">
            <div className="modal-title">
              {title}
            </div>
          </div>
          <div className="col-sm-3">
            <a className="btn-flat pull-right" onClick={@props.onHide}>Cancel</a>
          </div>
        </div>
      </div>            
      <div className="share-modal-body">
        <div id="list" className="list-avatar"> 
          {
            @renderStatus()
          }
          {
            if @props.owner?
              <PublicProfileItem key={@props.owner} isOwner={true} user={@props.owner} app={@props.app} onDelete={undefined} theme={'owner'}/>                
          }
          {
            if @props.users?
              if @props.entry?.isOwnedByMe
                onDelete = @onDelete
              else
                onDelete = undefined
              @props.users.map (user)=>
                <PublicProfileItem key={user.id} isUser={true} user={user.id} invitation={user.invitation} app={@props.app} onDelete={onDelete}/>  
          }
          {
            if @props.nonUsers?
              if @props.entry?.isOwnedByMe
                onDelete = @onDelete
              else
                onDelete = undefined
              @props.nonUsers.map (nonUser)=>
                <PublicProfileItem key={nonUser.id} isUser={false} user={nonUser.id} invitation={nonUser.invitation} app={@props.app} onDelete={onDelete}/>       
          }     
        </div>
      </div>
      
      {
        @renderFooter()
      }
    </Modal>

ManageSharingModalContainer = Marty.createContainer ManageSharingModal,
  listenTo: ['invitationStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        error: @props.app.invitationStore.getError()
        failed_invitations: @props.app.invitationStore.getFailedInvitations()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    users = []
    nonUsers = []
    invitationUsers = []
    invitationNonUsers = []
    entrySharedGroupUsers = []
    entrySharedNonGroupUsers = []

    if @props.entry?.isOwnedByMe
      owner = @props.app.authStore.getMyPublicUser()
    else if @props.entry?
      owner = @props.entry?.user
    else
      owner = undefined

    if @props.invitations?
      invitationUsers = @props.invitations.map (invitation)=>
        if invitation.sharing_user? 
          return {
            id: invitation.sharing_user
            invitation: invitation.id
          }
        else if invitation.shared_user?
          return {
            id: invitation.shared_user
            invitation: invitation.id
          }
      invitationUsers = _.compact invitationUsers

      invitationNonUsers = @props.invitations.map (invitation)=>
        if not invitation.sharing_user? and not invitation.shared_user?
          if invitation.shared_non_user_email?
            return {
              id: invitation.shared_non_user_email
              invitation: invitation.id
            }
      invitationNonUsers = _.compact invitationNonUsers
      
      if @props.type isnt 'one_on_one'
        if @props.entry?.shared_group_users
          entrySharedGroupUsers = @props.entry.shared_group_users.map (user)=>
            if user not in _.pluck invitationUsers, 'id'
              return {
                id: user
              }
          entrySharedGroupUsers = _.compact entrySharedGroupUsers

          entrySharedNonGroupUsers = @props.entry.shared_group_non_users.map (user)=>
            if user not in _.pluck invitationNonUsers, 'id'
              return {
                id: user
              }
          entrySharedNonGroupUsers = _.compact entrySharedNonGroupUsers
          
      
      users = _.uniq invitationUsers.concat entrySharedGroupUsers
      users = _.reject users, (user)=>
        user.id is owner
      nonUsers = _.uniq invitationNonUsers.concat entrySharedNonGroupUsers
      
      props['users'] = users
      props['nonUsers'] = nonUsers
      props['owner'] = owner

    return <ManageSharingModal {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <ManageSharingModal {...props} />
  failed: (error)->
    console.log error
    return <div>ManageSharing Error</div>

module.exports = ManageSharingModalContainer