React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Link = require('react-router').Link
moment = require 'moment'

RouteContext = require('react-router').RouteContext

AppHeader = require '../Header/AppHeader'
BookHeader = require './BookHeader'
Drawer = require '../Sidebar/Drawer'

BookContent = require './BookContent'
BookCover = require './BookCover'
BookPreview = require './BookPreview'
BookOrder = require './BookOrder'

BookStatusIndicator = require './BookStatusIndicator'

Book = React.createClass
  displayName: 'Book'
  mixins: [RouteContext]
  SUCCESS_BOOK_BUILDER_STATUS: [100, 99, 98]
  INCLUDE_FIELDS: ['id','title','shipping_address', 'all_about_me', 'approved', 'back_cover', 'book_builder_status', 'color', 'custom_introduction', 'end_date', 'end_datetime', 'export_format', 'journal', 'photos', 'quantity', 'subtitle', 'spine', 'timezone_offset', 'start_date', 'shipping_cost']
  
  getInitialState: ->
    book: @props.book || @bookDefaults(@props.journal)
    journal: @props.journal
    success: @props.success
    shouldPoll: false
    pollRefreshRate: 2000
    pollAttempts: 0
    book_builder_status: @props.book?.book_builder_status
    timer: undefined
    doNotPrintEntries: undefined
    saved: true

  componentDidMount: ->
    window.scrollTo(0,0)
    if @props.book?.book_builder_status not in @SUCCESS_BOOK_BUILDER_STATUS and @props.book?.book_builder_status?
      @setState
        shouldPoll: true
  
  componentWillUpdate: (nextProps, nextState) ->
    if (nextProps.success?.modified != @props.success?.modified) or (nextProps.book_order_success?.modified != @props.book_order_success?.modified)
      if @state.transitionTarget?
      
        if @state.transitionTarget != '/bookbuilder/' and @state.transitionTarget != '/orders/'
          transitionTarget = @state.transitionTarget + nextProps.success.id
        else
          transitionTarget = @state.transitionTarget

        @props.app.bookActionCreators.resetBookSuccess()
        if @state.transitionTarget == '/orders/'
          @props.app.bookOrderActionCreators.resetBookOrderSuccess()
          @.app.bookOrderQueries.getBookOrders('Orders', {order_by: '-created'})
        
        @props.history.pushState null, transitionTarget
        
      
      else if @props.location.pathname == '/book/new/'
        @props.history.pushState null, '/book/content/' + nextProps.success.id
        
  componentWillReceiveProps: (nextProps) ->
    book = undefined
    journal = undefined
    
    if nextProps.book?
      if nextProps.book?.id != @state.book?.id
        @setState
          book: nextProps.book
          book_builder_status: nextProps.book.book_builder_status
      
      if nextProps.book.book_builder_status not in @SUCCESS_BOOK_BUILDER_STATUS
        @setState
          shouldPoll: true
      else        
        @setState
          shouldPoll: false

    if nextProps.journal?.id != @state.journal?.id and @state.book? and not @state.doNotPrintEntries
      if @props.params.bookId?
        @setState
          journal: nextProps.journal
      else
        book = @state.book
        if not @state.doNotPrintEntries
          book.journal = nextProps.journal.id
          book.title = nextProps.journal.title
          journal = nextProps.journal
        book = _.defaults book, @bookDefaults(journal)
        @setState
          book: book

  componentDidUpdate: (prevProps, prevState) ->
    if @props.params.bookId?
      if prevState.shouldPoll != @state.shouldPoll
        @poll()  

    if @state.saveOnNextUpdate
      @onSave()

    if @props.book? and @state.book?
      if @props.book?.modified != @state.book?.modified
        @shouldUpdateBook(@props.book)

  componentWillUnmount: ->
    @app.bookActionCreators.removeAllTimers()
    clearTimeout(@state.timer)
  
  poll: ()->
    if @state.timer?
      clearTimeout(@state.timer)
      @setState
        timer: undefined
    else 
      refresh_rate = 2000
      attempts = 0
      timer = undefined
      
      do refresh = =>
        if attempts >= 15
          if refresh_rate <= 60000
            refresh_rate = parseInt(refresh_rate * 1.25)
          else
            refresh_rate = 60000
        @getUpdatedBook()
        timer = setTimeout(refresh, refresh_rate)
        attempts++
        
        if @isMounted()
          @.app.bookActionCreators.addTimer(timer)
          @setState
            timer: timer
            pollAttempts: attempts
            pollRefreshRate: refresh_rate
      
  shouldUpdateBook: (nextBook)->
    # Only update the fields the book builder can change, else state could change out from underneath the user
    
    bookBuilderFields = ['created','modified','book_builder_status','page_count','preview_pdf','downloadable_pdf', 'shipping_cost', 'total_amount']
    oldBook = _.omit @state.book, bookBuilderFields
    newBook = _.pick nextBook, bookBuilderFields
    book = _.extend {}, oldBook, newBook
    @setState
      book: book
      book_builder_status: nextBook.book_builder_status

  updateBookBuilderStatus: (book)->
    pickFields = ['all_about_me', 'title', 'back_cover', 'color', 'custom_introduction', 'end_date', 'export_format', 'journal', 'photos', 'spine', 'start_date', 'subtitle']
    oldBook = _.pick @props.book, pickFields
    newBook = _.pick book, pickFields

    if _.isMatch oldBook, newBook
      return _.extend {}, book, {freeze_book_builder_status: true}
    else
      # This is ignored, server always sets to 0 to retrigger full regeneration
      book['book_builder_status'] = 26
      book['approved'] = false
      return book

  getUpdatedBook: ()->
    if @props.book?
      options =
        modified__gt: @props.book.modified
        id: @props.book.id
        show_all_fields: 1
      
      @props.app.bookActionCreators.pollBook(options)

  bookDefaults: (journal)->
    bookDefaults = 
      export_format: "6X9"
      all_about_me: "MR"
      color: true
      photos: true  
      book_builder_status: 26
      title: 'My JRNL Book'

    if journal?
      if not @state?.book?.journal?
        bookDefaults['journal'] = journal.id

    return bookDefaults

  onDoNotPrintEntries: (e, value)->  
    @setState
      doNotPrintEntries: value
    if e?
      @updateBook(e)

  onSave: (shouldTransition, transitionTarget)->
    book = @state.book
    if @props.params.bookId?
      book = @updateBookBuilderStatus(book)
      book = _.defaults {}, book, @props.book
      book['timezone_offset'] =  moment().format('ZZ')
      freeze_book_builder_status = book.freeze_book_builder_status
      book = _.pick book, @INCLUDE_FIELDS
      @props.app.bookActionCreators.updateBook(book, freeze_book_builder_status)
    else
      book['timezone_offset'] =  moment().format('ZZ')
      @props.app.bookActionCreators.createBook(book)
    
    if shouldTransition  
      @setState
        saved: true
        transitionTarget: transitionTarget
    else
      @setState
        saved: true
        saveOnNextUpdate: false

  onPlaceOrder: (shouldTransition, transitionTarget, payment_profile)->
    if payment_profile?
      bookOrder = 
        book: @state.book.id
        payment_profile: payment_profile.id

      if @props.params.bookId?
        @props.app.bookOrderActionCreators.createBookOrder(bookOrder)

      if shouldTransition
        @setState
          transitionTarget: transitionTarget


  updateBook: (e)->
    field = e.target.name
    value = e.target.value
    
    switch field
      when "color"
        value = if e.target.value == "true" then true else false
      when "photos"
        value = if e.target.value == "true" then true else false
      when "approved"
        value = e.target.checked
      when "quantity"
        value = parseInt(value)

    book = _.clone @state.book
    book[field] = value
    
    saveOnNextUpdate = false
    if field == 'shipping_address'
      saveOnNextUpdate = true
    if field == 'quantity'
      saveOnNextUpdate = true
    
    @setState
      book: book
      saveOnNextUpdate: saveOnNextUpdate
      saved: false


  updateJournal: (journal)->
    if @state.book?
      book = @state.book
      if @props.params.bookId?
        book.journal = journal.resource_uri
        book.start_date = undefined
        book.end_date = undefined
      else
        book.journal = journal.id
        book.title = journal.title
        book.start_date = undefined
        book.end_date = undefined
    
    @setState
      book: book
      journal: journal
      saved: false

  updateStartDate: (start_date)->  
    book = @state.book
    book.start_date = start_date.local().format('YYYY-MM-DD')
    book.start_datetime = start_date.startOf('day').utc().format('YYYY-MM-DD'+'T'+'HH:mm:ss')
    
    @setState
      book: book
      saved: false

  updateEndDate: (end_date)->  
    book = @state.book
    book.end_date = end_date.local().format('YYYY-MM-DD')
    book.end_datetime = end_date.endOf('day').utc().format('YYYY-MM-DD'+'T'+'HH:mm:ss')
    
    @setState
      book: book
      saved: false

  resetError: ()->
    if @props.error?
      @props.app.bookActionCreators.resetError()
  
  renderError: ()->
    if @props.error?
      return <div className="alert danger">There was an error saving your book!</div>

  getIcon: (current)->
    angle = "icon icon-angle-right pull-right"
    checkmark = "icon icon-check pull-right"

    checkedPaths = []
    
    switch @props.step
      when 'content'
        checkedPaths = []
      when 'cover'
        checkedPaths = ['content']
      when 'preview'
        checkedPaths = ['content', 'cover']
      when 'order'
        checkedPaths = ['content', 'cover', 'preview']

    if current in checkedPaths
      return checkmark
    else
      return angle

  renderTabs: ()->    
    # THIS IS DUMB
    inactiveClassname = "col-sm-3 border-right"
    activeClassname = "col-sm-3 border-right active"
    disabledClassname = "col-sm-3 border-right disabled"
    bookId = @props.params.bookId || ""
  
    className = ""
    if @props.mode == 'add'
      className = activeClassname
    else
      className = inactiveClassname
    
    content_tab = (
      <Link key='content-tab' to={'/book/content/' + bookId} activeClass={activeClassname} className={className}>
        <div className={@getIcon('content')}></div>
        <div className="title">Content</div>
      </Link>
    )

    className = ""
    if @props.params.bookId?
      className = inactiveClassname
    else
      className = disabledClassname
    
    
    cover_tab = (
      <Link key='cover-tab' to={'/book/cover/'+ bookId} className={className} activeClass={activeClassname}>
        <div className={@getIcon('cover')}></div>
        <div className="title">Cover</div>
      </Link>
    )
    
    className = ""
    if @props.params.bookId?
      if @state.book_builder_status in @SUCCESS_BOOK_BUILDER_STATUS and @state.book.preview_pdf != null and @state.book.page_count != null
        className = inactiveClassname
      else
        className = disabledClassname
    else
      className = disabledClassname
    

    preview_tab = (
      <Link key='preview-tab' to={'/book/preview/'+ bookId} className={className} activeClass={activeClassname}>
        <div className={@getIcon('preview')}></div>
        <div className="title">Preview</div>
      </Link>
    )

    className = ""
    if @props.params.bookId?
      if @state.book_builder_status in @SUCCESS_BOOK_BUILDER_STATUS and @state.book.shipping_address != null and @state.book.approved and @state.saved
        className = inactiveClassname
      else
        className = disabledClassname
    else
      className = disabledClassname


    order_tab = (
      <Link key='order-tab' to={'/book/order/'+ bookId} className={className} activeClass={activeClassname}>
        <div className={@getIcon('order')}></div>
        <div className="title">Place Order</div>
      </Link>
    )

    output = [content_tab, cover_tab, preview_tab, order_tab]

    return (
      <div id="steps" className="border-btm">
        { output }
      </div>
    )

  renderStepContent: ()->
    # cloneElement is used to mutate props of the children. Replacing props.book for the children with the state.book from this parent Book so that parent can maintain store book in props and editted book in state.
    if @state.book?
      switch @props.step
        when "content"    
          return React.cloneElement <BookContent />, _.defaults {book: @state.book, journal: @state.journal, onSave: @onSave, updateBook: @updateBook, updateJournal: @updateJournal, updateStartDate: @updateStartDate, updateEndDate: @updateEndDate, onDoNotPrintEntries: @onDoNotPrintEntries, saved: @state.saved}, @props
        when "cover"
          return React.cloneElement <BookCover />, _.defaults({book: @state.book, journal: @state.journal, onSave: @onSave, updateBook: @updateBook, saved: @state.saved}, @props)
          # return <BookCover book={@state.book} journal={@state.journal} params={@props.params} app={@props.app} onSave={@onSave} updateBook={@updateBook} />
        when "preview"
          if @state.book.book_builder_status in @SUCCESS_BOOK_BUILDER_STATUS
            return React.cloneElement <BookPreview />, _.defaults {book: @state.book, journal: @state.journal, onSave: @onSave, updateBook: @updateBook, saved: @state.saved}, @props
          else
            return undefined
        when "order"
          if @state.book.book_builder_status in @SUCCESS_BOOK_BUILDER_STATUS
            return React.cloneElement <BookOrder />, _.defaults {book: @state.book, journal: @state.journal, onSave: @onSave, onPlaceOrder: @onPlaceOrder, updateBook: @updateBook, saved: @state.saved}, @props
          else
            return undefined


  renderProgress: ()->
    if @state.book_builder_status not in @SUCCESS_BOOK_BUILDER_STATUS and @props.params.bookId? and @props.book?
      return <BookStatusIndicator {...@props} />

  render: ()->
    <div>
      <Drawer />
      <div id="page">
        <AppHeader firstname={@props.user?.first_name} view_title={ if @props.book? then @props.book.title || 'No Title' else 'New Book'} {...@props}/>
        <BookHeader onSave={@onSave} cancelLocation={'/bookbuilder/'}/>
        <div id="content" onClick={@resetError} onChange={@resetError}>
          <div className="container">
            <div className="pull-left white-bg full-width frame">
                {
                  @renderTabs()
                }
              <div id="build-column" className="pull-left full-width">
                {
                  @renderProgress()
                }
                {
                  @renderError()
                }
                {
                  @renderStepContent()
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

NewBookContainer = Marty.createContainer Book,
  listenTo: ['bookStore', 'journalStore']

  fetch:()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        journal: @props.app.journalStore.getDefaultJournal()
        mode: 'add'
        step: 'content'
        error: @props.app.bookStore.getError()
        success: @props.app.bookStore.getBookSuccess()
        user: @props.app.authStore.fetchUser()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Book {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Book {...props} />
  failed: (error)->
    console.log error
    return <div>Book Content Error</div>

EditBookContentContainer = Marty.createContainer Book,
  listenTo: ['bookStore', 'journalStore']

  fetch:()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        book: if @props.params.bookId? then @props.app.bookStore.getBook(@props.params.bookId) else undefined
        journal: if @props.params.bookId? then @props.app.journalStore.getJournalByBookId(@props.params.bookId) else @props.app.journalStore.getDefaultJournal()
        step: 'content'
        error: @props.app.bookStore.getError()
        success: @props.app.bookStore.getBookSuccess()
        user: @props.app.authStore.fetchUser()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Book {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Book {...props} />
  failed: (error)->
    console.log error
    return <div>Book Content Error</div>

EditBookCoverContainer = Marty.createContainer Book,
  listenTo: ['bookStore']

  fetch:()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        book: if @props.params.bookId? then @props.app.bookStore.getBook(@props.params.bookId) else undefined
        step: 'cover'
        error: @props.app.bookStore.getError()
        success: @props.app.bookStore.getBookSuccess()
        user: @props.app.authStore.fetchUser()
      }
    else
      return {}

  done: (results)->
    props = _.extend {}, @props, results
    return <Book {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Book {...props} />
  failed: (error)->
    console.log error
    return <div>Book Content Error</div>

PreviewBookContainer = Marty.createContainer Book,
  listenTo: ['bookStore']

  fetch:()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        book: if @props.params.bookId? then @props.app.bookStore.getBook(@props.params.bookId) else undefined
        step: 'preview'
        error: @props.app.bookStore.getError()
        success: @props.app.bookStore.getBookSuccess()
        user: @props.app.authStore.fetchUser()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Book {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Book {...props} />
  failed: (error)->
    console.log error
    return <div>Book Content Error</div>

OrderBookContainer = Marty.createContainer Book,
  listenTo: ['bookStore','billingStore', 'bookOrderStore']

  fetch:()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        book: if @props.params.bookId? then @props.app.bookStore.getBook(@props.params.bookId) else undefined
        step: 'order'
        error: @props.app.bookStore.getError()
        success: @props.app.bookStore.getBookSuccess()
        book_order_success: @props.app.bookOrderStore.getSuccess()
        user: @props.app.authStore.fetchUser()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Book {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Book {...props} />
  failed: (error)->
    console.log error
    return <div>Book Content Error</div>

module.exports = {
  NewBook: NewBookContainer
  EditBookContent: EditBookContentContainer
  EditBookCover: EditBookCoverContainer
  PreviewBook: PreviewBookContainer
  OrderBook: OrderBookContainer
}

