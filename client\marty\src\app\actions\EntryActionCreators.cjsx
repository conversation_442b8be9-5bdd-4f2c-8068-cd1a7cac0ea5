Marty = require 'marty'
_ = require 'underscore'
entryConstants = require '../constants/EntryConstants'

sanitizeHtml = require '../utils/sanitizeHtml'

sanitizeEntryContent = (entry)->
  if entry?.content?
    entry.content = sanitizeHtml entry.content, true
  return entry

EntryActionCreators = Marty.createActionCreators
  updateEntries: (options, id)->
    @.dispatch entryConstants.UPDATE_ENTRIES, options, id

  updateViewMode: (options, id)->
    @.dispatch entryConstants.UPDATE_VIEW_MODE, options, id

  updateEntryViewMode: (options, id)->
    @.dispatch entryConstants.UPDATE_ENTRY_VIEW_MODE, options, id

  setRefreshFlag: ()->
    @dispatch entryConstants.SET_REFRESH_FLAG

  saveEntry: (newEntry, shouldClose, id, options, entry_images)->
    if newEntry?
      newEntry = sanitizeEntryContent newEntry
    return @.app.entryHttpAPI.saveEntry(newEntry)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @.dispatch entryConstants.ENTRY_SAVED, success, shouldClose, id, options
      if not _.isEmpty entry_images
        @app.socialConnectActionCreators.updateEntryImagesWithEntry success.id, entry_images
    .catch (error)=>
      console.log error
      @.dispatch entryConstants.ENTRY_ERROR, error

  updateEntry: (newEntry, shouldClose, id, options, entry_images)->
    if newEntry?
      newEntry = sanitizeEntryContent newEntry
    return @.app.entryHttpAPI.updateEntry(newEntry)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @.dispatch entryConstants.ENTRY_UPDATED, success, shouldClose, id, options
      if not _.isEmpty entry_images
        @app.socialConnectActionCreators.updateEntryImagesWithEntry success.id, entry_images
    .catch (error)=>
      console.log error
      @.dispatch entryConstants.ENTRY_ERROR, error

  updateJournalForEntry: (entry, newJournalId)->
    return @.app.entryHttpAPI.updateJournalForEntry(entry, newJournalId)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @.dispatch entryConstants.UPDATE_JOURNAL_FOR_ENTRY, success
    .catch (error)=>
      console.log error
      @.dispatch entryConstants.ENTRY_ERROR, error

  deleteEntry: (entry, transitionToTarget, shouldTransition)->
    return @.app.entryHttpAPI.deleteEntry(entry)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (success)=>
      @.dispatch entryConstants.ENTRY_DELETED, entry, transitionToTarget, shouldTransition
    .catch (error)=>
      console.log error
      @.dispatch entryConstants.ENTRY_ERROR, error

  # createEntryFromInstagram: (media)->
  #   newEntry = {}
  #   return @.app.entryHttpAPI.saveEntry(newEntry)
  #   .then (response)=>
  #     if response.ok
  #       return response
  #     else
  #       throw response
  #   .then (response)->
  #     return response.json()
  #   .then (success)=>
  #     @.dispatch entryConstants.CREATE_ENTRY_FROM_INSTAGRAM, success, media
  #   .catch (error)=>
  #     console.log error
  #     @.dispatch entryConstants.ENTRY_ERROR, error

  queueEntryImages: (entry_images)->
    # console.log entry_images
    # @dispatch entryConstants.QUEUE_ENTRY_IMAGE, entry_images

module.exports = EntryActionCreators
  