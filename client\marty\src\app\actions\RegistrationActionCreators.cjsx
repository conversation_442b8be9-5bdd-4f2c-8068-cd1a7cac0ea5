Marty = require 'marty'
registrationConstants = require '../constants/RegistrationConstants'
google_trackConversion = require 'google_trackConversion'
settings = require '../settings'

RegistrationActionCreators = Marty.createActionCreators
  
  signup: (user)->
    @.app.registrationHttpAPI.signup(user)
    .then (response)=>
      if response.ok    
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      if google_trackConversion?
        google_trackConversion settings.GA_REGISTRATION_CONVERSION
        google_trackConversion settings.GA_INACTIVE_REMARKETING
      @.dispatch registrationConstants.REGISTRATION_COMPLETED, success
    .catch (error)=>
      @.dispatch registrationConstants.REGISTRATION_ERROR, error
      return

  reset_signup: ()->
    @.dispatch registrationConstants.RESET_REGISTRATION

  reset_registration_error: ()->
    @.dispatch registrationConstants.RESET_REGISTRATION_ERROR

  set_jrnl_affiliate: (affiliate)->
    @.dispatch registrationConstants.SET_JRNL_AFFILIATE, affiliate

  set_hasoffer_affiliate: (affiliate)->
    @.dispatch registrationConstants.SET_HASOFFER_AFFILIATE, affiliate

  setInvitationId: (invitationId, storeOnCookie)->
    @.dispatch registrationConstants.SET_INVITATION_ID, invitationId, storeOnCookie

  resetInvitationId: ()->
    @.dispatch registrationConstants.RESET_INVITATION_ID


module.exports = RegistrationActionCreators