React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Clipboard = require 'clipboard'

FileDownload = require '../Utility/FileDownload'
settings = require '../../settings/'

JournalEmail = React.createClass
  displayName: 'JournalEmail'

  getInitialState: ->
    showOptions: false
    showDownload: false

  componentDidMount: ->
    @.clipboard = new Clipboard ".copy_#{@props.object.id}", {
      text: (target)=>
        @onCopy(target)
        return @props.object.incoming_email_address
    }

  componentWillUnmount: ->
    @.clipboard.destroy()
  
  onCopy: (target)-> 
    if _.isEmpty target.dataset.address
      window.setTimeout ()=>
        emailElement = document.getElementById("journal_email_#{@props.object.id}")
        if emailElement?
          emailElement.setSelectionRange(0, emailElement.value.length)
      , 50


      @setState
        showOptions: false
    else
      if @props.onHide?
        @props.onHide()
    

  onAddContacts: ()->
    @setState
      showOptions: false
      showDownload: true

  onReset: (journal_id)->
    @props.app.emailToJRNLActionCreators.reset_vcard(journal_id)      
    @setState
      showDownload: false
    
  onGenerateNew: ()->
    if @props.generate?
      @props.generate(@props.object)
    @setState
      showOptions: false

  toggleOptions: ()->
    @setState
      showOptions: !@state.showOptions

  buildFilename: ()->
    punctuation = /[\u2000-\u206F\u2E00-\u2E7F\\'!"#$%&()*+,\-.\/:;<=>?@\[\]^_`{|}~]/
    space = /\s+/g
    title = @props.object.title
    title = title.replace(punctuation,'').replace(space, '_')
    title = title.substring 0,10
    return "jrnl_#{title}.vcf"


  renderButtons: ()->
    copyClass = "btn btn-gray copy_#{@props.object.id}"
    <ul className="confirm-dialogue__buttons">
      <li>
        <a data-address={@props.object.incoming_email_address} className={copyClass}>Copy Email Address</a>
      </li>
      <li>
        <a onClick={@onAddContacts} className="btn btn-gray">Add to Contacts</a>
        {
          if @state.showDownload
            <FileDownload file_id={@props.object.id} file={@props.vcard} onDownloadComplete={@onReset} filename={@buildFilename()}/>
        }
      </li>
    </ul>

  renderPanel: ()->
    copyClass = "copy_#{@props.object.id}"
    <div className="panel panel--etj">
      <div className="panel__title">{@props.object.title}</div>                
        <div className="input-group">
          <input type="text" id={"journal_email_#{@props.object.id}"} className="form-control" value={@props.object.incoming_email_address} onChange={()-> null} />
          <span className="panel__options panel__options--small input-group-btn">
            <button type="button" className="btn btn-gray dropdown-toggle" onClick={@toggleOptions}>
              <i className="icon icon-options"></i>
            </button>
            {
              if @state.showOptions
                  <div className="open">
                    <ul className="dropdown-menu dropdown-menu-right">
                      <li>
                        <a className={copyClass}>Copy Email address</a>
                      </li>
                      <li>
                        <a onClick={@onAddContacts}>Add to Contacts</a>
                      </li>
                      <li>
                        <a className="TEMP-trigger-edit-journal-settings" onClick={@onGenerateNew}>Generate new Email</a>
                      </li>
                    </ul>
                  </div>
            }
            {
              if @state.showDownload
                <FileDownload file_id={@props.object.id} file={@props.vcard} onDownloadComplete={@onReset} filename={@buildFilename()}/>
            }
          </span>
          <span className="panel__options panel__options--large input-group-btn">
            <button className={"btn btn-gray " + copyClass} type="button">
              <i className="fa fa-copy"></i><span>Copy</span>
            </button>
            <button className="btn btn-gray" type="button" onClick={@onAddContacts}>
              <i className="fa fa-arrow-circle-o-down"></i><span>Add to Contacts</span>
            </button>
            <button className="TEMP-trigger-edit-journal-settings btn btn-gray" type="button" onClick={@onGenerateNew}>
              <i className="fa fa-refresh"></i><span>Generate new Email</span>
            </button>
          </span>
        </div>
      </div>
      

  render: ()->
    if @props.as_buttons
      @renderButtons()
    else if @props.as_panel
      @renderPanel()
    else
      copyClass = "copy_#{@props.object.id}"
      <div className="form-group">
        <div className="input-group">
          <input id={"journal_email_#{@props.object.id}"} type="text" className="form-control" value={@props.object.incoming_email_address} onChange={()-> null}/>
          <span className="panel__options panel__options--small input-group-btn">
            <button type="button" className="btn btn-gray dropdown-toggle" onClick={@toggleOptions}>
              <i className="icon icon-options"></i>
            </button>
            {
              if @state.showOptions
                <div className="open">
                  <ul className="dropdown-menu dropdown-menu-right">
                    <li>
                      <a className={copyClass}>Copy Email address</a>
                    </li>
                    <li>
                      <a onClick={@onAddContacts}>Add to Contacts</a>
                      {
                        if @state.showDownload
                          <FileDownload file_id={@props.object.id} file={@props.vcard} onDownloadComplete={@onReset} filename={@buildFilename()}/>
                      }
                    </li>
                    <li>
                      <a className="TEMP-trigger-reset-email" onClick={@onGenerateNew}>Generate new Email</a>
                    </li>
                  </ul>
                </div>
            }
          </span>
          <span className="panel__options panel__options--large input-group-btn">
            <button className={"btn btn-gray " + copyClass} type="button">
              <i className="fa fa-copy"></i>
              <span>Copy</span>
            </button>
            <button className="btn btn-gray" type="button" onClick={@onAddContacts}>
              <i className="fa fa-arrow-circle-o-down"></i>
              <span>Add to Contacts</span>
            </button>
            {
              if @state.showDownload
                <FileDownload file_id={@props.object.id} file={@props.vcard} onDownloadComplete={@onReset} filename={@buildFilename()} />
            }
            <button className="TEMP-trigger-reset-email btn btn-gray" type="button" onClick={@onGenerateNew}>
              <i className="fa fa-refresh"></i>
              <span>Generate new Email</span>
            </button>
          </span>
        </div>
      </div>

JournalEmailContainer = Marty.createContainer JournalEmail,
  listenTo: ['emailToJRNLStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      vcard: @props.app.emailToJRNLStore.getVcard(@props.object.id)
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <JournalEmail {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <JournalEmail {...props} />
    # return <div></div>
  failed: (error)->
    return <div>EmailToJRNL ERROR</div>


module.exports = JournalEmailContainer