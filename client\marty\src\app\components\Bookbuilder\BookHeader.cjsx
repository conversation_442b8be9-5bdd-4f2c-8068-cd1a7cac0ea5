React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
History = require('react-router').History

BookHeader = React.createClass
  displayName: 'Book Header'
  mixins: [History]

  propTypes:
    onSave: React.PropTypes.func
    cancelLocation: React.PropTypes.string

  onCancel: (e)->
    @history.pushState null,'/bookbuilder/'
    return

  onSave: (e)->
    if @props.onSave?
      @props.onSave()
    e.stopPropagation()
    e.preventDefault()
    return

  onSaveAndClose: (e)->
    console.log "Save and Close"
    if @props.onSave?
      @props.onSave(true, '/bookbuilder/')
    e.stopPropagation()
    e.preventDefault()
    return

  render: ()->
    <div id="action-nav" className="teal-bg">
      <div className="container">
        <div className="pull-left">
        </div>
        <div className="pull-right ">
          <ul className="nav navbar-nav">
            <li>
              <a onClick={@onCancel} className="btn-flat">Cancel</a>
            </li>
            <li>
              <a onClick={@onSave} className="btn-flat">Save</a>
            </li>
            <li>
              <a onClick={@onSaveAndClose} className="btn-flat">Save & Close</a>
            </li>
          </ul>
        </div>
      </div>
    </div>

BookHeaderContainer = Marty.createContainer BookHeader,
  listenTo: []

  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <BookHeader {...@props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <BookHeader {...@props} />
  failed: (error)->
    console.log error
    return <div>Book Header</div>


module.exports = BookHeaderContainer