React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

BookShippingCalculator = React.createClass
  displayName: 'DeliveryCalculator'
  
  getInitialState: ->
    book_shipping_address: @props.book_shipping_address || @defaultShippingAddress()
    book_shipping_success: undefined
    error: undefined
  
  componentWillReceiveProps: (nextProps) ->
    if nextProps.book_shipping_address and not @props.book_shipping_address?
      @setState
        book_shipping_address: nextProps.book_shipping_address
    
    if nextProps.user and not @props.user? and not @props.book_shipping_address?
      @userReceived(nextProps.user)

    if nextProps.book_shipping_success and not @props.book_shipping_success?
      @setState 
        book_shipping_success: nextProps.book_shipping_success

    if nextProps.error and not @props.error?
      @setState
        error: nextProps.error
    
  componentDidUpdate: (prevProps, prevState) ->
    if @state.book_shipping_success?.id? and prevState.book_shipping_success is undefined
      if @props.onSave? and @props.updateBook?
        e = 
          target:
            name: 'shipping_address'
            value: @state.book_shipping_success.resource_uri
        @props.updateBook(e)
        @props.app.bookActionCreators.resetShippingAddressSuccess()

  userReceived: (user)->
    shippingAddress = @state.book_shipping_address
    if not shippingAddress?.first_name? and not shippingAddress?.last_name?
      shippingAddress['first_name'] = user.first_name
      shippingAddress['last_name'] = user.last_name
    @setState
      book_shipping_address: shippingAddress
  
  defaultShippingAddress: ()->
    shippingAddress=
      first_name: @props.user?.first_name || undefined
      last_name: @props.user?.last_name || undefined
      city: undefined
      state: undefined
      street: undefined
      street2: undefined
      zip_code: undefined
    return shippingAddress

  onCalculate: ()->
    if @state.book_shipping_address?.id?
      @props.app.bookActionCreators.updateShippingAddress(@state.book_shipping_address)  
    else
      @props.app.bookActionCreators.createShippingAddress(@state.book_shipping_address)

  updateShippingAddress: (e)->
    book_shipping_address = @state.book_shipping_address
    book_shipping_address[e.target.name] = e.target.value
    @setState
      book_shipping_address: book_shipping_address

  renderFieldError: (field, compressed)->
    if @state.error?
      if @state.error.validation_errors[field]?
        messages = @state.error.validation_errors[field]
        output = []
        for message in messages
          if compressed?
            output.push <div className="alert danger text-center">Error</div>
          else
            output.push <div className="alert danger text-center">{message}</div>
        return output
    return undefined    

  removeError: ()->
    @setState
      error: undefined

  render: ()->
    <div onClick={@removeError}>
      <h2 className="section-title">Shipping Address</h2>
      {
        if @props.isRequired?
          @props.isRequired('shipping')
      }
      <p>To calculate your shipping price, fill out the following information and click "Save and Calculate"</p>
      {
        if @props.book?.shipping_cost?
          <div className="alert success text-center">Shipping Cost is ${@props.book.shipping_cost}</div>
      }
      <div className="row">
        <div className="col-sm-6 col-md-12 col-lg-6">
          <div className="form-group">
            <label>First Name</label>
            <input name="first_name" type="text" className="form-control full-width" placeholder="First Name" value={@state.book_shipping_address.first_name} onChange={@updateShippingAddress}/>
            {
              @renderFieldError('first_name')
            }
          </div>
        </div>
        <div className="col-sm-6 col-md-12 col-lg-6">
          <div className="form-group">
            <label>Last Name</label>
            <input name="last_name" type="text" className="form-control full-width" placeholder="Last Name" value={@state.book_shipping_address.last_name} onChange={@updateShippingAddress}/>
            {
              @renderFieldError('last_name')
            }
          </div>
        </div>  
        <div className="col-sm-6 col-md-12 col-lg-6">
          <div className="form-group">
            <label>Address</label>
            <input name="street" type="text" className="form-control full-width" placeholder="Street Address" value={@state.book_shipping_address.street} onChange={@updateShippingAddress}/>
            {
              @renderFieldError('street')
            }
          </div>
        </div>
        <div className="col-sm-6 col-md-12 col-lg-6">
          <div className="form-group">
            <label>Address 2</label>
            <input name="street2" type="text" className="form-control full-width" placeholder="Address 2" value={@state.book_shipping_address.street2} onChange={@updateShippingAddress}/>
            {
              @renderFieldError('street2')
            }
          </div>
        </div>  
        <div className="col-sm-6 col-md-6 col-lg-6">
          <div className="form-group">
            <label>City</label>
            <input name="city" type="text" className="form-control full-width" placeholder="City" value={@state.book_shipping_address.city} onChange={@updateShippingAddress}/>
            {
              @renderFieldError('city')
            }
          </div>      
        </div>
        <div className="col-sm-3 col-md-6 col-lg-3">
          <div className="form-group">
            <label>State</label>
            <select className="select-menu form-control full-width" name="state" size="1" value={@state.book_shipping_address.state} onChange={@updateShippingAddress}>
              <option value="AK">AK</option>
              <option value="AL">AL</option>
              <option value="AR">AR</option>
              <option value="AZ">AZ</option>
              <option value="CA">CA</option>
              <option value="CO">CO</option>
              <option value="CT">CT</option>
              <option value="DC">DC</option>
              <option value="DE">DE</option>
              <option value="FL">FL</option>
              <option value="GA">GA</option>
              <option value="HI">HI</option>
              <option value="IA">IA</option>
              <option value="ID">ID</option>
              <option value="IL">IL</option>
              <option value="IN">IN</option>
              <option value="KS">KS</option>
              <option value="KY">KY</option>
              <option value="LA">LA</option>
              <option value="MA">MA</option>
              <option value="MD">MD</option>
              <option value="ME">ME</option>
              <option value="MI">MI</option>
              <option value="MN">MN</option>
              <option value="MO">MO</option>
              <option value="MS">MS</option>
              <option value="MT">MT</option>
              <option value="NC">NC</option>
              <option value="ND">ND</option>
              <option value="NE">NE</option>
              <option value="NH">NH</option>
              <option value="NJ">NJ</option>
              <option value="NM">NM</option>
              <option value="NV">NV</option>
              <option value="NY">NY</option>
              <option value="OH">OH</option>
              <option value="OK">OK</option>
              <option value="OR">OR</option>
              <option value="PA">PA</option>
              <option value="RI">RI</option>
              <option value="SC">SC</option>
              <option value="SD">SD</option>
              <option value="TN">TN</option>
              <option value="TX">TX</option>
              <option value="UT">UT</option>
              <option value="VA">VA</option>
              <option value="VT">VT</option>
              <option value="WA">WA</option>
              <option value="WI">WI</option>
              <option value="WV">WV</option>
              <option value="WY">WY</option>
            </select>
            {
              @renderFieldError('state', true)
            }
          </div>
        </div>  
        <div className="col-sm-3">
          <div className="form-group">
            <label>Zip</label>
            <input name="zip_code" type="text" className="form-control full-width" id="" placeholder="Zip" value={@state.book_shipping_address.zip_code} onChange={@updateShippingAddress}/>
            {
              @renderFieldError('zip_code', true)
            }
          </div>
        </div>        
      </div>
      <a onClick={@onCalculate} className="btn btn-teal btn-small pull-left">Save and Calculate</a>
    </div>

BookShippingCalculatorContainer = Marty.createContainer BookShippingCalculator,
  listenTo: ['bookStore']

  fetch: ()->
    hasShippingAddress = !_.isEmpty @props.book?.shipping_address
    return {
      book_shipping_address: if hasShippingAddress then @props.app.bookStore.getShippingAddressByURI(@props.book.shipping_address) else undefined
      book_shipping_success: @props.app.bookStore.getShippingAddressSuccess()
      error: @props.app.bookStore.getShippingAddressError()
      success: @props.app.bookStore.getShippingAddressSuccess()
    }
  done: (results)->
    props = _.extend {}, @props, results
    return <BookShippingCalculator {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <BookShippingCalculator {...props} />
  failed: (error)->
    console.log error
    return <div>Shipping Calculator Error</div>

module.exports = BookShippingCalculatorContainer

