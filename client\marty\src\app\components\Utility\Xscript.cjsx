React = require 'react'
ReactDOM = require 'react-dom'

# React blocks the actual script request, this componenent bypasses that block. 
# This component does not execute the script that is downloaded, just ensures it is downloaded. Can be modified to handle that condition (see react-ga for example)
# Without this technique, the script tag is inserted into the DOM but never requested.

XScript = React.createClass 
  initScripts: (el, url)->
    script = document.createElement('script')
    script.setAttribute('type', 'text/javascript')
    script.setAttribute('src', url)
    el.appendChild script
  
  componentDidMount: ()->
    @initScripts(ReactDOM.findDOMNode(@refs['it']), @props.url)
  
  render: ()->
    # div className="test" ref="it" dangerouslySetInnerHTML={{__html: '<script type="text/javascript" src="'+@props.url+'"></script>'}}></div>
    <div ref="it"></div>

module.exports = XScript