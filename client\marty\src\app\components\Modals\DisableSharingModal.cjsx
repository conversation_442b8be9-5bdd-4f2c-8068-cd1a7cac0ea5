React = require 'react'
Marty = require 'marty'
Modal = require('react-bootstrap').Modal

History = require('react-router').History

DisableSharingModal = React.createClass
  displayName: 'Disable Sharing Modal'
  mixins: [Marty.createAppMixin(), History]

  propTypes:
    type: React.PropTypes.string
    object: React.PropTypes.object
    onRequestHide: React.PropTypes.func

  onCancel: ()->
    if @props.onCancel?
      @props.onCancel()
    @props.onHide()

  onConfirm: ()->
    if @props.onConfirm?
      @props.onConfirm()
    @props.onHide()

  render: ()->
    <Modal onHide={@props.onHide} show={@props.showModal} {...@props}>
      <div className="text-center">
        <div className="modal-body">
          <div className="confirm-dialogue">
            <div className="alert danger">You are about to disable sharing!</div>
            <h2>When you disable sharing:</h2>
            <ul>
              <li>Entries you have shared will no longer be visible to others.</li>
              <li>Entries shared with you will no longer be visible.</li>
              <li>Your public profile will no longer be visible to others.</li>
            </ul>
            <p><strong>Are you sure?</strong></p>
            <ul className="share-buttons">          
              <li>
                <a onClick={@onCancel} className="btn btn-teal btn-large share-2">No</a>
              </li>
              <li>
                <a onClick={@onConfirm} className="btn btn-navy btn-large">Yes</a>
              </li>
            </ul>
          </div>    
        </div>
      </div>
    </Modal>

module.exports = DisableSharingModal