React = require 'react'
Marty = require 'marty'
Link = require('react-router').Link

Sticky = require 'react-sticky'

AllAboutMeHeader = React.createClass
  displayName: 'AllAboutMe Header'
  STICKY_STYLES: {}
  propTypes:
    title: React.PropTypes.string
    newButton: React.PropTypes.object

  render: ()->
    <div id="nav_crumb" className="gray">
      <div className="container">
        <div id="crumb-bar" className="pull-left">
          <div id="date" className="pull-left">
            <Link to={"/all-about-me/"} className="icon crumb-icon icon-all-about-me"></Link>
            <i className="icon icon-angle-right"></i><span>{@props.title}</span>
          </div>
          <div id="options" className="pull-right">
          </div>
        </div>
        <div id="tools" className="pull-right text-right">
          {@props.newButton}
        </div>
      </div>
    </div>
    


module.exports = AllAboutMeHeader