/*---------------------------------------------------------------------------
  >Dropdowns
---------------------------------------------------------------------------*/

.dropdown {

	&.open {
	
		.dropdown-toggle {
			.teal-txt !important;
		}
	}
	
	.dropdown-toggle:hover {
		.teal-txt !important;
	}
}

.dropdown-default {
	padding: 5px 0;
	
	li a {
	  font-size: 13px;
	  line-height: 16px;
	  padding: 5px 12px;
	  display: block;
		color: @gray4;
	  .hover-teal-txt;
	  .font1;
	  
	  &:focus, &:hover {background: transparent;}
	}
	
	.divider {
		margin: 5px 0;
	}

	.icon-check {
    top: -3px;
	  .f15;
	}
}

.dropdown-menu {
	
	.heading {
	  border-top: 1px solid rgba(0, 0, 0, 0.07);
	  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	  padding: 5px 13px;
	  margin: 5px 0;
		color: @gray5;
	  .f11;
		.gradient-w-g; //.gray1-bg;
		.w700;
	  .uppercase;
	}
	
	.heading:first-child {
	  margin-top: -5px;
		border-radius: 4px 4px 0 0;
		border-top: none;
	}
}
