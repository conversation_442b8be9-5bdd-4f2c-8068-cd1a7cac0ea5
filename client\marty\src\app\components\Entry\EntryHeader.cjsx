React = require 'react'
Marty = require 'marty'
TagsModal = require '../Modals/AddTagModal'
DateTimeModal = require '../Modals/DateTimeModal'

EntryHeader = React.createClass
  displayName: 'EntryHeader'
  mixins: []

  getInitialState: ->
    showEntryDateModal: false
  
  openEntryDateModal: ()->
    @setState
      showEntryDateModal: true
  
  closeEntryDateModal: ()->
    @setState
      showEntryDateModal: false

  onCancel: (e)->
    if @props.onCancel?
      @props.onCancel()
    e.stopPropagation()
    e.preventDefault()

  onSave: (e)->
    if @props.onSave?
      @props.onSave()
    e.stopPropagation()
    e.preventDefault()

  onSaveAndClose: (e)->
    if @props.onSave?
      @props.onSave(true)
    e.stopPropagation()
    e.preventDefault()

  renderDisplayTime: ()->
    if @props.display_time
      return (
        <span>@ {@props.entryDate.format('h:mm a')}</span>
      )
    else
      return undefined

  # TODO Add hidden fields that are used at lower media queries to truncate the day and month names
  render: ()->
    <div id="action-nav" className="teal-bg">
      <div className="container">
        <div className="pull-left">
          <ul className="nav navbar-nav">
            <li>
              <DateTimeModal entryDate={@props.entryDate} onDone={@props.onDone} {...@props} show={@state.showEntryDateModal} onHide={@closeEntryDateModal} showOptionalTime={true} isTimeOptional={@props.display_time}/>
              <a onClick={@openEntryDateModal} className="date">{@props.entryDate.format('dddd')}, {@props.entryDate.format('MMMM')} {@props.entryDate.format('Do')}, {@props.entryDate.format('YYYY')} {@renderDisplayTime()}</a>
            </li>
          </ul>
        </div>
        <div className="pull-right ">
          <ul className="nav navbar-nav">
            <li>
              <a onClick={@onCancel} className="btn-flat">Cancel</a>
            </li>
            <li>
              <a onClick={@onSave} className="btn-flat">Save</a>
            </li>
            <li>
              <a onClick={@onSaveAndClose} className="btn-flat">Save & Close</a>
            </li>
          </ul>
        </div>
      </div>
    </div>

module.exports = EntryHeader


