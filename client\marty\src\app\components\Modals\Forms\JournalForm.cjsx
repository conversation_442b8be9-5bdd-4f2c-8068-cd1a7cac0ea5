React = require 'react'
FormData = require 'react-form-data'

JournalForm = React.createClass
  displayName: 'JournalForm'
  mixins:[FormData]

  onChange: ()->
    return true

  render: ()->
    <ul>
      <li className="sub-heading">
      Journal Title
      </li>
      <li>
        <div className="form-group title" onChange={@.updateFormData}>
          <input type="text" className="full-input form-control full-width" id="title" name="title" placeholder="Please enter a title" value={@.formData.title} onChange={@onChange}/>
        </div>
      </li>
    </ul>

module.exports = JournalForm