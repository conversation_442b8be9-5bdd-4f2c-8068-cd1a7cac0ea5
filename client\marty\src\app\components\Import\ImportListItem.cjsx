React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'

ImportListItem = React.createClass
  displayName: 'ImportListItem'

  onSelect: (data)->
    @props.app.socialConnectActionCreators.selectInstagramMedia @props.media, data.target.checked

  render: ()->
    <div className={if @props.viewSize is 'small' then 'list-item list-item--select list-item--compact' else 'list-item list-item--select list-item--large'}>
      <div className="list-item__row">
        
        <div className="list-item__left list-item__left--check">
          <label className="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect">
            <input type="checkbox" className="mdl-checkbox__input" onChange={@onSelect} checked={@props.isChecked}/>
          </label>
        </div>
        
        <div className="list-item__img">
          <img src={@props.media.image_thumbnail_url} />
        </div>
        
        <div className="list-item__content">
          <div className="list-item__date">
            <i className="fa fa-instagram"></i>
              <strong>{moment(@props.media.ig_created_time).format("MMM Do, YYYY")}</strong> at {moment.utc(@props.media.ig_created_time).local().format("h:mma")}
          </div>
          <div className="list-item__excerpt">
            {@props.media.caption}
          </div>
          <div className="list-item__info">
            {
              if @props.media?.entries?.length > 0
                <button type="button" className="btn--link list-item__button--imports" disabled><i className="fa fa-arrow-circle-o-down"></i><span>Imports </span>({@props.media?.entries?.length})</button>    
            }
          </div>
        </div>
      
      </div>
    </div>

module.exports = ImportListItem