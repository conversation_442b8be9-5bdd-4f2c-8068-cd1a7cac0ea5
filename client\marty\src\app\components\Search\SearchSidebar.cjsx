React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

LoadMore = require '../Timeline/LoadMore'

SearchSidebar = React.createClass
  displayName: 'Search Sidebar'

  onLoadMore: ()->
    if @props.onLoadMore?
      @props.onLoadMore()

  render: ()->
    if @props.query? and @props.page? and @props.results?
      return (  
        <div id="default-sidebar" className="pull-right white-bg frame padding-20">
          <h3>Results</h3>
          <p style={"fontSize": "90%"}>Loaded <strong>{@props.results.length}</strong> results of <strong>{@props.page.total_count}</strong> found for '<strong>{@props.query}</strong>'.</p>
          <hr style={"margin": "10px 0"} />
        </div>
      )
    else
      return (
        <div id="default-sidebar" className="pull-right white-bg frame padding-20">
          <h3>Results</h3>
          <p style={"fontSize": "90%"}>Enter a search term to get started.</p>
          <hr style={"margin": "10px 0"} />
        </div>
      )


SearchSidebarContainer = Marty.createContainer SearchSidebar,
  fetch: ()->
    return {
      
    }
  done: (results)->
    props = _.extend {}, @props, results
    return <SearchSidebar {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <SearchSidebar {...props} />
  failed: (error)->
    console.log error
    return <div>Search Error</div>

module.exports = SearchSidebarContainer