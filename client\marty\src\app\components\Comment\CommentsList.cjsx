React = require 'react'
Marty = require 'marty'
_ = require 'underscore'


CommentsList = React.createClass
  displayName: 'CommentsList'

  onPostComment: (comment)->
    @.props.app.commentActionCreators.createComment(comment)

  render: ()->
    <div className="comments">
      <ul>
      {
        if @props.entry?.comments?
          _.sortBy @props.entry.comments, (comment)->
            return (new Date comment.created)
          .map (comment)=>
            return <Comment key={comment.id} comment={comment} {...@props} />
      }
      </ul>

    </div>

CommentsListContainer = Marty.createContainer CommentsList,
  listenTo: ['commentStore']

  fetch: ()->
    if @props.entry?
      return {
        comments: @.props.app.commentStore.getCommentsForEntry(@props.entry?.id)
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <CommentsList {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <CommentsList {...props} />
  failed: (error)->
    console.log error
    return <div>CommentsList Error</div>

module.exports = CommentsListContainer
