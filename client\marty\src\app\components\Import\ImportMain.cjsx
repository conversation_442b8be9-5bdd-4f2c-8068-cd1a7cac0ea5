React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'

{
  findDOMNode
} = require 'react'


ImportListItem = require './ImportListItem'

ImportMain = React.createClass
  displayName: 'ImportMain'

  getInitialState: ()->
    import_filter: 'show_all'
    showFilters: false
    viewSize: 'small'
    
  onPreview: ()->
    if not _.isEmpty @props.selected_instagram_media
      @.props.onSetStep 'preview'

  onBack: (data)->
    @props.onHide()

  onImportFilter: (event)->
    @props.app.socialConnectActionCreators.updateImportFilter event.target.value

  onSearch: (event)->  
    # @props.app.socialConnectQueries.searchInstagramMedia {q: event.target.value}
    @props.app.socialConnectActionCreators.searchWithinResults(event.target.value)

  onSelectAll: (data)->
    if data.target.checked
      @props.app.socialConnectActionCreators.bulkSelectInstagramMedia @props.instagram_media
    else
      @props.app.socialConnectActionCreators.bulkSelectInstagramMedia []

  onLoadMore: ()->
    if @props.instagram_media_meta?.next?
      @props.app.socialConnectQueries.loadMoreInstagramMedia(@props.instagram_media_meta?.next)

  toggleFilters: ()->
    @setState
      showFilters: !@state.showFilters

  toggleViewSize: (event)->
    @setState
      viewSize: event.target.value

  toggleLoadingState: ()->
    @props.app.socialConnectActionCreators.toggleRefreshContentLoadingState()

  refreshMedia: ()->
    @props.app.socialConnectActionCreators.startMediaImport()
    .then (response)=>
      window.setTimeout(@toggleLoadingState, 1500)

  renderHeader: ()->
    if @state.showFilters
      @renderFilterHeader()
    else
      @renderContentHeader()

  renderFilterHeader: ()->
    <div className="modal-header teal-bg">
      <div className="row">
        <div className="col-xs-2">
        </div>
        <div className="col-xs-8 text-center">
          <div className="modal-title">Filters</div>
        </div>
        <div className="col-xs-2">
          <a onClick={@toggleFilters} className="TEMP-trigger-import-main btn-flat pull-right"><i className="icon icon-check-thin"></i></a>
        </div>
      </div>
    </div>

  renderContentHeader: ()->
    <div className="modal-header teal-bg">
      <div className="row">
        <div className="col-xs-2">
          <a onClick={@onBack} className="btn-flat pull-left"><i className="icon icon-times"></i></a>
        </div>
        <div className="col-xs-8 text-center">
          <div className="modal-title">Add Social Content</div>
        </div>
        <div className="col-xs-2">
          
            {
              if @props.selected_instagram_media?.length > 0 
                <a className="TEMP-trigger-import-preview btn-flat pull-right" onClick={@onPreview}>
                  Preview<i className="icon icon-angle-right"></i>
                </a>
            }
        </div>
      </div>
    </div>

  renderBody: ()->
    if @state.showFilters
      <div className="modal-body">
        {
          @renderFilters()
        }
      </div>
    else
      @renderContent()

  renderFilters: ()->
    <div className="filters filters--import">
      <div className="filters__heading">Content Sources</div>
      <div className="filters__item filters__item--content-sources">
        <div className="checkbox">
          <label>
            <input type="checkbox" checked disabled />
            <i className="label--icon fa fa-instagram"></i>
            {@props.instagram_access_token?.ig_username}
          </label>
        </div>
      </div>
      <div className="filters__heading">Import Status</div>
      <ul className="filters__item">
        <li>
          <div className="radio">
            <label> 
              <input type="radio" name="options" value="not_imported" checked={if @props.import_filter_value is "not_imported" then true else false} onChange={@onImportFilter} />
              Not Imported
            </label>
          </div>
        </li>
        <li>
          <div className="radio">
            <label>
              <input type="radio" name="options" value="imported" checked={if @props.import_filter_value is "imported" then true else false} onChange={@onImportFilter} />
              Imported
            </label>
          </div>
        </li>
        <li>
          <div className="radio">
            <label>
              <input type="radio" name="options" value="show_all" checked={if @props.import_filter_value is "show_all" then true else false} onChange={@onImportFilter} />
              Show All
            </label>
          </div>
        </li>
      </ul>
    </div>

  renderRefreshButton: ()->
    if @props.showRefreshContentLoading
      <div className="refresh-button"><a className="btn btn-gray" onClick={@refreshMedia}><img src="/static/images/button-loader.gif" /></a></div>
    else
      <div className="refresh-button"><a className="btn btn-gray" onClick={@refreshMedia}>Refresh Content</a></div>  

  renderContent: ()->
    <div className="modal-body">
      <div className="import__col import__col--left">
        <div className="search search-import">
          <input type="text" placeholder="Search within results" onChange={@onSearch}/>
        </div>
        <div className="select-all">
          <div className="select-all__row">
            <div className="select-all__cell select-all__cell--left">
              <div className="checkbox">
                <label>
                  <input type="checkbox" onChange={@onSelectAll}/>
                  <strong>{ if @props.instagram_media? then @props.instagram_media.length else undefined}</strong> found, <strong>{ if @props.selected_instagram_media? then @props.selected_instagram_media.length else undefined}</strong> selected
                </label>
              </div>
            </div>
            <div className="select-all__cell select-all__cell--right">
              <select value={@state.viewSize} onChange={@toggleViewSize}>
                <option value="small">Small View</option>
                <option value="large">Large View</option>
              </select>
              <button onClick={@toggleFilters} className="TEMP-trigger-filters btn btn-gray"><img className="icon" src="/static/images/icon-filters.svg" /></button>
            </div>
          </div>
        </div>
        <div className="import-content">
          {
            @renderRefreshButton()
          }
          {
            if @props.instagram_media?
              media = _.sortBy @props.instagram_media, (media)-> 
                console.log moment(media.ig_created_time).unix()
                return -(moment(media.ig_created_time).unix())
              media.map (media)=>
                checked = false
                if @props.selected_instagram_media?
                  if _.findWhere(@props.selected_instagram_media, {id: media.id})?
                    checked = true
                <ImportListItem key={media.id} media={media} app={@props.app} isChecked={checked} viewSize={@state.viewSize}/>
          }
        </div>
        {
          if @props.instagram_media_meta?.next?
            <div className="load-more">
              <a className="btn btn-gray btn--load-more" onClick={@onLoadMore}>Load More Content</a>
            </div>
        }
      </div>
      <div className="import__col import__col--right">
        {
          @renderFilters()
        }
      </div>
    </div>

  render: ()->
    <div className="">
      {
        @renderHeader()
      }
      {
        @renderBody()
      }
    </div>
    

ImportMainContainer = Marty.createContainer ImportMain, 
  listenTo: ['socialStore']
  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        instagram_media: @app.socialStore.getInstagramMedia()
        instagram_access_token: @app.socialStore.getInstagramAccessToken()
        selected_instagram_media: @app.socialStore.getSelectedInstagramMedia()
        import_filter_value: @app.socialStore.getImportFilterValue()
        instagram_media_meta: @app.socialStore.getInstagramMediaMeta()
        showRefreshContentLoading: @app.socialStore.getRefreshContentLoadingState()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <ImportMain {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <ImportMain {...props} />
  failed: (errors)->
    console.log errors
    return <div>Import Error</div>

module.exports = ImportMainContainer