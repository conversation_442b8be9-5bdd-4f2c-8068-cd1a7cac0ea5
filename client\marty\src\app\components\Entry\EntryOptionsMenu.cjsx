React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

Link = require('react-router').Link
Dropdown = require('react-bootstrap').Dropdown

EntryOptionsMenu = React.createClass
  displayName: 'EntryOptionsMenu'

  getInitialState: ->
    isOpen: false

  getDefaultProps: ->
    detailURL: "/entry/"

  onToggle: (isOpen)->
    @setState
      isOpen: !@state.isOpen

  onSelect: (e, key)->
    console.log 'onSelect'
    @setState
      isOpen: false

  onBlockUser: (e)->
    console.log 'onBlockUser'
    alert 'This is not implemented yet'

  render: ()->
    <Dropdown id={"#{@props.entry?.id}-entry-options"} className="fr" onToggle={@onToggle} onSelect={@onSelect} open={@state.isOpen} componentClass={'div'}>
      <Dropdown.Toggle title={'Options'} noCaret={true} useAnchor={true}>
        <i className="icon icon-options" />
      </Dropdown.Toggle>
      <Dropdown.Menu className="dropdown-swing-left dropdown-default" style={minWidth: "120px"} componentClass={'ul'}>
        <li className="heading">Options</li>
        <li onClick={@onToggle}>
          <Link to={"#{@props.detailURL}" + @props.entry?.id} query={_.extend {}, @props.query,{comments:1}}>View</Link>
        </li>
        {
          if @props.entry?.isOwnedByMe
            <li>
              <Link to={"#{@props.detailURL}edit/" + @props.entry?.id} >Edit</Link>
            </li>            
        }
        {
          if @props.entry?.isOwnedByMe
            <li>
              <a onClick={@props.openDeleteModal}>Delete</a>
            </li>
        }
      </Dropdown.Menu>
    </Dropdown>         

module.exports = EntryOptionsMenu