React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

ImportPreview = React.createClass
  displayName: 'ImportPreview'

  onBack: (data)->
    @props.onSetStep 'main'

  onImportMedia: (data)->
    # Create entry per selected Media
    # @props.selected_instagram_media.map (media)=>
    #   @props.app.entryActionCreators.createEntryFromInstagram(media)
    # @props.app.socialConnectActionCreators.bulkSelectInstagramMedia([], false)
    # @props.onHide()

    if @props.onInsertInstagramMedia?
      @props.onInsertInstagramMedia @props.selected_instagram_media
      @props.app.socialConnectActionCreators.bulkSelectInstagramMedia([], false)
 
   render: ()->
      <div className="">
        <div className="modal-header teal-bg">
          <div className="row">
            <div className="col-xs-2">
              <a onClick={@onBack} className="TEMP-trigger-import-main btn-flat pull-left"><i className="icon icon-angle-left"></i></a>
            </div>
            <div className="col-xs-8 text-center">
              <div className="modal-title">Social Content Preview</div>
            </div>
            <div className="col-xs-2">
              <a onClick={@onImportMedia} className="btn-flat pull-right"><i className="icon icon-check-thin"></i></a>
            </div>
          </div>
        </div>
        <div className="modal-body">
          {
            if @props.selected_instagram_media?
              @props.selected_instagram_media.map (media)->
                <div className="preview preview--raw" key={media.id}>
                  <p>
                    <img src={media.image_url} />
                  <br />
                    {media.caption}
                  </p>
                </div>      
          }
        </div>
      </div>

ImportPreviewContainer = Marty.createContainer ImportPreview,
  listenTo: ['socialStore', 'journalStore']
  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        selected_instagram_media: @app.socialStore.getSelectedInstagramMedia()
        journals: @app.journalStore.getJournals('import-preview')
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <ImportPreview {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <ImportPreview {...props} />
  failed: (errors)->
    return <div>Import Preview Error</div>

module.exports = ImportPreviewContainer