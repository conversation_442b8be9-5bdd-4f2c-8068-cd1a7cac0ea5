React = require 'react'
Marty = require 'marty'
authStore = require('../application').authStore

module.exports = (ComposedComponent) ->
  AuthenticatedComponent = React.createClass
    displayName: 'Authenticated Component'
    statics:
      willTransitionTo: (transition)=>
        console.log 'TRANSITION'
        console.log transition
        if not authStore.isLoggedIn()
          transition.redirect '/login/', {}, {'nextPath': transition.path}

        if not authStore.isActive()
          transition.redirect '/signup/success/', {}, {}

    render: ()->  
      <ComposedComponent {...@props} />

  return AuthenticatedComponent
