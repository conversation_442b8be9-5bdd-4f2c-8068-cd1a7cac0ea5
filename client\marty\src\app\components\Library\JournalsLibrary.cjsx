React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Link = require('react-router').Link

Drawer = require '../Sidebar/Drawer'
AppHeader = require '../Header/AppHeader'
LibraryHeader = require './LibraryHeader'

OptionsList = require '../Modals/OptionsList/OptionsList'
EditModal = require '../Modals/EditJournalModal'
DeleteModal = require '../Modals/DeleteJournalModal'

JournalForm = require '../Modals/Forms/JournalForm'
LoadMore = require '../Timeline/LoadMore'
AuthenticatedComponent = require '../AuthenticatedComponent'

JournalOption = React.createClass
  displayName: "Journal Option Item"

  propTypes: 
    option: React.PropTypes.object
    onOptionSelected: React.PropTypes.func

  getInitialState: ->
    showEditJournalModal: false
    showDeleteJournalModal: false
  
  openEditJournalModal: ()->
    @setState
      showEditJournalModal: true

  openDeleteJournalModal: ()->
    @setState
      showDeleteJournalModal: true

  closeEditJournalModal: ()->
    @setState
      showEditJournalModal: false

  closeDeleteJournalModal: ()->
    @setState
      showDeleteJournalModal: false

  onClick: (option)->
    @props.onOptionSelected(option)
    
  renderDelete: ()->
    if @props.option.is_default or @props.option.journal_type is 'AllAboutMe'
      return
    else
      return ([<DeleteModal key={'deletemodal'+@props.option.id} type={'Journal'} object={@props.option} onHide={@closeDeleteJournalModal} show={@state.showDeleteJournalModal} {...@props}/>,<a key={'delete-icon'+@props.option.id} onClick={@openDeleteJournalModal} className="icon icon-times-circle"></a>])

  renderSelected: ()->
    if @props.isSelected?
      return (
        <div className="item-icon-right">
          <span className="icon icon-check pull-right"></span>
        </div>
      )
    else
      return undefined

  render: ()->
    entry_count = 0
    if @props.option.entry_count > 0 
      entry_count = @props.option.entry_count
    if @props.option.name?
      title = @props.option.name
    else
      title = @props.option.title

    <div className="item">
      <div className="item-icon-left">
        <span className="icon icon-journal"></span>
      </div>
      <Link to={"/journal/" + @props.option.id}>
        {title}
      </Link>
      <span className="sub-text">{entry_count} entries</span>
      <div className="options">
        {
          if @state.showEditJournalModal
            <EditModal type={'Journal'} object={@props.option} onHide={@closeEditJournalModal} show={@state.showEditJournalModal} {...@props}/>
        }
        <a onClick={@openEditJournalModal} className="icon icon-pencil"></a>
        {
          @renderDelete()  
        }
      </div>
    </div>

JournalsLibrary = React.createClass
  displayName: "JournalsLibrary"

  getInitialState: ->
    pager: {
      previous: @props.page?.previous
      next: @props.page?.next
      total_count: @props.page?.total_count
      offset: @props.page?.offset
      limit: @props.page?.limit
    }
   
  componentDidMount: ->
    @props.app.journalQueries.getJournals()
  
  componentWillReceiveProps: (nextProps)->    
    @setState
      pager: {
        previous: nextProps.page?.previous
        next: nextProps.page?.next
        total_count: nextProps.page?.total_count
        offset: nextProps.page?.offset
        limit: nextProps.page?.limit
      }

  onLoadMore: ()->
    if @state.pager.next?
      @.app.journalQueries.getPage(@state.pager.next, 'Journals')

  onJournalCreate: (journal)->
    @.app.journalActionCreators.createJournal(journal)

  renderPager: ()->
    if @state.pager?.next? and @props.journals?
      return (
        <div>
          <LoadMore pager={@state.pager} loaded={@props.journals.length} onClick={@onLoadMore}/>
        </div>
      )
    else
      return undefined

  renderError: ()->
    if @props.journal_error?
      return (
        <div className="alert danger alert-dismissible"><button type="button" className="close icon-times" onClick={@dismissAlert} aria-label="Close"></button>{@props.journal_error.user_message}</div>
      )
    else
      return undefined

  dismissAlert: ()->
    @.app.journalActionCreators.dismissError()

  render: ()->
    <div>
      <Drawer />
      <div id="page">
        <AppHeader view_title="Library" {...@props} />
        <LibraryHeader title={'Journals'} buttonLabel='New Journal' createForm={JournalForm} createAction={@onJournalCreate}/>
        <div id="content">
          <div className="container">
            <div className="section-heading">     
              <h2><img src="/static/images/icon-journal.svg" /> Journals</h2>
            </div>
            <div id="subsection" className="frame">
              {
                @renderError()
              }
              <OptionsList options={@props.journals} optionComponent={JournalOption} optionsSortBy={'title'} isEditable={true} app={@props.app}/>
              {
               @renderPager()
              }
            </div>
          </div>
        </div>
      </div>
    </div>

JournalsLibraryContainer = AuthenticatedComponent(Marty.createContainer JournalsLibrary,
  listenTo: ['journalStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn()
      return {
        journals: @props.app.journalStore.getJournals('Journals')
        page: @props.app.pageStore.getPage('Journals')
        username: @props.app.authStore.getUsername()
        firstname: @.props.app.authStore.getFirstNameOrUsername()
        user: @props.app.authStore.fetchUser()
        journal_error: @props.app.journalStore.getError()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <JournalsLibrary {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <div></div>
  failed: (error)->
    console.log error
    return <div>Journals Library Error</div>
)

module.exports = JournalsLibraryContainer