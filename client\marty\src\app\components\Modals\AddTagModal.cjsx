React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

Modal = require('react-bootstrap').Modal
Select = require 'react-select'

OptionsListHeader = require './OptionsList/OptionsListHeader'
OptionsList = require './OptionsList/OptionsList'

TagOption = React.createClass
  displayName: "Tag Option Item"

  propTypes: 
    option: React.PropTypes.object
    onOptionSelected: React.PropTypes.func
    onUpdateTags: React.PropTypes.func

  onClick: (option)->
    @props.onOptionSelected(option)
    
  renderSelected: ()->
    if @props.isSelected?
      return (
        <div className="item-icon-right">
          <span className="icon icon-check pull-right"></span>
        </div>
      )
    else
      return undefined

  render: ()->
    <a onClick={@onClick.bind null,@props.option}>
      <div className="item-icon-left">
        <span className="icon icon-tag"></span>
      </div>{@props.option.name}
      <span className="sub-text">{@props.option.entry_count || 0} Entries</span> 
      {@renderSelected()}
    </a>

TagModal = React.createClass
  displayName: "Tag Modal"

  getInitialState: ->
    selectedOptions: undefined
    isLoading: false
    allowCreate: false
    value: undefined
  
  componentWillReceiveProps: (nextProps) ->
    if nextProps.page? and nextProps.tags?
      if nextProps.page.next != null and nextProps.page.total_count > nextProps.tags.length
        @.app.tagsQueries.getPage(nextProps.page.next)
        @setState
          isLoading: true
      else 
        @setState
          isLoading: false

  
  onHide: ()->
    if @props.onHide?
      @props.onHide()

  onDone: ()->
    console.log 'onDone'

  getOptions: ()->
    if @props.tags?
      options = []
      for tag in @props.tags
        option = 
          value: tag.id
          label: tag.name
        options.push option
      return options
    else
      return undefined

  onLabelClick: ()->
    console.log 'onLabelClick'

  onChange: (newValue)->
    console.log 'onChange'
    console.log newValue

  newOptionCreator: (inputValue)->
    return {
      label: inputValue
      value: inputValue
    }

  filterOptions: (options, filterString, values)->
    filteredOptions = options
    
    if filterString.length > 0 
      filteredOptions = filteredOptions.map (option, i)=>      
        filter = option.label.substring 0, filterString.length        
        if filter isnt filterString
          return undefined
        else
          return option
    
    filteredOptions = _.reject filteredOptions, (option)->
      if option?
        if option.value in values
          return true
    
    filteredOptions = _.compact filteredOptions
    
    if _.isEmpty filteredOptions
      console.log "Empty"
      if not @state.allowCreate
        console.log "Allow create"
        @setState
          allowCreate: true
    # else
    #   if @state.allowCreate
    #     console.log "Disallow create"
    #     @setState
    #       allowCreate: false


    return filteredOptions

  onOptionSelected: (option)->
    console.log option

  render: ()->
    <Modal backDrop={'static'} show={@props.show} onHide={@onHide} {...@props} >
      <OptionsListHeader title={'All Tags'} onHide={@onHide} onDone={@onDone} />
      <div className="modal-body">
        <Select 
          ref="select"
          options={@getOptions()} 
          multi
          placeholder={"Tags yo"}
          className={'search'}
          filterOptions={@filterOptions}
          isLoading={@state.isLoading}
          onChange={@onChange}
          onOptionLabel={@onLabelClick}
          newOptionCreator={@newOptionCreator}
          allowCreate={@state.allowCreate}
          valueKey={'label'}
        />
        <OptionsList 
          optionsSortBy={'name'} 
          optionComponent={TagOption} 
          header={'Tags'} 
          options={@props.tags} 
          selectedOptions={@state.selectedOptions} 
          onOptionSelected={@onOptionSelected}
        />        
        
      </div>
    </Modal>

TagModalContainer = Marty.createContainer TagModal,
  listenTo: ['tagsStore']

  fetch: ()->
    return {
      tags: @.app.tagsStore.getTags('Tags')
      page: @.app.pageStore.getPage('Tags')
    }
  done:(results)->
    props = _.extend {}, @props, results
    return <TagModal {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <TagModal {...props} />
  failed: (error)->
    console.log error
    return <div>Tag Modal Error</div>

module.exports = TagModalContainer