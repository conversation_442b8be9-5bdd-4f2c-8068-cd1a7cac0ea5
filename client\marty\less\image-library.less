/*---------------------------------------------------------------------------
  >Image Library
---------------------------------------------------------------------------*/

.image-library {
  padding: 10px;
  overflow-x: hidden;
  overflow-y: scroll;
  max-height: 600px;

	h2 {
		.font2;
    .f21;
    color: @gray0;
    display: inline-block;
    width: 100%;
    padding: 8px;
    margin: 0;
	}

	img {
    border: 2px solid @gray10;
    float: left;
    margin: 5px;
    padding: 2px;
    .radius3;
    
    &:hover {
	    opacity: .6;
	    cursor: pointer;
	  }
  }
  
	.selected {
		border-color: @teal;
	}
	
	&.wallpaper img {
    @media @ss-xs {width: 122px; height: 72px;}
    @media @sm {width: 122px; height: 72px;}
    @media @md {width: 142px; height: 83px;}
    @media @lg-x {width: 150px; height: 88px;}
	}
}

.image-library-sample {
	border: 1px solid @gray8;
	padding: 2px;
	margin: 0 0 15px;
	display: block;
	max-height: 174px;
	width: auto;
	
	@media @ss {
		width: 100%;
		height: auto;
	}
}