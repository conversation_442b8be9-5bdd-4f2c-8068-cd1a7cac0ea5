React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

Modal = require('react-bootstrap').Modal
Link = require('react-router').Link

InvitationSelect = require './InvitationSelect'
constants = require './InvitationModalConstants'
config = require './InvitationModalConfig'
PublicProfileItem = require '../../PublicProfile/PublicProfileItem'

sanitizeHtml = require '../../../utils/sanitizeHtml'

InvitationModal = React.createClass
  displayName: 'InvitationModal'

  # constants
  CONTENT_TYPES: constants.CONTENT_TYPES
  CONTENT_STAGES: constants.CONTENT_STAGES
  # config
  types: config.types
  stages: config.stages
  
  # lifecycle
  getInitialState: ()->
    # UI
    bgClass: 'teal-bg'
    customMessageMaxCharacters: 200
    header: @props.initialHeader || 'Share'
    # Stages and types
    stage: @stages.INTRO.name
    type: undefined
    # UX state
    showFooter: false
    showCustomMessage: false
    allowCommenting: true
    customMessage: undefined
    useCustomMessage: false
    # multiple independent selects
    defaultSelectId: (new Date).getTime()
    selectIds: []
    selectValues: {}
    component_validation_error: undefined
    focusSelectAfterUpdate: false
    
  componentDidMount: ->
    # If loaded with a type then skip to the next step and setup the component visuals
    if @props.type?
      switch @props.type
        when 'group'
          @onGroupSharing(undefined)
        when '1on1', 'one_on_one'
          @onOneSharing(undefined)
  
  # actions
  processInvitations: ()->
    invitations = []
    
    invitations = @getAllSelectValues()
    
    if @state.allowCommenting
      granted_permissions = ['view_entry', 'view_entry_comments', 'add_entry_comments']
    else
      granted_permissions = ['view_entry', 'view_entry_comments']

    if @state.useCustomMessage
      if _.isEmpty @state.customMessage
        switch @state.type
          when @CONTENT_TYPES.ONE.name
            customMessage = @types.ONE.default_message
          when @CONTENT_TYPES.GROUP.name
            customMessage = @types.GROUP.default_message
      else
        customMessage = sanitizeHtml @state.customMessage
    else
      customMessage = undefined

    if customMessage?
      if customMessage.length > 0 
        customMessage = customMessage.replace(/\r?\n/g, '<br />')

    options = 
      display_channel: @state.type
      entry: @props.entry.id
      custom_message: customMessage
      granted_permissions: granted_permissions
    @props.app.invitationActionCreators.createInvitations(invitations, options)

  clearErrors: ()->
    if @state.component_validation_error? and not _.isEmpty @state.component_validation_error
      @setState
        component_validation_error: undefined
    return

  # event handlers
  onGroupSharing: (e)->
    if e?
      e.stopPropagation()
      e.preventDefault()
    @setState
      stage: @stages.INITIAL.name
      type: @types.GROUP.name
      bgClass: @types.GROUP.bgClass
      showFooter: @stages.INITIAL.showFooter

  onOneSharing: (e)->
    if e?
      e.stopPropagation()
      e.preventDefault()
    @setState
      stage: @stages.INITIAL.name
      type: @types.ONE.name
      bgClass: @types.ONE.bgClass
      showFooter: @stages.INITIAL.showFooter

  onContinue: (e)->
    if e?
      e.stopPropagation()
      e.preventDefault()


    if @state.stage is @stages.INITIAL.name
      canProceed = @validateForm()
    else
      canProceed = true
    
    if canProceed is true
      if @state.customMessage?
        customMessage = sanitizeHtml @state.customMessage
      else
        customMessage = undefined

      switch @state.stage
        when @stages.CONFIRM.name
          @setState
            stage: @stages.SEND.name
            header: @stages.SEND.header
            customMessage: customMessage
          , @processInvitations
        when @stages.SEND.name
          @props.app.invitationActionCreators.resetErrors()
          @props.onHide()
        else
          @setState
            stage: @stages.CONFIRM.name
            header: @stages.CONFIRM.header
            customMessage: customMessage
    else
      @setState
        component_validation_error: canProceed
        forceErrorDisplay: true
        customMessage: customMessage

  onAddCustomMessage: (e)->
    e.stopPropagation()
    e.preventDefault()
    if @state.type is 'group'
      default_message = @types.GROUP.default_message
    if @state.type is 'one_on_one'
      default_message = @types.ONE.default_message
    
    if @state.customMessage? 
      customMessage = @state.customMessage.replace(/\r?\n/g, '<br />')
    else 
      customMessage = default_message
    
    @setState
      showCustomMessage: true
      customMessage: customMessage
      useCustomMessage: true
      component_validation_error: undefined

  onRemoveCustomMessage: (e)->
    e.stopPropagation()
    e.preventDefault()
    @setState
      showCustomMessage: false
      customMessage: ''
      useCustomMessage: false
      component_validation_error: undefined

  onToggleAllowCommenting: (e)->
    e.stopPropagation()
    @setState
      allowCommenting: !@state.allowCommenting

  onCustomMessageInput: (e)->
    e.stopPropagation()
    if e.target?.value?
      customMessage = e.target.value
    @setState
      customMessage: customMessage
      messageCustomized: true

  onChangeInvitationSelect: (newValue, newSelectedValues, selectId)->
    validation_errors = []
    re = /.@(?!$)/
    allSelectValues = @getAllSelectValues()
    
    members = @getMemberList()
    users = members.map (member)=>
      return _.find @props.public_users, (user)=>
        if user.resource_uri is member or user.email is member
          return true
    users = _.compact users   
    
    cleanedValues = _.uniq newSelectedValues, (value)->
      return value.value

    if cleanedValues.length < newSelectedValues.length
      validation_errors.push
        error: 'Duplicate entry'
        selectId: selectId

    cleanedValues = _.reject cleanedValues, (value)=>
      reject = false
      if _.find users, {email: value.value}
        validation_errors.push
          error: 'Already invited'
          value: value.value
          selectId: selectId
        reject = true
      
      if value.value is @props.user.email
        validation_errors.push
          error: "That's you!"
          value: value.value
          selectId: selectId
        reject = true

      if re.test(value.value) is false
        validation_errors.push
          error: 'Not an email address'
          value: value.value
          selectId: selectId
        reject = true

      if _.contains allSelectValues, value.value 
        if @types.ONE.name is @state.type
          validation_errors.push
            error: 'Already selected'
            value: value.value
            selectId: selectId
          reject = true
      return reject

    selectIds = @state.selectIds
    selectValues = @state.selectValues
    defaultSelectId = @state.defaultSelectId

    if _.contains @state.selectIds, selectId
      selectIds = @state.selectIds
    else
      selectIds.push selectId
      if selectId is "#{@CONTENT_TYPES.ONE}-#{@state.defaultSelectId}"
        defaultSelectId = (new Date).getTime()

    selectValues[selectId] = cleanedValues

    if _.isEmpty cleanedValues
      addAdditionalEmptySelect = false
    else
      addAdditionalEmptySelect = true
    
    @setState
      selectValues: selectValues
      selectIds: selectIds
      component_validation_error: validation_errors
      defaultSelectId: defaultSelectId
      addAdditionalEmptySelect: addAdditionalEmptySelect

  onEdit: (e)->
    if @stages[@state.stage]?.previous?
      @setState
        stage: @stages[@state.stage].previous

  onHide: (e)->
    @props.app.invitationActionCreators.resetErrors()
    if @props.onHide?
      @props.onHide()

  onAddAnotherPerson: (e)->
    newId = @state.defaultSelectId
    newSelectId = "#{@CONTENT_TYPES.ONE}-#{newId}"
    newSelectedValues = @state.selectValues
    newSelectedValues[newSelectId] = []
    
    selectIds = @state.selectIds
    selectIds.push newSelectId
    
    @setState
      selectValues: newSelectedValues
      selectIds: selectIds
      defaultSelectId: (new Date).getTime()

  # type renderers
  renderDefaultInitial: ()->
    <div className="share-modal-body">
      {
        if @state.type is @CONTENT_TYPES.ONE
          <label>1 on 1 Sharing</label>
          <span className="label-subtext">All comments are private between you and each contributor.</span>
      }
      {
        @renderMembers()
      }
      {
        @renderSelects()
      }
      {
        @renderOptions()
      }
      {
        @renderCustomMessage()
      }
    </div>

  renderDefaultConfirm: ()->
    formatConfirmation = "Inviting to view"
    if @state.allowCommenting
      formatConfirmation = "#{formatConfirmation} and comment"
       
    inviteTargets = []
    for id in @state.selectIds
      if _.isArray @state.selectValues[id]
        values = @state.selectValues[id].map (value)->
          value.label
      else
        values = undefined
      
      if not _.isEmpty values
        inviteTargets = inviteTargets.concat values
    
    if @state.useCustomMessage
      customMessage = @state.customMessage
      if @state.messageCustomized
        title = 'Custom'
      else
        title = 'Default'
    else
      customMessage = undefined

    if customMessage?
      if customMessage.length > 0
        customMessage = customMessage.replace(/\r?\n/g, '<br />')

    <div className="share-modal-body">
      <div className="group-share-confirm">
        <div className="contacts">
          <label>
          {
            formatConfirmation + ":"
          }
          </label>
          {
            inviteTargets.map (value)=>
              if value != _.last inviteTargets
                <span key={value}>{value + ', '}</span>
              else
                <span key={value}>{value}</span>
          }          
        </div>
        {
          if customMessage?
            
            <div className="custom-message">
              <label>{title} Message:</label>  
              <p dangerouslySetInnerHTML={{__html: customMessage}}>
              </p>
            </div>
          else
            <div className="custom-message">
              <label>No Message Added:</label>  
              {
                "No custom message added."     
              }
            </div>
        }
      </div>
    </div>

  renderDefaultSend: ()->
    <div className="share-modal-body">
      <div id="list" className="list-avatar"> 
          {
            if @props.success
              <div className="list-heading">
                Invitation sent successfully for:
              </div>
          }
          {
            if @props.invitations?
              @props.invitations.map (invitation)=>
                if invitation.display_channel is @state.type
                  if invitation.invite_sent_to_email in @getAllSelectValues() 
                    if invitation.shared_user?
                      <PublicProfileItem key={invitation.id} isUser={true} user={invitation.shared_user} invitation={invitation.id} app={@props.app} onDelete={undefined} showSuccess={true} theme={'item'}/>
                    else if invitation.shared_non_user_email?
                      <PublicProfileItem key={invitation.id} isUser={false} user={invitation.shared_non_user_email} invitation={invitation.id} app={@props.app} onDelete={undefined} showSuccess={true} theme={'item'}/>
          }
          {
            if @props.error? and @props.failed_invitations
              <div className="list-heading">Invitation had errors</div>
          }
          {
            if @props.error? and @props.failed_invitations
              @props.failed_invitations.map (invite)=>
                if invite.shared_email in @getAllSelectValues()
                  <PublicProfileItem key={invite.shared_email} isUser={false} user={invite.shared_email} invitation={undefined} app={@props.app} onDelete={undefined} showError={true} errorMessage={@props.error.user_message} theme={'item'}/>
                  # <div>{invite.shared_email} FAILED DUE TO {@props.error.user_message}</div>
          }
      </div>
    </div>

  renderDefaultIntro: ()->
    <div className="sharing-intro">
      <div className="table-row">
        <div className="item">
          <div className='img-wrapper'>
            <img src="/static/images/sketch-sharing-group.jpg" />
          </div>
          <a onClick={@onGroupSharing} className="btn btn-color-group btn-large">Group Sharing</a>
          <div className="subtitle">
            Everyone can see each other's comments if they are enabled.
          </div>
        </div>        
        <div className="item">
          <div className='img-wrapper'>
            <img src="/static/images/sketch-sharing-1on1.jpg" />
          </div>
          <a onClick={@onOneSharing} className="btn btn-color-1on1 btn-large">1 on 1 Sharing</a>
          <div className="subtitle">
            Only you and the people you invite can see each other's comments
          </div>
        </div>
      </div>
    </div>

  renderSharingDisabled: ()->
    <div className="sharing-intro">
      <div className="table-row">
        <div className="item">
          <Link to={'/settings/'} className="btn btn-color-group btn-large">Enable Sharing</Link>
          <div className="subtitle">
            Sharing is currently disabled. Enable sharing in your Profile Settings.
          </div>
        </div>        
      </div>
    </div>

  # utility/partial renderers
  validateForm: ()->
    validation_errors = []
    selectValues = @getAllSelectValues()
    if _.isEmpty selectValues
      validation_errors.push {error: 'Must have at least one value to continue', isFormInvalid: true}

    if _.isEmpty validation_errors
      return true
    else 
      return validation_errors

  filterOptions: (options, filter, currentValues)->
    allSelectedOptions = []
    filter = filter.toLowerCase()
    
    for id in @state.selectIds
      allSelectedOptions = allSelectedOptions.concat @state.selectValues[id]

    filteredOptions = _.reject options, (option)->
      if option.value in _.pluck allSelectedOptions, 'value'
        return true
      if filter.length > 0 
        # Start of label or email
        labelTest = if (option.label.toLowerCase().substring 0, (filter.length)) is filter then true else false
        emailTest = if (option.email?.toLowerCase().substring 0, (filter.length)) is filter then true else false
        if not labelTest and not emailTest
          return true
        # Any part of label
        # if (option.label.toLowerCase().indexOf(filter)) < 0
        #   remove = true
      return false
    return filteredOptions

  prefilterOptionsByType: ()->
    invitation_users = _.pluck _.filter(@props.invitations, {display_channel: @state.type}), 'shared_user'
    invitation_users.push @props.app.authStore.getMyPublicUser()
    public_user_options = []
    if @props.public_users?
      public_user_options = @props.public_users.map (public_user)->
        if not _.contains invitation_users, public_user.resource_uri
          {
            label: "#{public_user.public_display_name} (#{public_user.email})"
            value: public_user.email
            className: 'known-user'
          }
    return _.compact public_user_options

  getMemberPublicProfileItems: ()->
    if @props.invitations?
      members = @props.invitations.map  (invitation)=>
        if invitation.display_channel in [@state.type, @props.type]
          if invitation.shared_user?
            <PublicProfileItem key={invitation.id} isUser={true} user={invitation.shared_user} invitation={invitation.id} app={@props.app} onDelete={@onDelete}/>
          else if invitation.shared_non_user_email?
            <PublicProfileItem key={invitation.id} isUser={false} user={invitation.shared_non_user_email} invitation={invitation.id} app={@props.app} onDelete={@onDelete}/>
      if not _.isEmpty _.compact members
        return members
    return undefined

  getMemberList: ()->
    if @props.invitations?
      members = @props.invitations.map  (invitation)=>
        if invitation.display_channel in [@state.type, @props.type]
          if invitation.shared_user?
            return invitation.shared_user
          else if invitation.shared_non_user_email?
            return invitation.shared_non_user_email
      if not _.isEmpty _.compact members
        return members
    return []

  getAllSelectValues: ()->
    invitations = []
    for select in @state.selectIds
      @state.selectValues[select].map (value)=>
        invitations.push value.value
    return invitations

  renderMembers: ()->
    members = @getMemberPublicProfileItems()
    if members?
      <div className='contacts'>
        <label>Currently invited:</label>
        <div id="list" className="list-avatar"> 
        {
          members
        }
        </div>
      </div>

  renderSelects: ()->
    content = []
    switch @state.type
      when @CONTENT_TYPES.GROUP
        selectProps = @types.GROUP.selectProps
        addAdditionalEmptySelect = false
      when @CONTENT_TYPES.ONE
        selectProps = @types.ONE.selectProps
        selectProps = _.extend {}, selectProps, {
          filterOptions: @filterOptions
        }
        addAdditionalEmptySelect = @state.addAdditionalEmptySelect
    
    if @state.selectIds.length > 0
      content = @state.selectIds.map (id)=>
        <InvitationSelect ref={id} key={id} selectId={id} onChange={@onChangeInvitationSelect} value={@state.selectValues[id]} options={@prefilterOptionsByType()} component_validation_error={@state.component_validation_error} {...selectProps}/>
      
      if addAdditionalEmptySelect
        selectId = "#{@state.type}-#{@state.defaultSelectId}"  
        content.push <InvitationSelect ref={selectId} key={selectId} selectId={selectId} onChange={@onChangeInvitationSelect} options={@prefilterOptionsByType()} component_validation_error={@state.component_validation_error} value={undefined} {...selectProps} />
    else
      selectId = "#{@state.type}-#{@state.defaultSelectId}"
      content.push <InvitationSelect ref={selectId} key={selectId} selectId={selectId} onChange={@onChangeInvitationSelect} options={@prefilterOptionsByType()} component_validation_error={@state.component_validation_error} value={undefined} {...selectProps} />
    return content    

  renderFooter: ()->
    nextLabel = 'Continue'
    switch @state.type
      when @CONTENT_TYPES.GROUP
        if @state.stage is @CONTENT_STAGES.INITIAL
          showAllowCommentingCheckbox = true
        if @state.stage is @CONTENT_STAGES.CONFIRM
          showEditButton = true
          nextLabel = 'Share Now'
        if @state.stage is @CONTENT_STAGES.SEND
          showEditButton = false
          nextLabel = 'Close'
      when @CONTENT_TYPES.ONE
        if @state.stage is @CONTENT_STAGES.CONFIRM
          showEditButton = true
          nextLabel = 'Share Now'
        if @state.stage is @CONTENT_STAGES.SEND
          showEditButton = false
          nextLabel = 'Close'
    return (
      <div className="modal-footer">
        {
          if showAllowCommentingCheckbox
            <div className="checkbox pull-left">
              <label>
                <input type="checkbox" checked={@state.allowCommenting} onChange={@onToggleAllowCommenting}/> Allow commenting
              </label>
            </div>
        }
        {
          if showEditButton
            <a onClick={@onEdit} className="btn btn-gray btn-large pull-left">Edit</a>
        }
        <a onClick={@onContinue} className="btn btn-navy btn-large">{nextLabel}</a>
      </div>
    )

  renderOptions: ()->
    if @state.showCustomMessage
      <div className="share-modal-options">
        <div className="row">
          <div className="col-xs-6 col-left">
            <a onClick={@onRemoveCustomMessage}>Remove Message</a>
          </div>
          {
            if @state.type is @CONTENT_TYPES.ONE and @state.selectIds.length > 0
              <div className="col-xs-6 col-right text-right">
                <a onClick={@onAddAnotherPerson}>Add Another Person</a>
              </div>    
          }
        </div>
        <div className="row">
          <div className="col-xs-12 col-right">
            <div className="help-text">{@state.customMessage?.length || 0}/200 characters</div>
          </div>
        </div>
      </div>
    else
      <div className="share-modal-options">
        <div className="row">
          <div className="col-xs-6 col-left">
            <a onClick={@onAddCustomMessage}>Add Custom Message</a>
          </div>
          {
            if @state.type is @CONTENT_TYPES.ONE
              <div className="col-xs-6 col-right text-right">
                <a onClick={@onAddAnotherPerson}>Add Another Person</a>
              </div>
          }
        </div>
      </div>

  renderCustomMessage: ()->
    if @state.type is 'group'
      default_message = @types.GROUP.default_message
    if @state.type is 'one_on_one'
      default_message = @types.ONE.default_message

    if @state.showCustomMessage
      <textarea 
        autoFocus
        ref="customMessage"
        className="form-control" 
        maxLength={@state.customMessageMaxCharacters}
        value={@state.customMessage.replace(/<br\s*[\/]?>/gi, "\n")}
        onChange={@onCustomMessageInput} 
        placeholder={default_message}
      >
      </textarea>
    else
      return undefined
      
  # stage renderers
  renderIntro: ()->
    return @renderDefaultIntro()

  renderInitial: ()->
    return @renderDefaultInitial()

  renderConfirm: ()->
    return @renderDefaultConfirm()

  renderSend: ()->
    return @renderDefaultSend()

  renderContent: ()->
    if @props.user?.sharing
      switch @state.stage
        when @CONTENT_STAGES.INTRO
          return @renderIntro()
        when @CONTENT_STAGES.INITIAL
          return @renderInitial()
        when @CONTENT_STAGES.CONFIRM
          return @renderConfirm()
        when @CONTENT_STAGES.SEND
          return @renderSend()
        else 
          return @renderIntro()
      return undefined
    else
      return @renderSharingDisabled()
    
  render: ()->
    headerClass = 'modal-header ' + @state.bgClass
    <Modal className="share-modal" backdrop={'static'} {...@props} keyboard={false} onHide={@onHide}>
      <div className={headerClass}>
        <div className="row">
          <div className="col-sm-3"></div>
          <div className="col-sm-6 text-center">
            <div className="modal-title">
              {
                @state.header
              }              
            </div>
          </div>
          <div className="col-sm-3">
            <a className="btn-flat pull-right" onClick={@onHide}>Cancel</a>
          </div>
        </div>
      </div>            
      {
        @renderContent()
      }
      {
        if @state.showFooter
          @renderFooter()
      }
    </Modal>

InvitationModalContainer = Marty.createContainer InvitationModal,
  listenTo: ['invitationStore', 'publicUserStore', 'authStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      if @props.entry? and @props.user?.sharing
        return {
          invitations: @props.app.invitationStore.getSharedEntryInvitations({entry__in: @props.entry?.id})
          error: @props.app.invitationStore.getError()
          success: @props.app.invitationStore.getSuccess()
          failed_invitations: @props.app.invitationStore.getFailedInvitations()
          public_users: @props.app.publicUserStore.getPublicUserList()
        }
      else
        return {}
    else
      return {}
  
  done: (results)->
    props = _.extend {}, @props, results
    return <InvitationModal {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <InvitationModal {...props} />
  failed: (error)->
    console.log error
    return <div>InvitationModal Error</div>

module.exports = InvitationModalContainer
