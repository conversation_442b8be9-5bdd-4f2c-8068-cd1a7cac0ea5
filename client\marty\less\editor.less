/*---------------------------------------------------------------------------
  >Editor
---------------------------------------------------------------------------*/

#editor { 
	
	.btn-title {
    border: 1px solid @gray7;
    padding: .3em .6em .3em .3em;
    border-radius: 3px;
    top: 2px;
    font-size: 18px;
    font-weight: 600;
    max-width: 100%;
    display: inline-block;
    color: @navy;
    .gradient-w-g;
    .truncate;
        
    &:hover {color: @teal;}
    
    .icon {
	    @media @sm {top: 0;}
	    @media @md-x {top: -1px;}
    }
	}
				
	#editor_column {
		width: 100%;
    background-clip: padding-box;
		margin-bottom: 200px;
    .radius3;
    .shadow;
		
		.form-group {margin-bottom: 0;}
		
		.entry-title {
		  padding: .4em .6em;
		  height: inherit;
		  color: @teal;
		  border-radius: 3px 3px 0 0;
			border: none;
		  .f20;
			.font2;
		  .w400; 
		}
			
		.post-stats {
			border-radius: 0 0 3px 3px;
			padding: 5px 10px;
			border: none;
			border-top: 1px solid @gray9;
			display: table;
			width: 100%;
			color: @gray5;
			.gradient-g-w;
		  .f10;
		  
		  .pull-left, .pull-right {
			
			  @media @ss {width: 100%;}
			}
		  
			.divider {
			  padding: 0 7px;
			  filter: alpha(opacity=50);
			  -moz-opacity: 0.5;
			  opacity: 0.5;
			}
		}
	}
}