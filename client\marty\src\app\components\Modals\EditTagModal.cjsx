React = require 'react'
Marty = require 'marty'
FormData = require 'react-form-data'

Modal = require('react-bootstrap').Modal

OptionsListHeader = require '../Modals/OptionsList/OptionsListHeader'

EditTagModal = React.createClass
  displayName: 'Edit Tag Modal'
  mixins: [FormData, Marty.createAppMixin()]

  propTypes:
    type: React.PropTypes.string
    object: React.PropTypes.object
    onRequestHide: React.PropTypes.func

  getInitialState: ->
    query: @props.object.name
  
  onDone: ()->
    newTag = @props.object
    if @.formData.tagName?
      if @formData.tagName != newTag.name and @formData.tagName != ""
        newTag.name = @formData.tagName
        @.app.tagActionCreators.updateTag(newTag)
    @props.onHide()

  onChange: (e)->
    query = e.target.value
    @setState
      query: query

  render: ()->
    <Modal onHide={@props.onHide} show={@props.show} >
      <OptionsListHeader title={'Edit Tag'} show={@props.show} onHide={@props.onHide} onDone={@onDone} />
      <div className="modal-body">
        <ul>
          <li className="sub-heading">
            { "Edit" + @props.type}
          </li>
          <li>
            <div className="form-group title" onChange={@updateFormData}>
              <input type="text" className="full-input form-control full-width" id="tagName" name="tagName" placeholder="Please enter a title" value={@state.query} onChange={@onChange}/>
            </div>
          </li>
        </ul>
      </div>
    </Modal>

module.exports = EditTagModal