React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'
Link = require('react-router').Link

AppHeader = require '../Header/AppHeader'
BookbuilderHeader = require './BookbuilderHeader'
Drawer = require '../Sidebar/Drawer'
LoadMore = require '../Timeline/LoadMore'

Bookbuilder = React.createClass
  displayName: 'Bookbuilder'

  getInitialState: ->
    allChecked: false
    selectedBooks: []
    pager: {
        previous: @props.page?.previous
        next: @props.page?.next
        total_count: @props.page?.total_count
        offset: @props.page?.offset
        limit: @props.page?.limit
      }

  componentDidMount: ->
    window.scrollTo(0,0)

  componentWillReceiveProps: (nextProps) ->
    @setState
      pager: {
        previous: nextProps.page?.previous
        next: nextProps.page?.next
        total_count: nextProps.page?.total_count
        offset: nextProps.page?.offset
        limit: nextProps.page?.limit
      }
  
  onDeleteBook: (book)->
    @.app.bookActionCreators.deleteBook book

  onDeleteSelected: ()->
    for book in @state.selectedBooks
      @.app.bookActionCreators.deleteBook book
    return

  onSelectAll: ()->
    @setState
      selectedBooks: @props.books
      allChecked: !@state.allChecked
    return

  onBookSelect: (book)->
    if (_.findWhere @state.selectedBooks, {id: book.id})?
      selectedBooks = _.without @state.selectedBooks, _.findWhere @state.selectedBooks, {id: book.id}
      @setState
        selectedBooks: selectedBooks
        allChecked: false
    else  
      selectedBooks = @state.selectedBooks
      selectedBooks.push book
      @setState
        selectedBooks: selectedBooks
    return

  onNewBook: ()->
    @props.history.pushState null, '/book/new/'

  onLoadMore: ()->
    if @state.pager.next?
      @.app.bookQueries.getPage(@state.pager.next, 'Books')

  onUpdateJournal: (journal)->
    @.app.bookActionCreators.createBook({journal: journal.id}, false)

  renderPager: ()->
    if @state.pager?.next? and @props.books?
      return (
        <div className="frame full-width">
          <LoadMore pager={@state.pager} loaded={@props.books?.length} onClick={@onLoadMore}/>
        </div>
      )
    else
      return undefined
      
  renderBookRow: (book)->
    return (
      <tr key={book.id}>
        <td className="col-check"><input type="checkbox" defaultChecked={false} checked={ if _.findWhere @state.selectedBooks, {id: book.id} then true else false } onChange={@onBookSelect.bind null, book}/></td>
        <td className="col-name">
          <div className="title"><Link to={"/book/content/" + book.id}>{book.title || "No Title"}</Link></div>
          <div className="options"><Link to={"/book/content/" + book.id}>Edit</Link><a onClick={@onDeleteBook.bind null, book}>Delete</a></div>
        </td>
        <td className="col-date">
          <span>{moment.utc(book.created, 'YYYY-MM-DDTHH:mm:ss').local().format('MMM, D YYYY')}</span>
        </td>
        <td className="col-modified">
          <span>{moment.utc(book.modified, 'YYYY-MM-DDTHH:mm:ss').local().format('MMM, D YYYY')}</span>
        </td>
      </tr>
    )

  render: ()->
    <div>
      <Drawer />
      <div id="page">
        <AppHeader view_title='Bookbuilder' {...@props}/>
        <BookbuilderHeader hasSelected={@state.selectedBooks.length > 0} hasOrders={if @props.orders? and !_.isEmpty(@props.orders) then true else undefined} onDeleteSelected={@onDeleteSelected} onNewBook={@onNewBook} />
        <div id="content">
          <div className="container">
            <div id="bookbuilder-column" className="pull-left full-width">
              <div className="alert section-intro teal-bg alert-dismissible">
                <div className="section-intro-body">
                  <img className="fr intro-image-float" src="/static/images/bbdr-intro-image.png" alt="aam-instruction-image" />
                  <h2>Easily create and publish amazing archival-quality books to enjoy for generations to come.</h2>
                  <h3 style={marginBottom: "30px"}>High quality. Print on demand. Fully customizable.</h3>
                </div>
              </div>
              <table className="table frame table-bookbuilder">
                <thead>
                  <tr>
                    <th className="col-check">
                      <input type="checkbox" checked={@state.allChecked} onChange={@onSelectAll}/>
                    </th>
                    <th className="col-name">
                      <a>Name <span className="icon icon-caret-down"></span></a>
                    </th>
                    <th className="col-name">
                      <a>Created <span className="icon icon-caret-down"></span></a>
                    </th>
                    <th className="col-name">
                      <a>Modified <span className="icon icon-caret-down"></span></a>
                    </th>
                  </tr>
                </thead>
                <tbody>
                { 
                  if @props.books?
                    _.sortBy @props.books, (book)->
                      return (parseInt(book.id) * -1)
                    .map (book)=>
                      return (
                        @renderBookRow(book)
                      )
                }
                </tbody>
              </table>  
              {
                @renderPager()
              }
            </div>
          </div>
        </div>
      </div>
    </div>

BookbuilderContainer = Marty.createContainer Bookbuilder,
  listenTo: ['bookStore', 'authStore', 'bookOrderStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        books: @.props.app.bookStore.getBooks('Books')
        username: @.props.app.authStore.getUsername()
        firstname: @props.app.authStore.getFirstNameOrUsername()
        user: @props.app.authStore.fetchUser()
        page: @props.app.pageStore.getPage('Books')
        orders: @props.app.bookOrderStore.getBookOrders('Bookbuilder')
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Bookbuilder {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Bookbuilder {...props} />
  failed: (error)->
    console.log error
    return <div>BOOKBUILDER ERROR</div>

module.exports = BookbuilderContainer