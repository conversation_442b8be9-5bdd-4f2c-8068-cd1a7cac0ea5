React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'
FormData = require 'react-form-data'

Lifecycle = require('react-router').Lifecycle

Drawer = require '../Sidebar/Drawer'
AppHeader = require '../Header/AppHeader'
EntryHeader = require '../AllAboutMe/AllAboutMeEntryHeader'
JournalsAndEventsModal = require '../Modals/JournalsAndEventsModal'

DateTimeModal = require '../Modals/DateTimeModal'

# TODO npm package for global namespace access
AuthenticatedComponent = require '../AuthenticatedComponent'
Redactor = require '../Redactor/Redactor'
MediaManagerModal = require '../MediaManager/Modals/MediaManagerModal'
TagSelect = require '../Entry/TagSelect'
ImportModal = require '../Import/ImportModal'

AllAboutMeEntry = React.createClass
  displayName: 'AllAboutMeEntry'
  mixins: [FormData, Lifecycle]

  getInitialState: ->
    tags: undefined
    entryDate: if @props.entry?.entry_date? then moment.utc(@props.entry.entry_date).local() else if @props.location.query.entry_date? then @defaultEntryDate(moment(@props.location.query.entry_date)) else moment() 
    hasChanged: false
    success: undefined
    shouldClose: false
    showMediaManager: false
    redactor_element: undefined
    showImport: false
    inserted_entry_images: []
    replaceImagesAndSave: undefined

  componentDidMount: ->
    @.props.app.allaboutmeActionCreators.updateRandom()

  componentWillReceiveProps: (nextProps) ->
    if nextProps.success?
      @setState
        success: nextProps.success

    if nextProps.entry?.entry_date?
      @setState
        entryDate: moment.utc(nextProps.entry.entry_date).local()

    if nextProps.success?
      @setState
        success: nextProps.success

    if @state.replaceImagesAndSave? and nextProps.session_entry_images?
      content = @refs.redactor.getRedactorValue()
      entry_images = @findEntryImages(content, nextProps.session_entry_images)
      content = @replaceEntryImages(content, entry_images)
      
      if @props.entry?.id?
        @updateEntry(@state.replaceImagesAndSave.shouldClose, content, entry_images)
      else
        @createEntry(@state.replaceImagesAndSave.shouldClose, content, entry_images)
      
      @setState
        replaceImagesAndSave: undefined
        hasChanged: false
        shouldClose: @state.replaceImagesAndSave.shouldClose
        last_saved: moment()

  componentWillUpdate: (nextProps, nextState) ->
    if @state.success and @state.shouldClose
      @props.history.pushState null, @getOriginatingLocation()

  getOriginatingViewId: ()->
    viewId = undefined
    if @props.params.journalId?
      viewId = 'Journal' + @props.params.journalId
    else if @props.params.tagId?
      viewId = 'Tag' + @props.tagId
    else if @props.location.query.aamId?
      viewId = 'Question' + @props.params.questionId
    else
      viewId = 'Timeline'
    return viewId

  getOriginatingLocation: ()->
    location = undefined
    if @props.params.journalId?
      return '/journal/' + @props.params.journalId
    else if @props.params.tagId?
      return '/tag/' + @props.params.tagId
    else if @props.location.query.aamId?
      return "/all-about-me/question/" + @props.question?.id
    else
      return '/'

  getOptions: ()->
    options = undefined
    if @props.params.journalId?
      options = {journal: @props.params.journalId}
    else if @props.params.tagId?
      options = {tags__id: @props.params.tagId}
    else if @props.params.aamId?
      options = {question: @props.params.questionId}
    else
      options = {}
    return options
   
  routerWillLeave: (nextLocation)->
    if @state.hasChanged and not @state.isSaved
      return "Your changes will be lost. Are you sure you want to leave?"
  
  handleSubmit: (shouldClose, options)->
    content = @refs.redactor.getRedactorValue()
    
    if @state.inserted_entry_images.length > 0
      entry_image_queue = @state.inserted_entry_images.map (media)=>
        if content.indexOf "entry_image_pending_media_#{media.id}" >= 0
          return media
        else
          return undefined
      
      entry_image_queue = _.compact entry_image_queue
    
      if not _.isEmpty entry_image_queue
        @.app.socialConnectActionCreators.createEntryImages entry_image_queue
        @setState
          replaceImagesAndSave: {shouldClose: shouldClose, options: options}
      else
        if @.props.params.entryId?
          @updateEntry(shouldClose, content)
          @setState
            hasChanged: false
            shouldClose: shouldClose
            last_saved: moment()
        else
          @createEntry(shouldClose, content)
          @setState
            hasChanged: false
            shouldClose: shouldClose
            last_saved: moment()
    else
      content = @refs.redactor.getRedactorValue()
      if @.props.params.entryId?
        @updateEntry(shouldClose, content)
        @setState
          hasChanged: false
          shouldClose: shouldClose
          last_saved: moment()
      else
        @createEntry(shouldClose, content)
        @setState
          hasChanged: false
          shouldClose: shouldClose
          last_saved: moment()

  updateEntry: (shouldClose, content, entry_images)->
    newEntry = 
      id: @props.entry.id
      title: @formData.entryTitle
      content: content
      entry_date: moment(@state.entryDate).utc().format('YYYY-MM-DD'+'T'+'HH:mm:ss')
      display_time: @state.display_time
      tags: @state.tags || @props.entry.tags
      entry_type: "QA"
      journal: @props.journal.resource_uri
      question: @props.question.resource_uri
    
    @.app.allaboutmeActionCreators.updateAnswer newEntry, shouldClose, @getOriginatingViewId(), @getOptions(), {aamId: @props.location.query.aamId}, entry_images
    
  createEntry: (shouldClose, content, entry_images)->
    newEntry = 
      title: @formData.entryTitle
      content: content
      journal: if @state.journal? then @state.journal.id else if @props.journal? then @props.journal.id else @props.defaultJournal.id
      tags: @state.tags
      entry_date: moment(@state.entryDate).utc().format('YYYY-MM-DD'+'T'+ 'HH:mm:ss')
      display_time: @state.display_time
      question: @props.question.id
      entry_type: "QA"
    
    @.app.allaboutmeActionCreators.saveAnswer newEntry, shouldClose, @getOriginatingViewId(), @getOptions(), {aamId: @props.location.query.aamId}, entry_images


  formDataDidChange: (formData) ->
    @formData.redactor = @refs.redactor.getRedactorValue() 

  openMediaManager: ()->
    @setState
      showMediaManager: true

  closeMediaManager: ()->
    @setState
      showMediaManager: false

  openImport: ()->
    @setState
      showImport: true

  closeImport: ()->
    @setState
      showImport: false

  onUpdateTags: (addTags, createTags)->
    newState = {}
    newState.addTags = addTags
    newState.createTags = createTags
    
    currentTags = @state.tags || @props.entry?.tags
    
    if @props.params.entryId?
      newTags = addTags.map (tag)->
        return tag.resource_uri
    else
      newTags = addTags.map (tag)->
        return tag.id

    if _.difference currentTags, newTags
      newState.tags = newTags
      newState.hasChanged = true
      newState.debug_event = 'Tags updated'
    
    if createTags?
      newState.hasChanged = true
      newState.debug_event = 'Tags updated'

    @setState newState
      
  onEntryChange: ()->
    @setState
      hasChanged: true
      debug_event: 'Entry Body updated'

  onCancel: ()->
    @props.history.pushState null, @getOriginatingLocation()

  onInsertMedia: (files)->
    if @state.redactor_element?
      for file in files
        image = '<p><img src="' + file.filelink + '" /></p>'
        @state.redactor_element.redactor('insert.html', image, false)
      @state.redactor_element.redactor('observe.images')
    @closeMediaManager()

  onInsertInstagramMedia: (media)->
    if @state.redactor_element?
      image = ""
      for file in media
        image = image.concat "<p><img id=\"entry_image_pending_media_#{file.id}\"src=\"#{file.image_url}\" /></p><p>#{file.caption}</p>"
      @state.redactor_element.redactor('insert.html', image, false)
      @state.redactor_element.redactor('observe.images')
      
      @setState
        inserted_entry_images: @state.inserted_entry_images.concat media
        waitForEntryImageBeforeSave: true
    @closeImport()

  onRedactorInit: (redactor)->
    @setState
      redactor_element: redactor

  replaceEntryImages: (content, entry_images)->
    for image in entry_images
      imageId = "entry_image_pending_media_#{image.instagram_media}"
      pendingRegex = '<img id="'+imageId+'" src="(.*?)">'
      url = content.match pendingRegex
        
      if url?
        content = content.replace(url[1], image.image)
        replace_id = "entry_image_pending_media_" + image.instagram_media
        content = content.replace(replace_id, "entry_image_#{image.id}")

    return content

  findEntryImages: (content, session_entry_images)->
    entry_images = []
    if session_entry_images?
      for image in session_entry_images
        imageId = "entry_image_pending_media_#{image.instagram_media}"
        if content.indexOf(imageId) >= 0
          entry_images.push image
    return entry_images

  renderTags: ()->
    if @props.entry? or @props.mode =='add'
      return <TagSelect entryTags={@props.entry?.tags} onUpdateTags={@onUpdateTags} lastSaved={@state.last_saved} {...@props} />
  
  render: ()->
    modifiedDate = moment.utc(@props.entry?.modified)
    view_title = @props.journal?.title
    <div id="editor">
      <Drawer />
      <div id="page">
        <AppHeader username={@props.username} view_title={view_title} view_title_is_editable={false} {...@props}/>
        <EntryHeader onCancel={@onCancel} onSave={@handleSubmit} entryTags={@props.entry?.tags} onUpdateTags={@onUpdateTags} {...@props} />
        <div id="content">
          {
            if @state.showMediaManager
              <MediaManagerModal show={@state.showMediaManager} onHide={@closeMediaManager} onDone={@onInsertMedia} {...@props} />
          }
          {
            if @state.showImport
              <ImportModal show={@state.showImport} onHide={@closeImport} {...@props} onInsertInstagramMedia={@onInsertInstagramMedia}/>
          }
          <div onChange={@.updateFormData}>
            <div className="container">
              <div className="header__buttons">
                <a className="trigger-modal-import btn btn-gray" onClick={@openImport}>
                  <img className="icon" src="/static/images/icon-import-social.svg" />
                  <span>Import</span> Social <span>Content</span>
                </a>
                <a className="trigger-modal-media btn btn-gray" onClick={@openMediaManager}>
                  <img className="icon" src="/static/images/icon-import-media.svg" />
                  <span>Add</span> Photos</a> 
              </div>
              <div id="editor_column" className="pull-left full-width">
                <div className="form-group">
                  <div className="aam-entry-title">{@props.question?.text}</div>
                </div>
                {
                  if @props.entry?.content? or @props.mode == 'add'
                    <Redactor ref="redactor" content={@props?.entry?.content} onRedactorChange={@onEntryChange} onRedactorOpenMedia={@openMediaManager} onRedactorInit={@onRedactorInit} onRedactorImport={@openImport} {...@props} />
                  else
                    undefined
                }
                {
                  @renderTags()
                }
                <div className="post-stats">
                  <div className="pull-left">Last updated: {modifiedDate.local().format('MMMM Do, YYYY [at] hh:mm:ss a')}</div>
                </div>
              </div>          
            </div>
          </div>
        </div>
      </div>
    </div>

AllAboutMeEntryContainer = AuthenticatedComponent(Marty.createContainer AllAboutMeEntry,
  listenTo: ['entryStore', 'journalStore', 'allaboutmeStore', 'socialStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      entryId = @props.params.entryId
      if entryId?
        return {
          firstname: @.props.app.authStore.getFirstNameOrUsername()
          username: @.props.app.authStore.getUsername()
          user: @props.app.authStore.fetchUser()
          journal: @.props.app.journalStore.getAllAboutMeJournal()
          entry: @.props.app.entryStore.getEntryById(entryId)
          mode: 'edit'
          success: @props.app.entryStore.getSuccess()
          session_entry_images: @props.app.socialStore.getSessionEntryImages()
        }
      else
        questionId = @props.params.questionId
        if questionId?
          return {
            firstname: @.props.app.authStore.getFirstNameOrUsername()
            username: @.props.app.authStore.getUsername()
            user: @props.app.authStore.fetchUser()
            journal: @.props.app.journalStore.getAllAboutMeJournal()
            question: @.props.app.allaboutmeStore.getQuestionById(questionId)
            mode: 'add'
            success: @props.app.entryStore.getSuccess()
            session_entry_images: @props.app.socialStore.getSessionEntryImages()
          }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    if results.entry?.question?
      props['question'] = @props.app.allaboutmeStore.getQuestionByURI(results.entry.question).result
    return <AllAboutMeEntry {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <AllAboutMeEntry {...props} />
  failed: (error)->
    console.log error
    return <div>AllAboutMe Entry ERROR</div>
)

module.exports = {
  AllAboutMeEntryContainer: AllAboutMeEntryContainer
}


