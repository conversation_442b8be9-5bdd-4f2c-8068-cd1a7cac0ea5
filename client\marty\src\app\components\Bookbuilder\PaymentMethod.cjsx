React = require 'react'
Marty = require 'marty'
moment = require 'moment'
_ = require 'underscore'

AddPaymentProfileModal = require('../Modals/PaymentProfileModal').AddPaymentProfileModal
DeletePaymentProfileModal = require '../Modals/DeletePaymentProfileModal'

PaymentMethod = React.createClass
  displayName: 'PaymentMethod'

  CARD_TYPES:
    'mc': 'Mastercard'
    'visa': 'Visa'
    'discover': 'Discover'

  getInitialState: ->
    showDeleteModal: false
    showEditModal: false
    showAddModal: false
    success: undefined

  componentWillReceiveProps: (nextProps) ->
    if nextProps.success != @state.success and nextProps.success
      @setState
        showAddModal: false
      @props.app.billingStore.reset()

  openDeleteModal: ()->
    @setState
      showDeleteModal: true

  closeDeleteModal: ()->
    @setState
      showDeleteModal: false

  closeEditModal: ()->
    @setState
      showEditModal: false

  openAddModal: (e)->
    e.stopPropagation()
    e.preventDefault()
    @setState
      showAddModal: true

  closeAddModal: ()->
    @setState
      showAddModal: false

  openEditModal: ()->
    @setState
      showEditModal: true

  formatCardType: ()->
    if @props.payment_profile?.card_type?
      return @CARD_TYPES[@props.payment_profile?.card_type]
    else
      return undefined

  formatExpirationDate: ()->
    if @props.payment_profile?.expiration_date?
      return moment.utc(@props.payment_profile?.expiration_date).format('MM/YY')
    else
      return undefined    

  renderEmpty: ()->
    return (
      <div className="summary">
        <div className="help-text">Credit card on file</div>
        No credit card on file.
      </div>
    )

  renderSummary: ()->
    return (
      <div className="summary">
        <div className="help-text">Credit card on file</div>
        <p><strong>{@formatCardType()}</strong> Ending In {@props.payment_profile?.safe_credit_card_number}</p>
      </div>
    )

  renderButton: ()->
    if @props.payment_profile?
      return <div className="button-wrapper"><button onClick={@openAddModal} className="btn btn-gray btn-small">Use a different card</button></div>
    else
      return <div className="button-wrapper"><button onClick={@openAddModal} className="btn btn-navy btn-small">Add Card</button></div>
  
  render: ()->
    return (
      <div className="col-left">
        <div id="payment_method" className="billing-tile">
          <div className="panel panel-default">
            <div className="panel-heading">
              <AddPaymentProfileModal show={@state.showAddModal} onHide={@closeAddModal} {...@props}/>
              {
                @renderButton(0)
              }
              Payment Method
            </div>
            <div className="panel-body">
              {
                if @props.payment_profile?
                  @renderSummary()
                else
                  @renderEmpty()
              }
            </div>
          </div>
        </div>
      </div>
    )

PaymentMethodContainer = Marty.createContainer PaymentMethod,
  listenTo: ['billingStore']

  getPaymentProfile: ()->
    return @getInnerComponent().props.payment_profile

  fetch: ()->
    if @props.app.authStore.isLoggedIn()
      hasPaymentProfileURI = @props.payment_profile_uri?
      return {
        payment_profile: if hasPaymentProfileURI then @props.app.billingStore.getPaymentProfileByURI(@props.payment_profile_uri) else undefined
        success: @props.app.billingStore.getSuccess()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <PaymentMethod ref="innerComponent" {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <PaymentMethod ref="innerComponent" {...props}/>
  failed: (error)->
    console.log error
    return <div>Payment Profile Error</div>


module.exports = PaymentMethodContainer