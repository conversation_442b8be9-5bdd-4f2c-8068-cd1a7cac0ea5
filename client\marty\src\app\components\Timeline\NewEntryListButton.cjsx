React = require 'react'

Link = require('react-router').Link

moment = require 'moment'

NewEntryListButton = React.createClass
  displayName: 'NewEntryListButton'

  getPeriod: ()->
    switch @props.view_mode
      when 'day'
        period = moment.utc(@props.min_date)
        return period.format('MMMM D, YYYY')
      when 'month'
        period = moment(@props.min_date)
        return period.format('MMMM YYYY')
      when 'year'
        period = moment(@props.min_date)
        return period.format('YYYY')

  renderNewEntryButton: ()->
    if @props.journalId?
      if @props.journal?
        if @props.journal?.journal_type == 'AllAboutMe' and @props.random_question?.id?
          return <Link to={"/all-about-me/new/" + @props.random_question.id} query={entry_date: @props.min_date, tag_id: @props.tagId} className="btn-list-new btn btn-small btn-gray full-width">New Entry for {@getPeriod()}</Link>
        else
          return <Link to={"/entry/journal/create/" + @props.journalId} query={entry_date: @props.min_date} className="btn-list-new btn btn-small btn-gray full-width">New Entry for {@getPeriod()}</Link>
      else
        return <Link to={"/entry/journal/create/" + @props.journalId} query={entry_date: @props.min_date, tag_id: @props.tagId} className="btn-list-new btn btn-small btn-gray full-width">New Entry for {@getPeriod()}</Link>
    else
      return <Link to={"/entry/create/"} query={entry_date: @props.min_date, tag_id: @props.tagId} className="btn-list-new btn btn-small btn-gray full-width">New Entry for {@getPeriod()}</Link>


  render: ()->
    <div>
      {
        @renderNewEntryButton()
      }
    </div>

module.exports = NewEntryListButton