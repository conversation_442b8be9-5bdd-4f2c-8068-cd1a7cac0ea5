React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'

JournalsAndEventsModal = require '../Modals/JournalsAndEventsModal'
DatePickerModal = require '../Modals/DatePickerModal'

Lifecycle = require('react-router').Lifecycle

BookContent = React.createClass
  displayName: 'BookContent'
  mixins: [Lifecycle]

  getInitialState: ->
    journal_options: @getJournalOptionsState(@props.book)
    showStartDatePickerModal: false
    showEndDatePickerModal: false
    showJournalsModal: false
    defaultUsed: if @props.mode == 'add' then true else false
    saved: true

  componentWillReceiveProps: (nextProps) ->
    if nextProps.book?
      @setState
        journal_options: @getJournalOptionsState(nextProps.book)

    if nextProps.saved?
      @setState
        saved: nextProps.saved

  routerWillLeave: ()->
    if !@state.saved
      return "Discard unsaved changes?"

  getJournalOptionsState: (book)->
    if book?
      if book.journal?
        if book.start_date? and book.end_date?
          return 'range'
        else
          return 'all'
      else if book.journal is null
        return 'do_not_print'
    

  formatJournalDateRange: ()->
    if @props.journal? and @state.journal_options != 'do_not_print'
      if @props.journal.first_entry_date? and @props.journal.last_entry_date?
        journalDateRange = 
          minDate: moment(@props.journal?.first_entry_date).format('MMM D, YYYY')
          maxDate: moment(@props.journal?.last_entry_date).format('MMM D, YYYY')
        return journalDateRange
      else
        return undefined
    else
      return undefined

  updateBook: (e)->
    if @props.updateBook?
      @props.updateBook(e)

  onJournalOption: (e)->
    switch e.target.name
      when 'journal_options'
        switch e.target.value
          when 'do_not_print'
            @setState
              journal_options: e.target.value
              defaultUsed: false
            fakeEvent = 
              target: 
                name: 'journal'
                value: null
            if @props.onDoNotPrintEntries?
              @props.onDoNotPrintEntries(fakeEvent, true)
          else 
            @setState
              journal_options: e.target.value
            if @props.onDoNotPrintEntries?
              @props.onDoNotPrintEntries(undefined, false)


  openJournalsModal: ()->
    @setState
      showJournalsModal: true

  closeJournalsModal: ()->
    @setState
      showJournalsModal: false

  openStartDatePickerModal: ()->
    if @state.journal_options != 'do_not_print'
      @setState
        showStartDatePickerModal: true


  closeStartDatePickerModal: ()->
    @setState
      showStartDatePickerModal: false

  openEndDatePickerModal: ()->
    if @state.journal_options != 'do_not_print'
      @setState
        showEndDatePickerModal: true

  closeEndDatePickerModal: ()->
    @setState
      showEndDatePickerModal: false

  onStartDate: (date)->
    if @props.updateStartDate?
      @props.updateStartDate(moment(date,'YYYY-MM-DD'))

  onEndDate: (date)->
    if @props.updateEndDate?
      @props.updateEndDate(moment(date,'YYYY-MM-DD'))

  onUpdateJournal: (journal)->
    if @props.updateJournal?
      @props.updateJournal(journal)
    @setState
      defaultUsed: false

  onNext: ()->
    if @props.onSave?
      @props.onSave(true, '/book/cover/')

  defaultUsed: ()->
    if @state.defaultUsed
      return 'teal-txt'
    else
      return 'teal-txt'
  
  renderDateRangeError: (date_type)->
    if @props.journal? and @state.journal_options == 'range' and (@props.book?.start_date? or @props.book?.end_date?)
      switch date_type
        when "start_date"
          if @props.book?.start_date != null
            if moment(@props.journal?.first_entry_date).isAfter(moment(@props.book?.start_date), 'day')
              return <div className="alert warning text-center">Start date before first entry date.</div>
        when "end_date"
          if @props.book?.end_date != null
            if moment(@props.journal?.last_entry_date).isBefore(moment(@props.book?.end_date), 'day')
              return <div className="alert warning text-center">End date before last entry date.</div>
        else
          return undefined
    else
      return undefined

  renderDefaultJournalWarning: ()->
    if @state.defaultUsed
      return <div className="alert warning text-center">Your default journal was selected for you.</div>
    else
      return undefined

  renderJournalTitle: ()->
    if @state.journal_options == 'do_not_print'
      return <span className="red-txt">None</span>
    else 
      if @props.book?
        if @props.journal?
          if @props.mode == "add"
            if @props.journal.id == @props.book.journal
              return @props.journal.title
          else
            if @props.journal.resource_uri == @props.book.journal
              return @props.journal.title
      return <span className="gray3-txt">Retrieving journal...</span>
              

  render: ()->
    journalDateRange = @formatJournalDateRange()
    <form>
      <div className="row">
        <div className="col-sm-8 col-lg-6">
          <h2 className="section-title">Export Format</h2>
          <div className="form-group">
            <div className="radio">
              <ul>
                <li>
                  <label>
                    <input type="radio" name="export_format" value="6X9" checked={true} onChange={@updateBook}/>
                    6" x 9" Printed Book (900 page limit)
                    </label>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div className="col-sm-4 col-lg-4">
          <div className="description">
            <p>Printed books will include a downloadable PDF after purchase.</p>
          </div>
        </div>
      </div>
      <hr />
      <div className="row">
        <div className="col-sm-8 col-lg-6">
          <h2 className="section-title">Journal & Journal Entries</h2>
          {
            @renderDefaultJournalWarning()
          }
          <div className="form-group">
            <span><h3>Selected Journal: <strong className={@defaultUsed()}>{ @renderJournalTitle() }</strong></h3></span>
          </div>
          <div className="form-group" style={height:"30px"}>
              <div>
                <JournalsAndEventsModal onUpdateJournal={@onUpdateJournal} onHide={@closeJournalsModal} show={@state.showJournalsModal} {...@props}/>
                <a onClick={@openJournalsModal} className="btn btn-navy btn-small pull-left">Change Journal</a>  
              </div>
          </div>
          <div className="form-group">
            <div className="radio">
              <ul>
                <li>
                  <label>
                    <input type="radio" name="journal_options" value="all" checked={ if @state.journal_options is 'all' then true else false } disabled={!@props.book?.journal?} onChange={@onJournalOption} />
                    Print all journal entries { if @props.journal? and @state.journal_options != 'do_not_print' then '(' + @props.journal.entry_count + ')'}
                    </label>
                </li>
                <li>
                  <label>
                    <input type="radio" name="journal_options" value="range" disabled={!@props.book?.journal?} checked={ if @state.journal_options is 'range' then true else false } onChange={@onJournalOption} onClick={@openStartDatePickerModal}/>                    
                    Print a specific date range (Select a date range)
                  </label>
                  <DatePickerModal minDate={@props.journal?.first_entry_date} maxDate={@props.journal?.last_entry_date} options={if @props.journal? then {journal:@props.journal.id} else undefined} date={moment(@props.journal?.first_entry_date)} calendar_id={'Journal' + @props.journal?.id} onDone={@onStartDate} onHide={@closeStartDatePickerModal} show={@state.showStartDatePickerModal} initialViewMode={'month'} {...@props}/>
                  <span><input name="start_date" type="text" className="form-control date-field" placeholder={journalDateRange?.minDate} value={if @props.book?.start_date? then moment(@props.book?.start_date).format('MMM D, YYYY')} readOnly onClick={@openStartDatePickerModal}/>
                  {
                    @renderDateRangeError('start_date')
                  }
                  <br/>&nbsp;&nbsp;to&nbsp;&nbsp;<br /></span>
                  <DatePickerModal minDate={@props.journal?.first_entry_date} maxDate={@props.journal?.last_entry_date} options={if @props.journal? then {journal:@props.journal.id} else undefined} date={moment(@props.journal?.last_entry_date)} calendar_id={'Journal' + @props.journal?.id} onDone={@onEndDate} onHide={@closeEndDatePickerModal} show={@state.showEndDatePickerModal} initialViewMode={'month'} {...@props}/>
                  <input name="end_date" type="text" className="form-control date-field" placeholder={journalDateRange?.maxDate} value={if @props.book?.end_date then moment(@props.book?.end_date).format('MMM D, YYYY')} readOnly onClick={@openEndDatePickerModal}/>
                  {
                    @renderDateRangeError('end_date')
                  }
                </li>
                <li>
                  <label>
                    <input type="radio" name="journal_options" value="do_not_print" checked={ if @state.journal_options is 'do_not_print' then true else false } onChange={@onJournalOption}/>                    
                    <strong>Do not print</strong> journal entries
                  </label>
                </li>
              </ul>
            </div>
          </div>
        </div>      
        <div className="col-sm-4 col-lg-4">
          <div className="description">
            <p>Choose to have all entries included in your book OR select a specific range of entries by beginning and end date.</p>
          </div>
        </div>
      </div>     
      <hr />
      <div className="row">        
        <div className="col-sm-8 col-lg-6">
          <h2 className="section-title">All About Me</h2>
          <div className="form-group">
            <div className="radio">
              <ul>
                <li>
                  <label>
                    <input type="radio" name="all_about_me" value="MR" checked={ if @props.book?.all_about_me is 'MR' then true else false } onChange={@updateBook}/>
                    Print most recent answers</label>
                </li>
                <li>
                  <label>
                    <input type="radio" name="all_about_me" value="ALL" checked={ if @props.book?.all_about_me is 'ALL' then true else false } onChange={@updateBook}/>
                    Print all Answers
                  </label>
                </li>
                <li>
                  <label>
                    <input type="radio" name="all_about_me" value="" checked={ if @props.book?.all_about_me is '' then true else false } onChange={@updateBook}/>
                    <strong>Do not print</strong> any All About Me Questions
                  </label>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div className="col-sm-4 col-lg-4">             
          <div className="description">
            <p>Please choose how you would like your 'All About Me' questions and answers included in your exported book.</p>
          </div>
        </div>
      </div>
      <hr/>
      <div className="row">
        <div className="col-sm-8 col-lg-6">
          <h2 className="section-title">Color Options</h2>
          <div className="form-group">
            <div className="radio">
              <ul>
                <li>
                  <label>
                    <input type="radio" name="color" value="true" checked={@props.book?.color} onChange={@updateBook}/>
                    Print in Full Color</label>
                </li>
                <li>
                  <label>
                    <input type="radio" name="color" value="false" checked={!@props.book?.color} onChange={@updateBook}/>
                    Print in Black and White
                  </label>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div className="col-sm-4 col-lg-4">             
          <div className="description">
            <p>Choose to export your book in color or black and white. You might want to consider black and white if you book contains only text.</p>
          </div>
        </div>
      </div>
      <hr/>
      <h2 className="section-title">Photo Options</h2>        
      <div className="form-group">
        <div className="radio">
          <ul>
            <li>
              <label>
                <input type="radio" name="photos" value="true" checked={@props.book?.photos} onChange={@updateBook}/>
                Print photos
              </label>
            </li>
            <li>
              <label>
                <input type="radio" name="photos" value="false" checked={!@props.book?.photos} onChange={@updateBook}/>
                <strong>Do not print</strong> photos
              </label>
            </li>
          </ul>
        </div>
      </div>
      <hr/>
      <a onClick={@onNext} className="btn btn-navy btn-large pull-right">Next</a>
    </form>

BookContentContainer = Marty.createContainer BookContent,
  listenTo: []
  
  fetch:()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <BookContent ref="innerComponent" {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <BookContent ref="innerComponent" {...props} />
  failed: (error)->
    console.log error
    return <div>Book Content Error</div>

module.exports = BookContentContainer
