React = require 'react'
Marty = require 'marty'
moment = require 'moment'
_ = require 'underscore'

DatePicker = require 'react-date-picker'
DatePickerModal = require '../../Modals/DatePickerModal'
MonthView = require('react-date-picker').MonthView

CalendarWidget = React.createClass
  displayName: 'Calendar Widget'

  getInitialState: ->
    view: 'month'
    date: moment(@props.min_date, 'YYYY-MM-DD'+'T'+'HH:mm:ss')
    selectedDate: moment(@props.min_date, 'YYYY-MM-DD'+'T'+'HH:mm:ss')
    journalId: @props.journalId
    lockWidgetToView: @props.lockWidgetToView
  
  componentWillReceiveProps: (nextProps) ->
    if nextProps.lockWidgetToView
      @setState
        lockWidgetToView: true
        date: moment(nextProps.min_date, 'YYYY-MM-DD'+'T'+'HH:mm:ss')
        selectedDate: moment(nextProps.min_date, 'YYYY-MM-DD'+'T'+'HH:mm:ss')

  onViewChange: (view)->
    @setState
      view: view
    return

  mapView: (pickerView)->
    JRNL_VIEW_MODES =
      decade: 'year'
      year: 'year'
      month: 'month'

    if pickerView?
      return JRNL_VIEW_MODES[pickerView]
    else
      return 'day'
      
  onSelect: (date, momentDate, view)->
    @setState
      date: momentDate.startOf(@mapView(view))
      view: 'month'
    if @props.widgetCallback?
      @props.widgetCallback('calendar',date, @mapView(view))

  onChange: (date, momentDate, e)->
    @setState
      date: momentDate
      selectedDate: moment(date)
    if @props.widgetCallback?
      @props.widgetCallback('calendar', date, 'day')

  onToday: ()->
    date = new Date()
    @setState
      view: 'month'
      date: moment(date)
      selectedDate: moment(date)
    if @props.widgetCallback?
      @props.widgetCallback('calendar', date)

  onDone: (value)->  
    if @props.widgetCallback?
      @props.widgetCallback('calendar', value)

  onNav: (dateText, dateMoment, view, direction)->
    options = {}
    id = 'Timeline'
    if @props.journalId?
      options = _.extend options, {journal: @props.journalId}
      id = 'Journal' + @props.journalId
    if @props.tagId?
      options = _.extend options, {tags__id: @props.tagId}
      id = 'Tag' + @props.tagId
    
    @.app.calendarActionCreators.updateCalendar(options, id)
    @.app.calendarActionCreators.unlockWidget()

    @setState
      date: dateMoment
      lockWidgetToView: false
    
  onRenderDay: (props)->
    year = props.date.year()
    month = props.date.month()+1
    day = props.date.date()
    
    classNames = props.className.split ' '
    
    if props.date.isAfter(moment())
      classNames.push 'dp-upcoming'

    if @state.date.endOf('month').isBefore(props.date)
      classNames = _.without classNames, 'dp-current'
      classNames.push 'dp-next'
    
    if @props.calendar?
      if @props.calendar[year]?[month]?
        if _.findWhere(@props.calendar[year][month], {day: day})?
          classNames.push 'entry'
    
    if @props.lockWidgetToView
      if @state.selectedDate.isSame(props.date, 'day') and moment(@props.min_date, 'YYYY-MM-DD'+'T'+'HH:mm:ss').isSame(moment(@props.max_date, 'YYYY-MM-DD'+'T'+'HH:mm:ss').startOf('day'))
        classNames.push 'dp-selected'
    else
      if @state.selectedDate.isSame(props.date, 'day')
          classNames.push 'dp-selected'

    if @props.view_mode != 'day'
      classNames = _.without(classNames,'dp-value', 'dp-selected')

    props.className = classNames.join ' '
  
    return props

  datePickerProps: ()->
    datePickerProps
      onSelect: @onSelect
      onNav: @onNav
      onViewDateChange: @onViewDateChange

  render: ()->
    <div id="calendar-widget" className="widget frame">
      <DatePicker navPrev={<i className="icon icon-angle-left"></i>} navNext={<i className="icon icon-angle-right"></i>} onSelect={@onSelect} onNav={@onNav} view={@state.view} onViewChange={@onViewChange} onChange={@onChange} hideFooter={true} viewDate={@state.date} date={@state.selectedDate} weekDayNames={['S','M','T','W','T','F','S']} viewOrder = {['month','year','decade']} onRenderDay={@onRenderDay} monthFormat={'MMM'}/>     
      <div className="dp-footer">
        <div onClick={@onToday} className="dp-footer-today">Today</div>
      </div>
    </div>

CalendarWidgetContainer = Marty.createContainer CalendarWidget,
  listenTo: ['calendarStore', 'listStore']

  fetch: ()->
    journalId = @props.journalId
    tagId = @props.tagId
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      if journalId?
        viewId = 'Journal' + journalId
        options = {journal: journalId}
        return {  
          calendar: @props.app.calendarStore.getCalendarByJournal(journalId, viewId)
          lockWidgetToView: @props.app.calendarStore.getWidgetLock()
        }
      else if tagId?
        viewId = "Tag" + tagId
        options = {tag__is: tagId}
        return {
          calendar: @props.app.calendarStore.getCalendarByTag(tagId, viewId)
          lockWidgetToView: @props.app.calendarStore.getWidgetLock() 
        }
      else
        viewId: 'Timeline'
        options = {}
        return {  
          calendar: @props.app.calendarStore.getCalendar({},'Timeline')
          lockWidgetToView: @props.app.calendarStore.getWidgetLock() 
        }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <CalendarWidget {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <div></div>
  failed: (error)->
    console.log error
    return <div>Error</div>

module.exports = CalendarWidgetContainer