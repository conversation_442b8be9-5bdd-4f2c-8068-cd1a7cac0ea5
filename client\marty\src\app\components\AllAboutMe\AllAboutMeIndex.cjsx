React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Link = require('react-router').Link

Drawer = require '../Sidebar/Drawer'
AppHeader = require '../Header/AppHeader'

AllAboutMeSidebar = require './AllAboutMeSidebar'
AllAboutMeHeader = require './AllAboutMeHeader'
SectionIntro = require './AllAboutMeSectionIntro'
AnswerTile = require './AnswerTile'
LoadMore = require '../Timeline/LoadMore'


CategoryRow = React.createClass
  displayName: 'CategoryRow'

  getCategorySVG: ()->
    if @props.category?
      category_src = "/static/images/icons/icon-" + @props.category.name.split(" ").join('').toLowerCase() + ".svg"
      return <img src={category_src}/>
    else
      return undefined

  render: ()->
    <li>
      <Link to={"/all-about-me/category/" + @props.category.id}>
        <div className="item-icon-right">
          <span className="icon icon-angle-right"></span>
          <span className="sub-text">{@props.category?.answered_count}/{@props.category?.question_count}</span>
        </div>
        <div className="item-icon-left">
          {
            @getCategorySVG()
          }
        </div>
        <h2>{@props.category.name}</h2>
      </Link>
    </li>

AllAboutMeIndex = React.createClass
  displayName: 'AllAboutMe Index'

  renderNewButton: ()->
    if @props.random_question?.id?
      return <Link to={"/all-about-me/new/" + @props.random_question.id} query={aamId: @props.aamId} className="btn btn-new btn-navy pull-right">Answer a Random Question</Link>
    else
      return undefined

  render: ()->
    <div>
      <Drawer />
      <div id="page">
        <AppHeader view_title='All About Me' firstname={@props.user?.first_name || @props.user?.username} {...@props}/>
        <AllAboutMeHeader title='Categories' newButton={@renderNewButton()} />
        <div id="content">
          <div className="container">  
            <div id="category" className="frame">
            <ul className="list">
              {
                if @props.categories?
                  @props.categories.map (category)=>
                    if category.name not in ['Favorites','Other'] and category.question_count > 0
                      return <CategoryRow key={category.id} category={category} {...@props} />
                else
                  undefined
              }
            </ul>
            </div>  
          </div>
        </div>
      </div>
    </div>

AllAboutMeIndexContainer = Marty.createContainer AllAboutMeIndex,
  listenTo: ['allaboutmeStore', 'entryStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        user: @props.app.authStore.fetchUser()
        categories: @.props.app.allaboutmeStore.getCategories()
        random_question: @props.app.allaboutmeStore.getRandomQuestion()
        aamId: 1
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <AllAboutMeIndex {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <AllAboutMeIndex {...props} />
  failed: (error)->
    console.log error
    return <div>AllAboutMeIndex error</div>

module.exports = AllAboutMeIndexContainer