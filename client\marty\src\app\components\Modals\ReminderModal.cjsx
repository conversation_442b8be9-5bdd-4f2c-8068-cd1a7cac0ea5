React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'
Modal = require('react-bootstrap').Modal
FormData = require 'react-form-data'

ModalHeader = require './Headers/ModalHeader'
AuthenticatedComponent = require '../AuthenticatedComponent'

sanitizeHtml = require '../../utils/sanitizeHtml'

ReminderModal = React.createClass
  displayName: 'Reminder Modal'
  mixins: [FormData]

  getInitialState: ->
    showError: !!(@props.error)?
    error: @props.error
    showDays: false

  componentDidMount: ->
    @formData = {}
  
  componentWillReceiveProps: (nextProps) ->
    showError = undefined
    error = undefined
    showDays = @state.showDays
    
    if nextProps.error != undefined
      showError = true
      error = nextProps.error

    if not @props.show and nextProps.show
      @formData = {}

    if nextProps.reminder_profile? and not @props.reminder_profile?
      if nextProps.reminder_profile.frequency == 7
        showDays = true

    @setState
      showError: showError
      error: error
      showDays: showDays

  onHide: ()->
    if @props.onHide?
      @props.onHide()

  onDone: ()->
    if @formData.custom_message?
      sanitizedCustomMessage = sanitizeHtml @formData.custom_message
    else
      sanitizedCustomMessage = undefined

    reminderProfile = 
      enabled: @formData.enabled
      custom_message: sanitizedCustomMessage
      frequency: @formData.frequency
      day_of_the_week: @formData.day_of_week
      time: @getTime().utc().format("HH:mm:ss")
    
    keys = ['custom_message', 'day_of_the_week', 'enabled', 'frequency','time']
    reminderProfile = _.defaults reminderProfile, _.pick @props.reminder_profile, keys
    
    @props.app.reminderActionCreators.updateReminderProfile(reminderProfile)

  getTime: ()->
    
    hours = @formData.hours || @refs.hours.getDOMNode.value || @getHours()
    minutes = @formData.minutes || @refs.minutes.getDOMNode.value || @getMinutes()
    meridian = @formData.meridian || @refs.meridian.getDOMNode.value || @getMeridian()
    time = moment(hours + ":" + minutes + " " + meridian, "hh:mm a")
    console.log time.format()
    return time

  getHours: ()->
    if @props.reminder_profile?.time?
      return moment.utc(@props.reminder_profile.time, "HH:mm:ss").local().format('h')
  
  getMinutes: ()->
    if @props.reminder_profile?.time?
      return moment.utc(@props.reminder_profile.time, "HH:mm:ss").local().format('mm')
  
  getMeridian: ()->
    if @props.reminder_profile?.time?
      return moment.utc(@props.reminder_profile.time, "HH:mm:ss").local().format('a')


  onFrequencySelected: (e)->
    switch e.target.value
      when "7"
        @setState
          showDays: true
      else
        @setState
          showDays: false

  removeError: ()->
    @setState
      showError: false
      error: undefined

  
  formatFieldErrorMessage: (field, message)->
    return <div className="alert danger text-center">{message}</div>

  formatError: ()->
    return (
      <div className="col-sm-12">
        <div className="alert danger text-center">{@state.error?.user_message}</div>
      </div>
    )

  renderFieldError: (field)->
    if @state.showError
      if @state.error?
        if @state.error.validation_errors[field]?
          messages = @state.error.validation_errors[field]
          output = []
          for message in messages
            output.push @formatFieldErrorMessage(field, message)
          return output
    return undefined

  renderError: ()->
    if @state.showError
      if @state.error?
        return @formatError()
    return undefined

  renderDays: ()->
    if @state.showDays
      return (
        <div className="col-sm-12">
          <div className="form-group">
            <label>Choose Day</label>
            <label className="checkbox-inline">
              <input type="radio" name="day_of_week" value="sunday" defaultChecked={@props.reminder_profile?.day_of_the_week == 'sunday'} /> S
            </label>
            <label className="checkbox-inline">
              <input type="radio" name="day_of_week" value="monday" defaultChecked={@props.reminder_profile?.day_of_the_week == 'monday'} /> M
            </label>
            <label className="checkbox-inline">
              <input type="radio" name="day_of_week" value="tuesday" defaultChecked={@props.reminder_profile?.day_of_the_week == 'tuesday'} /> T
            </label>
            <label className="checkbox-inline">
              <input type="radio" name="day_of_week" value="wednesday" defaultChecked={@props.reminder_profile?.day_of_the_week == 'wednesday'} /> W
            </label>
            <label className="checkbox-inline">
              <input type="radio" name="day_of_week" value="thursday" defaultChecked={@props.reminder_profile?.day_of_the_week == 'thursday'} /> T
            </label>
            <label className="checkbox-inline">
              <input type="radio" name="day_of_week" value="friday" defaultChecked={@props.reminder_profile?.day_of_the_week == 'friday'} /> F
            </label>
            <label className="checkbox-inline">
              <input type="radio" name="day_of_week" value="saturday" defaultChecked={@props.reminder_profile?.day_of_the_week == 'saturday'} /> S
            </label>
          </div>
        </div>
      )
    else
      return undefined

  render: ()->
    <Modal show={@props.show} onHide={@onHide} backdrop={'static'} {...@props}>
      <ModalHeader header={'Reminder Settings'} onHide={@onHide} onDone={@onDone}/>
      <form className="modal-body padding-20" onChange={@updateFormData}>
        <div className="row">
          {
            @renderError()
          }
          <div className="col-sm-12">
            <div className="form-group">
              <label>Enable Reminders</label>
              <input name="enabled" type="checkbox" defaultChecked={@props.reminder_profile?.enabled} />
            </div>
          </div>
          <div className="col-sm-12">
            <div className="form-group">
              <label>Email Address</label>
              {
                @props.user?.email
              }
            </div>
          </div>
          <div className="col-sm-12">
            <div className="form-group">
              <label>Reminder Frequency</label>
              <select defaultValue={@props.reminder_profile?.frequency} name="frequency" className="form-control section-selection" onChange={@onFrequencySelected}>
                <option value=1>Every day</option>
                <option value=2>Every other day</option>
              </select>
            </div>
          </div>
          {
            @renderDays()
          }
          <div className="time col-sm-12">
            <div className="input-group form-group">
              <label>Reminder Time</label>
              <select ref="hours" defaultValue={@getHours()} name="hours" className="form-control">
                <option>1</option>
                <option>2</option>
                <option>3</option>
                <option>4</option>
                <option>5</option>
                <option>6</option>
                <option>7</option>
                <option>8</option>
                <option>9</option>
                <option>10</option>
                <option>11</option>
                <option>12</option>
              </select>
              <span>:</span>
              <select ref='minutes' defaultValue={@getMinutes()} name="minutes" className="form-control">
                <option>00</option>
                <option>01</option>
                <option>02</option>
                <option>03</option>
                <option>04</option>
                <option>05</option>
                <option>06</option>
                <option>07</option>
                <option>08</option>
                <option>09</option>
                <option>10</option>
                <option>11</option>
                <option>12</option>
                <option>13</option>
                <option>14</option>
                <option>15</option>
                <option>16</option>
                <option>17</option>
                <option>18</option>
                <option>19</option>
                <option>20</option>
                <option>21</option>
                <option>22</option>
                <option>23</option>
                <option>24</option>
                <option>25</option>
                <option>26</option>
                <option>27</option>
                <option>28</option>
                <option>29</option>
                <option>30</option>
                <option>31</option>
                <option>32</option>
                <option>33</option>
                <option>34</option>
                <option>35</option>
                <option>36</option>
                <option>37</option>
                <option>38</option>
                <option>39</option>
                <option>40</option>
                <option>41</option>
                <option>42</option>
                <option>43</option>
                <option>44</option>
                <option>45</option>
                <option>46</option>
                <option>47</option>
                <option>48</option>
                <option>49</option>
                <option>50</option>
                <option>51</option>
                <option>52</option>
                <option>53</option>
                <option>54</option>
                <option>55</option>
                <option>56</option>
                <option>57</option>
                <option>58</option>
                <option>59</option>
              </select>
              <select ref="meridian" defaultValue={@getMeridian()} name='meridian' className="form-control">
                <option value="am">am</option>
                <option value="pm">pm</option>
              </select>
            </div>
            </div>
          <div className="col-sm-12">
            <div className="row">
              <div className="col-sm-12">
                <div className="form-group">
                  <label>Custom message</label>
                  <textarea defaultValue={@props.reminder_profile?.custom_message} name="custom_message" className="form-control full-width"></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </Modal>

ReminderModalContainer = AuthenticatedComponent(Marty.createContainer ReminderModal,
  listenTo: ['reminderStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn()
      return {
        error: @props.app.reminderStore.getError()
        user: @props.app.authStore.getUser()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <ReminderModal {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <ReminderModal {...props} />
  failed: (error)->
    console.log error
    return <div>ReminderModal Error</div>
)

module.exports = ReminderModalContainer