React = require 'react'
_ = require 'underscore'
Marty = require 'marty'
moment = require 'moment'

DateRangePagerControl = React.createClass
  displayName: 'DateRangePagerControl'
  mixins: [Marty.createAppMixin()]

  # format('YYYY-MM-DD'+'T'+'00:00:00')
  onRightArrow: (args)->
    id = 'Timeline'
    if @props.journal?.id?
      id = 'Journal' + @props.journal.id

    if @props.tag?.id?
      id = 'Tag' + @props.tag.id

    filter_by = @.app.entryStore.getFilterBy()
    view_mode = @.app.listStore.getDateViewMode(id)
    
    date = moment(@props.min_date)
    min_date = @.app.calendarStore.getPageRange(date, view_mode, 'next', id)
    if min_date?
      options = 
        order_by: @props.order_by
        filter_by: filter_by
        min_date: moment(min_date).startOf(view_mode).format('YYYY-MM-DD'+'T'+'00:00:00')
        max_date: moment(min_date).endOf(view_mode).format('YYYY-MM-DD'+'T'+'23:59:59')
        view_mode: view_mode

      if @props.journal?.id?
        options['journal'] = @props.journal.id

      if @props.tag?.id?
        options['tags_id'] = @props.tag.id
        
      @.app.entryActionCreators.updateEntries(options, id)
      @.app.calendarActionCreators.lockWidget(options.min_date)
    else
      console.log "No later entries"

  onLeftArrow: (args)->
    id = 'Timeline'
    if @props.journal?.id?
      id = 'Journal' + @props.journal.id

    if @props.tag?.id?
      id = 'Tag' + @props.tag.id
    
    filter_by = @.app.entryStore.getFilterBy()
    view_mode = @.app.listStore.getDateViewMode(id)
      
    date = moment(@props.min_date)
    min_date = @.app.calendarStore.getPageRange(date,view_mode, 'prev', id)
    
    if min_date?
      options = 
        order_by: @props.order_by
        filter_by: filter_by
        min_date: moment(min_date).startOf(view_mode).format('YYYY-MM-DD'+'T'+'00:00:00')
        max_date: moment(min_date).endOf(view_mode).format('YYYY-MM-DD'+'T'+'23:59:59')
        view_mode: view_mode

      if @props.journal?.id?
        options['journal'] = @props.journal.id

      if @props.tag?.id?
        options['tags__id'] = @props.tag.id

      @.app.entryActionCreators.updateEntries(options, id)
      @.app.calendarActionCreators.lockWidget(options.min_date)
    else
      console.log "No earlier entries"
  
  render: ()->
    return (
        <div id="pager" className="pager-small fr">
          <a onClick={@onLeftArrow.bind null, {date: @props.min_date}} className="left btn btn-gray"><i className="icon icon-angle-left"></i></a>
          <a onClick={@onRightArrow.bind null, {date: @props.min_date}} className="right btn btn-gray"><i className="icon icon-angle-right"></i></a>
        </div>
      
    )

DateRangePageControlContainer = Marty.createContainer DateRangePagerControl,
  listenTo: ['calendarStore']

  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <DateRangePagerControl {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <DateRangePagerControl {...props} />
  failed: (error)->
    console.log error
    return <div>DateRangePagerControl Error</div>

module.exports = DateRangePageControlContainer