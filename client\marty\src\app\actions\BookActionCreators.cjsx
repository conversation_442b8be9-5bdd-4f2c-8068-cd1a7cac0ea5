React = require 'react'
Marty = require 'marty'
bookConstants = require '../constants/BookConstants'

BookActionCreators = Marty.createActionCreators
  id: 'BookActionCreators'

  createBook: (newBook)->
    return @.app.bookHttpAPI.createBook(newBook)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @.dispatch bookConstants.BOOK_CREATED, success
    .catch (error)=>
      console.log error
      @.dispatch bookConstants.BOOK_ERROR, error

  deleteBook: (book)->
    return @.app.bookHttpAPI.deleteBook(book)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)=>
      @.dispatch bookConstants.BOOK_DELETED, book
    .catch (error)=>
      console.log error
      @.dispatch bookConstants.BOOK_ERROR, error

  updateBook: (newBook, freezeBookBuilderStatus)->
    return @.app.bookHttpAPI.updateBook(newBook, freezeBookBuilderStatus)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @.dispatch bookConstants.BOOK_UPDATED, success
    .catch (error)=>
      console.log error
      @.dispatch bookConstants.BOOK_ERROR, error

  createShippingAddress: (newShippingAddress, shouldClose)->
    return @.app.bookHttpAPI.createShippingAddress(newShippingAddress)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @.dispatch bookConstants.SHIPPING_ADDRESS_CREATED, success, shouldClose
    .catch (error)=>
      console.log error
      @.dispatch bookConstants.SHIPPING_ADDRESS_ERROR, error   

  updateShippingAddress: (newShippingAddress, shouldClose)->
    return @.app.bookHttpAPI.updateShippingAddress(newShippingAddress)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @.dispatch bookConstants.SHIPPING_ADDRESS_UPDATED, success, shouldClose
    .catch (error)=>
      console.log error
      @.dispatch bookConstants.SHIPPING_ADDRESS_ERROR, error  

  deleteShippingAddress: (deletedBook)->
    return @.app.bookHttpAPI.deleteShippingAddress(deletedBook)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (success)=>
      @.dispatch bookConstants.SHIPPING_ADDRESS_DELETED, deletedBook
    .catch (error)=>
      console.log error
      @.dispatch bookConstants.SHIPPING_ADDRESS_ERROR, error  

  resetError: ()->
    @.dispatch bookConstants.RESET_BOOK_ERROR

  resetBookSuccess: ()->
    @dispatch bookConstants.RESET_BOOK_SUCCESS

  pollBook: (options)->
    return @.app.bookHttpAPI.pollBook(options)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      if success.meta?.total_count > 0
        @.dispatch bookConstants.BOOKS_RECEIVED, success.objects, success.meta
    .catch (error)=>
      console.log error
      @.dispatch bookConstants.SHIPPING_ADDRESS_ERROR, error

  resetShippingAddressError: ()->
    @.dispatch bookConstants.RESET_SHIPPING_ADDRESS_ERROR

  resetShippingAddressSuccess: ()->
    @dispatch bookConstants.RESET_SHIPPING_ADDRESS_SUCCESS

  addTimer: (timer)->
    @dispatch bookConstants.ADD_TIMER, timer

  removeTimer: (timer)->
    @dispatch bookConstants.REMOVE_TIMER, timer

  removeAllTimers: ()->
    @dispatch bookConstants.REMOVE_ALL_TIMERS

module.exports = BookActionCreators