#!/usr/bin/env python
"""
Test script to debug <PERSON><PERSON> algorithm validation for AMEX cards
"""

def luhn_check(card_number):
    """
    Implement the exact same <PERSON><PERSON> algorithm as in util.py line 118
    """
    try:
        num = [int(n) for n in card_number]
    except ValueError:
        return False, "Contains non-numeric characters"
    
    # This is the exact algorithm from util.py line 118:
    # if sum(num[::-2] + [sum(divmod(d * 2, 10)) for d in num[-2::-2]]) % 10:
    
    # Let's break it down step by step for debugging:
    print(f"Card number: {card_number}")
    print(f"Digits: {num}")
    
    # num[::-2] - every other digit starting from the end (right to left, odd positions)
    odd_positions = num[::-2]
    print(f"Odd positions (from right): {odd_positions}")
    
    # num[-2::-2] - every other digit starting from second-to-last (even positions)
    even_positions = num[-2::-2]
    print(f"Even positions (from right): {even_positions}")
    
    # Double the even position digits and sum the digits if > 9
    doubled_evens = [sum(divmod(d * 2, 10)) for d in even_positions]
    print(f"Doubled evens: {doubled_evens}")
    
    # Sum all digits
    total_sum = sum(odd_positions + doubled_evens)
    print(f"Total sum: {total_sum}")
    
    # Check if divisible by 10
    is_valid = total_sum % 10 == 0
    print(f"Valid (sum % 10 == 0): {is_valid}")
    
    return is_valid, f"Sum: {total_sum}, Remainder: {total_sum % 10}"

def test_known_cards():
    """Test with known valid and invalid cards"""
    print("=" * 60)
    print("TESTING KNOWN CARDS")
    print("=" * 60)
    
    test_cards = [
        # Known valid test cards
        ("****************", True, "Valid Visa test card"),
        ("****************", True, "Valid MasterCard test card"),
        ("***************", True, "Valid AMEX test card"),
        ("***************", True, "Valid AMEX test card"),
        ("***************", True, "Valid AMEX test card"),
        
        # Invalid cards (wrong check digit)
        ("****************", False, "Invalid Visa (wrong check digit)"),
        ("***************", False, "Invalid AMEX (wrong check digit)"),
        
        # Test some real-world AMEX patterns
        ("***************", None, "Random 34xx AMEX (unknown validity)"),
        ("***************", None, "Random 37xx AMEX (unknown validity)"),
    ]
    
    for card, expected, description in test_cards:
        print(f"\n{description}")
        print("-" * 40)
        is_valid, details = luhn_check(card)
        
        if expected is not None:
            status = "✓" if is_valid == expected else "✗"
            print(f"Expected: {expected}, Got: {is_valid} {status}")
        else:
            print(f"Result: {is_valid}")
        print(f"Details: {details}")

def test_amex_patterns():
    """Test various AMEX card patterns"""
    print("\n" + "=" * 60)
    print("TESTING AMEX PATTERNS")
    print("=" * 60)
    
    # Generate some AMEX cards with different check digits
    base_cards = [
        "34123456789012",  # 34xx base (14 digits)
        "37123456789012",  # 37xx base (14 digits)
    ]
    
    for base in base_cards:
        print(f"\nTesting variations of {base}X:")
        print("-" * 30)
        
        for check_digit in range(10):
            card = base + str(check_digit)
            is_valid, details = luhn_check(card)
            status = "✓ VALID" if is_valid else "✗ Invalid"
            print(f"{card}: {status}")

def analyze_user_card():
    """
    Analyze a specific card number that the user is having trouble with.
    Replace with the actual card number for debugging.
    """
    print("\n" + "=" * 60)
    print("ANALYZING USER'S CARD")
    print("=" * 60)
    
    # NOTE: Replace this with the actual card number the user is trying to use
    # For security, this should only be done in a secure environment
    user_card = "***************"  # Using a test card as example
    
    print("IMPORTANT: Replace 'user_card' variable with the actual card number")
    print("that the user is trying to use (only in a secure environment)")
    print()
    
    is_valid, details = luhn_check(user_card)
    
    if is_valid:
        print("✅ The card PASSES Luhn validation")
        print("The issue is likely elsewhere (regex pattern, CVV, expiration, etc.)")
    else:
        print("❌ The card FAILS Luhn validation")
        print("This is why the user gets 'Credit card number is not valid'")
        print("The card number has an incorrect check digit")
    
    print(f"Details: {details}")

def main():
    print("LUHN ALGORITHM DEBUGGING TOOL")
    print("This tool helps debug why specific AMEX cards fail validation")
    print()
    
    test_known_cards()
    test_amex_patterns()
    analyze_user_card()
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print("The Luhn algorithm validates that a credit card number is mathematically valid.")
    print("If a card fails this check, it means:")
    print("1. The card number was typed incorrectly")
    print("2. There's a typo in one of the digits")
    print("3. The card number is not a real/valid card number")
    print()
    print("To fix this issue:")
    print("1. Ask the user to double-check they entered the card number correctly")
    print("2. Try entering the card number again carefully")
    print("3. If the problem persists, the card number itself may be invalid")

if __name__ == '__main__':
    main()
