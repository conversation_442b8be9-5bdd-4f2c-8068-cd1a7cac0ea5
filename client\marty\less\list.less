/*---------------------------------------------------------------------------
  >Queries
---------------------------------------------------------------------------*/

.list {
	
	.list-item:last-child .list-item__row > div {
		border: none;
	}
}

#list {
	
	.list-item {
		display: table;
    table-layout: fixed;
    width: 100%;
    min-height: 50px;
    
    .list-closed {display: none;}    
    .action.collapsed .list-open {display: none;}
    .action.collapsed .list-closed {display: inline-block;}
    
    .icon-no, .icon-no-full {color: @red;}
    .icon-checkbox-full {color: @green;}
		
		.col {
	    padding: .8em 0;
			vertical-align: middle;
			
			&.text .icon {
		    float: right;
		    font-size: 16px;
		    color: @gray7;
		    margin: 0 8px;
	    }
		}
		
		.avatar {
	    text-align: center;
			width: 70px;
			
			img {width: 36px; height: 36px;}
			
			.icon {font-size: 25px;}
			
			/*
			input[type="checkbox"] {display: none;}
			
			&:hover {
				input[type="checkbox"] {
					display: inline-block;
					z-index: 1;
				}
				img {.hover-shrink;}
			}
			*/
		}

		.parent, a.text {
			color: @navy;
			cursor: pointer;
			
			div {
				.truncate;
				padding-right: 16px;
	    }
	    
	    &:hover {color: @teal;}
		}
		
		.text {
			font-weight: 600;
		  .truncate;
		    			
			.subtext {
		    font-weight: 400;
		    font-size: 12px;
		    color: @gray5;
		    .truncate;
			}
		}
				
		.icon-hover {display: none;}
		
		.action {
			
			&:hover {
				.icon-hover {color: @teal;}
			}
		}
		
		.action-primary {.table-cell;}
		
		.action-secondary {
	    vertical-align: middle;
	    text-align: center;
	    width: 40px;
	    .table-cell;
	    
	    .icon {
				font-size: 16px;
				color: @gray7;
				
				&:hover {color: @teal;}
	    }
		}
		
		.item-status {
			border-bottom: 1px solid @gray9;
	    vertical-align: middle;
	    text-align: center;
	    width: 40px;
	    .table-cell;
	    
	    .icon {font-size: 16px;}
		}
		
		&:hover {
			.icon-hover {display: block;}
			.icon-static {display: none;}
		}
		
		.item-wrapper {
	    display: table;
	    table-layout: fixed;
	    width: 100%;
	    min-height: 58px;

			.item-row {
				display: table-row;
				
				.col {
					.table-cell;
				}
			}
		}
		
		.entry-owner {
			.gradient-w-g;
			
			.avatar {
				border-bottom: 1px solid @gray9;
				border-radius: 0;	
			}
		}
		
		.delete-list-item {
			background-color: @red-light;
			
			.text {border: none;}
			
			.col.confirm {
				text-align: right;
				padding-right: 1em;
				
				.subtext {margin-top: 5px;}
				
				.btn {margin-left: 5px;}
			}
		}
	}
	
	.list-heading {
		color: @gray5;
    padding: .8em 1em .5em;
    font-weight: 600;
	}
	
	.text, .parent, .action-secondary {border-bottom: 1px solid @gray9;}
	
	> div:last-child {
		.text, .parent, .action-secondary, .item-status {border: none;}
	}
	
	&.list-avatar {
		.list-divide {margin-left: 70px;}
	}
	
	&.list-hierarchal {

		.list-divide {margin-left: 50px;}
		
		.parent {
	    font-size: 20px;
			font-weight: 400;
			.table-cell;
			.font2;
		}
		
		.children {
			margin-left: 35px;
			
			.list-divide-full {margin-left: 15px;}
		}
		
		.control {i {top: 1px;}}
		
		.control, .icon-left {
			width: 50px;
			text-align: center;
			.table-cell;
		}

		.icon-left {
			img {
				width: 22px;
				height: auto;
			}
		}
		
		.text {
			img {
				width: auto;
				height: 16px;
			}		
		}
		
		.icon-checkbox-full, icon-checkbox-full:hover {font-size: 20px; color: @green !important;}
		.icon-checkbox-full.disabled {opacity: .6;} 
	}
}