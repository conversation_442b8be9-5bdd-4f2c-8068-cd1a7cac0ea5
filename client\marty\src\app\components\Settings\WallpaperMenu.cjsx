React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

ButtonToolbar = require('react-bootstrap').ButtonToolbar
MenuItem = require('react-bootstrap').MenuItem
Dropdown = require('react-bootstrap').Dropdown
Button = require('react-bootstrap').Button

utils = require('react-bootstrap').utils.bootstrapUtils

utils.addStyle Dropdown.Toggle, ['gray']
utils.addStyle Button, ['gray']


WallpaperMenu = React.createClass
  displayName: 'Wallpaper Menu'
    
  getInitialState: ->
    open: false
  
  setDropdownState: (state, callback)->
    @setState
      open: state
    , callback

  onSelectWallpaper: (e)->
    e.stopPropagation()
    e.preventDefault()
    @setDropdownState false, ()=>
      if @props.onSelectWallpaper?
        @props.onSelectWallpaper()

  onRemoveWallpaper: (e)->
    e.stopPropagation()
    e.preventDefault()
    @setDropdownState false, ()=>
      if @props.onRemoveWallpaper?
        @props.onRemoveWallpaper()

  render: ()->
    <ButtonToolbar>
      <Dropdown id={'WallpaperMenu'}>
        <Dropdown.Toggle bsStyle={'gray'} bsSize={'small'} title={'Change Wallpaper'}>
        </Dropdown.Toggle>
        <Dropdown.Menu className="dropdown-default">
          <MenuItem onClick={@onSelectWallpaper}>Select Wallpaper</MenuItem>
          <MenuItem onClick={@onRemoveWallpaper}>Remove Current Wallpaper</MenuItem>
        </Dropdown.Menu>
      </Dropdown>
    </ButtonToolbar>

WallpaperMenuContainer = Marty.createContainer WallpaperMenu,
  listenTo: ['userStore']

  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <WallpaperMenu {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <WallpaperMenu {...props} />
  failed: (error)->
    console.log error
    return <div>Avatar Menu Error</div>

module.exports = WallpaperMenuContainer