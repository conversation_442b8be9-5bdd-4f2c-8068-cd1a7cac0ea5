React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
History = require('react-router').History
Link = require('react-router').Link


AllAboutMeHeaderControl = React.createClass
  displayName: 'AllAboutMeHeaderControl'
  mixins: [History]

  onBreadcrumbSelect: (categoryId)->
    @history.pushState null, '/all-about-me/category/' + categoryId

  formatBreadcrumbs: ()->
    breadCrumbs = [@props.question?.category?.name, 'Question']
    crumbs = []
    i = 0
    for crumb in breadCrumbs
      i++
      crumbs.push <i key={'angleright'+i} className="icon icon-angle-right"></i>
      if i == breadCrumbs.length
        crumbs.push <span key={'endcrumb'}>{crumb}</span>
      else
        crumbs.push <a key={'category'+@props.question?.category?.id} onClick={@onBreadcrumbSelect.bind null, @props.question?.category?.id}>{crumb}</a>
    return crumbs

  render: ()->
    <div id="date" className="pull-left">
      <Link to={'/all-about-me/'} className="icon crumb-icon icon-all-about-me"></Link>
      {
        @formatBreadcrumbs()
      }
    </div>


AllAboutMeHeaderControlContainer = Marty.createContainer AllAboutMeHeaderControl,
  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <AllAboutMeHeaderControl {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <AllAboutMeHeaderControl {...props} />
  failed: (error)->
    console.log error
    return <div>Error</div>


module.exports = AllAboutMeHeaderControlContainer