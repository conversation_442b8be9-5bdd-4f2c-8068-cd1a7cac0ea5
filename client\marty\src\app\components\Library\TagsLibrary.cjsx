React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Link = require('react-router').Link

Drawer = require '../Sidebar/Drawer'
AppHeader = require '../Header/AppHeader'
LibraryHeader = require './LibraryHeader'

OptionsList = require '../Modals/OptionsList/OptionsList'
EditModal = require '../Modals/EditTagModal'
DeleteModal = require '../Modals/DeleteTagModal'
CreateModal = require '../Modals/CreateModal'

TagForm = require '../Modals/Forms/TagForm'
LoadMore = require '../Timeline/LoadMore'

AuthenticatedComponent = require '../AuthenticatedComponent'

TagOption = React.createClass
  displayName: "Tag Option Item"

  propTypes: 
    option: React.PropTypes.object
    onOptionSelected: React.PropTypes.func

  getInitialState: ->
    showEditModal: false
    showDeleteModal: false
  
  openEditModal: ()->
    @setState
      showEditModal: true

  openDeleteModal: ()->
    @setState
      showDeleteModal: true

  closeEditModal: ()->
    @setState
      showEditModal: false

  closeDeleteModal: ()->
    @setState
      showDeleteModal: false

  onClick: (option)->
    @props.onOptionSelected(option)
    
  renderSelected: ()->
    if @props.isSelected?
      return (
        <div className="item-icon-right">
          <span className="icon icon-check pull-right"></span>
        </div>
      )
    else
      return undefined

  render: ()->
    entry_count = 0
    if @props.option.entry_count > 0 
      entry_count = @props.option.entry_count
    if @props.option.name?
      title = @props.option.name
    else
      title = @props.option.title

    <div className="item">
      <div className="item-icon-left">
        <span className="icon icon-tag"></span>
      </div>
      <Link to={"/tag/" + @props.option.id}>
        {title}
      </Link>
      <span className="sub-text">{entry_count} tagged</span>
      <div className="options">
        <EditModal type={'Tag'} object={@props.option} onHide={@closeEditModal} show={@state.showEditModal} {...@props}/>
        <a onClick={@openEditModal} className="icon icon-pencil"></a>
        <DeleteModal type={'Tag'} object={@props.option} onHide={@closeDeleteModal} show={@state.showDeleteModal} {...@props}/>
        <a onClick={@openDeleteModal} className="icon icon-times-circle"></a>
      </div>
    </div>


TagsLibrary = React.createClass
  displayName: "TagsLibrary"
  
  getInitialState: ->
    pager: {
      previous: @props.page?.previous
      next: @props.page?.next
      total_count: @props.page?.total_count
      offset: @props.page?.offset
      limit: @props.page?.limit
    }

  componentDidMount: ->
    @props.app.tagsQueries.getTags()
  

  componentWillReceiveProps: (nextProps) ->
    @setState
      pager: {
        previous: nextProps.page?.previous
        next: nextProps.page?.next
        total_count: nextProps.page?.total_count
        offset: nextProps.page?.offset
        limit: nextProps.page?.limit
      }
  
  onLoadMore: ()->
    if @state.pager.next?
      @.app.tagsQueries.getPage(@state.pager.next, 'Tags')

  renderPager: ()->
    if @state.pager?.next? and @props.tags?
      return (
        <div>
          <LoadMore pager={@state.pager} loaded={@props.tags.length} onClick={@onLoadMore}/>
        </div>
      )
    else
      return undefined

  onCreateTag: (tag)->
    @.app.tagActionCreators.createTag(tag)

  render: ()->
    <div>
      <Drawer />
      <div id="page">
        <AppHeader view_title="Library" {...@props} />
        <LibraryHeader title={'Tags'} buttonLabel='New Tag' createForm={TagForm} createAction={@onCreateTag}/>
        <div id="content">
          <div className="container">
            <div className="section-heading">     
              <h2><img src="/static/images/icon-tag.svg" /> Tags</h2>
            </div>
            <div id="subsection" className="frame">
              <OptionsList options={@props.tags} optionComponent={TagOption} optionsSortBy={'name'} isEditable={true}/>
              {
               @renderPager()
              }
            </div>
          </div>
        </div>
      </div>
    </div>

TagsLibraryContainer = AuthenticatedComponent(Marty.createContainer TagsLibrary,
  listenTo: ['tagsStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn()
      return {
        tags: @props.app.tagsStore.getTags('Tags')
        page: @props.app.pageStore.getPage('Tags')
        username: @props.app.authStore.getUsername()
        user: @props.app.authStore.fetchUser()
        firstname: @.props.app.authStore.getFirstNameOrUsername()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <TagsLibrary {...props} />
  pending: ()->
    return <div>Pending</div>
  failed: (error)->
    console.log error
    return <div>Tags Library Error</div>
)

module.exports = TagsLibraryContainer