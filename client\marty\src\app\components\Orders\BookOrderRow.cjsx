React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
moment = require 'moment'

BookOrderRow = React.createClass
  displayName: 'BookOrderRow'

  render: ()->
    return (
      <tr>
        <td className="col-name">
          <div className="title">{@props.order?.id}</div>
        </td>
        <td className="col-name">
          <div className={"title"}><a className={if _.isEmpty(@props.order?.downloadable_pdf) then "disabled"} target="_blank" href={@props.order?.downloadable_pdf}>{@props.order?.title}</a></div>
        </td>
        <td className="col-date">
          <div className="title">${@props.order?.total_amount}</div>
        </td>
        <td className="col-modified">
          <div className="title">{moment.utc(@props.order?.created, 'YYYY-MM-DDTHH:mm:ss').local().format('MMM, D YYYY')}</div>
        </td>
        <td className="col-modified">
          <div className="title">{@props.order?.processed_status}</div>
        </td>
        <td className="col-modified">
          <div className="title">{if @props.order?.processed_status == 'processed' then moment.utc(@props.order?.processed_date, 'YYYY-MM-DDTHH:mm:ss').local().format('MMM, DD YYYY') else undefined}</div>
        </td>
      </tr>
    )


BookOrderRowContainer = Marty.createContainer BookOrderRow,
  listenTo: ['bookOrderStore']

  fetch: ()->
    return {}
  done: (results) ->
    props = _.extend {}, @props, results
    return <BookOrderRow {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <BookOrderRow {...props} />
  failed: (error) ->
    console.log error
    return <div>BookOrderRow Error</div>

module.exports = BookOrderRowContainer