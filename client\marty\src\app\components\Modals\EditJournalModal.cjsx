React = require 'react'
Marty = require 'marty'
FormData = require 'react-form-data'

Modal = require('react-bootstrap').Modal
JournalEmail = require '../Journal/JournalEmail'

EditJournalModal = React.createClass
  displayName: 'Edit Journal Modal'

  getInitialState: ->
    title: @props.object.title
    showConfirmation: false
    showSuccess: false
  
  onDone: ()->
    newJournal = @props.object
    if @state.title?
      if @state.title != newJournal.title and @state.title != ""
        newJournal.title = @state.title
        @props.app.journalActionCreators.updateJournal(newJournal)
    @props.onHide()

  onChange: (e)->
    title = e.target.value
    @setState
      title: title

  showConfirmation: ()->
    @setState
      showConfirmation: true

  hideConfirmation: ()->
    @setState
      showConfirmation: false

  showSuccess: ()->
    @setState
      showSuccess: true

  hideSuccess: ()->
    @setState
      showSuccess: false

  generateNew: ()->
    @props.app.emailToJRNLActionCreators.generate({journal_id: @props.object.id})
    @setState
      showConfirmation: false
      showSuccess: true

  renderHeader: ()->
    if @state.showConfirmation
      <div className="modal-header teal-bg">
        <div className="row">
          <div className="col-sm-12 text-center">
            <div className="modal-title">Generate New Email</div>
          </div>
        </div>
      </div>
    else if @state.showSuccess
      <div className="modal-header teal-bg">
        <div className="row">
          <div className="col-sm-2">
            <a onClick={@props.onHide} className="btn-flat pull-left">
              <i className="icon icon-times"></i>
            </a>
          </div>
          <div className="col-sm-8 text-center">
            <div className="modal-title">Email Generated</div>
          </div>
        </div>
      </div>
    else
      <div className="modal-header teal-bg">
        <div className="row">
          <div className="col-sm-3">
            <a onClick={@props.onHide} className="btn-flat pull-left">
              <i className="icon icon-times"></i>
            </a>
          </div>
          <div className="col-sm-6 text-center">
            <div className="modal-title">Journal Settings</div>
          </div>
          <div className="col-sm-3">
            <a onClick={@onDone} className="btn-flat pull-right">
              <i className="icon icon-check-thin"></i>
            </a>
          </div>
        </div>
      </div>

  renderBody: ()->
    if @state.showConfirmation
      <div className="modal-body padding-20">
        <div className="confirm-dialogue text-center">
          <h2>Are you sure that you want to reset the email address for:</h2>
          <p><strong>{@props.object.title}</strong></p>
          <ul className="share-buttons">          
            <li>
              <a onClick={@hideConfirmation} className="TEMP-trigger-edit-journal-initial btn btn-teal btn-large share-2">No</a>
            </li>
            <li>
              <a onClick={@generateNew} className="TEMP-trigger-edit-journal-reset-confirm btn btn-navy btn-large">Yes</a>
            </li>
          </ul>
        </div>
      </div>
    else if @state.showSuccess
      <div className="modal-body padding-20">
        <div className="confirm-dialogue text-center">
          <h2>The email address for<br />
            <strong>{@props.object.title}</strong><br />
            has been regenerated and is now:</h2>
          <p><strong>{@props.object.incoming_email_address}</strong></p>
          <JournalEmail {...@props} generate={@showConfirmation} as_buttons={true} onHide={@props.onHide}/>
        </div>
      </div>
    else
      <div className="modal-body padding-20">
        <label>Journal Settings</label>
        <div className="form-group">
          <input name="title" className="form-control full-width" placeholder="Please enter a title" value={@state.title} onChange={@onChange} />
        </div>
        {
          if @props.object.journal_type != 'AllAboutMe'
            <div>
              <label>Journal Email Address</label>
              <p className="help-text">Any emails sent to this address will be posted as entries in this journal.</p>
              <JournalEmail {...@props} generate={@showConfirmation} onHide={@props.onHide}/>
            </div>
        }
      </div>

  render: ()->
    <Modal backdrop={'static'} onHide={@props.onHide} show={@props.show} id={"modal_edit_journal"} app={@props.app}>
      {
        @renderHeader()
      }
      {
        @renderBody()
      }
    </Modal>

module.exports = EditJournalModal