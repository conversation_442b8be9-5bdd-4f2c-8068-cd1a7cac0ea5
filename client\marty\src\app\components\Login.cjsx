React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
FormData = require 'react-form-data'
Link = require('react-router').Link

Login = React.createClass
  displayName: 'Login'
  mixins: [FormData, Marty.createAppMixin()]

  getInitialState: ->
    showError: !!@props.error?
    error: @props.error
    
  componentDidMount: ->
    if @props.isLoggedIn and @props.isActive
      @login()
  
  componentWillReceiveProps: (nextProps) ->
    if nextProps.error? != @state.error
      @setState
        showError: true
        error: nextProps.error

  login: ()->
    if @props.params.nextPath?
      @props.history.pushState null, nextPath
    else
      # console.log 'Auth pushstate'
      @props.history.pushState null, '/'
    return

  handleSubmit: (e)->
    e.stopPropagation()
    e.preventDefault()
    username = @formData.username || @refs.usernameInput?.value
    password = @formData.password || @refs.passwordInput?.value
    nextPath = @props.location.query.nextPath
    if username? and password? and not _.isEmpty(username) and not _.isEmpty(password)
        @setState
          showError: false
          error: undefined
        @.app.authActionCreators.authenticate(username, password, nextPath)
    else
      @setState
        showError: true
        error: {user_message: 'Enter username and password'}
    
  renderError: ()->
    if @state.showError
      if @state.error?
        return <div className="alert danger text-center">{@state.error.user_message}</div>
    else
      return undefined

  removeError: ()->
    @setState
      showError: false
      error: undefined

  onKeyDown: (e)->
    if e.keyCode == 13
      @handleSubmit(e)

  render: ()->
    <div>
      <div id='access'>
        <div className='container'>      
          <form onChange={@updateFormData} autoComplete={"off"} onSubmit={@handleSubmit} >
            <div className="panel frame">
              <div className="panel-body">
                <Link to={"/"} className="jrnl-logo"><img src="/static/images/jrnl-logo.png"/></Link>
                <div className="">
                  <div id="login">
                    <div className="form-wrapper">          
                      {
                        @renderError()
                      }
                      <div className="form-group">
                        <input ref="usernameInput" autoComplete="off" name='username' id='username' type='text' className='form-control' placeholder="Username" onChange={@removeError} onKeyDown={@onKeyDown}/>
                        <input ref="passwordInput" autoComplete="off" name='password' id='password' type='password' className='form-control' placeholder="Password" onChange={@removeError} onKeyDown={@onKeyDown}/>
                        <a onClick={@handleSubmit} className="btn btn-navy btn-medium full-width">Login</a>
                      </div>
                    </div>
                    <div className="access-links">
                      <div className="left">
                        <Link to={"/forgot-password/"}>Forgot Password?</Link>
                      </div>
                      <div className="right">
                        <Link to={"/signup/"}>Signup Here!</Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

LoginContainer = Marty.createContainer Login,
  listenTo: ['authStore']

  fetch: ()->
    return {
      isLoggedIn: @.app.authStore.isLoggedIn()
      isActive: @.app.authStore.isActive()
      error: @.app.authStore.getError()
    }
  done: (results)->
    props = _.extend {}, @props, results
    return <Login {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Login {...props}/>
  failed: (error)->
    console.log error
    return <div>Error</div>

            
module.exports = LoginContainer