React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

Link = require('react-router').Link

Drawer = React.createClass
  displayName: 'Sidebar Drawer'

  render: ()->
    <div id="drawer" className="pull-left">
      <Link to={"/"} className="logo">
        <img className="hidden-md hidden-lg" styles={width: "24px", height:"auto"} src="/static/images/jrnl-icon.png" />
        <img className="hidden-sm" src="/static/images/jrnl-logo.png" />
      </Link>
      <ul>
        <li>
          <Link to={"/search/"} activeClassName="active">
            <img src="/static/images/icons-interface/icon-search.svg" /><span className="hidden-sm">Search</span>
          </Link>
        </li>
        <li>
          <Link to={"/"} activeClassName="active">
            <img src="/static/images/icons-interface/icon-timeline.svg" /><span className="hidden-sm">Timeline</span>
          </Link>
        </li>
        <li>
          <Link to={"/library/"} activeClassName="active">
            <img src="/static/images/icons-interface/icon-library.svg" /><span className="hidden-sm">Library</span>
          </Link>
        </li>
        <li>
          <Link to={'/all-about-me/'} activeClassName="active">
            <img src="/static/images/icons-interface/icon-all-about-me.svg" /><span className="hidden-sm">All About Me</span>
          </Link>
        </li>
        <li>
          <Link to={'/bookbuilder/'} activeClassName="active">
            <img src="/static/images/icons-interface/icon-bookbuilder.svg" /><span className="hidden-sm">Bookbuilder</span>
          </Link>
        </li>
        <li>
          <a href="http://helpdesk.jrnl.com/" target="_blank">
            <img src="/static/images/icons-interface/icon-feedback.svg" /><span className="hidden-sm">Feedback</span>
          </a>
        </li>
      </ul>
      <div className="jrnl-version">JRNL v.3.2.6</div>
    </div>

DrawerContainer = Marty.createContainer Drawer,
  
  fetch: ()->
    if @app.authStore.isLoggedIn() and @app.authStore.isActive()
      return {
        username: @app.authStore.getFirstNameOrUsername()
        user: @app.authStore.fetchUser()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Drawer {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Drawer {...props} />
  failed: (error)->
    console.log error
    return <div>Drawer Error </div>


module.exports = DrawerContainer
