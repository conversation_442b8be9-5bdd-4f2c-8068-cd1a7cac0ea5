React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Modal = require('react-bootstrap').Modal

QuotaWidget = require '../../Sidebar/Widgets/QuotaWidget'
Dropzone = require './Dropzone'

WallpaperGallery = require './WallpaperGallery'

MediaManagerModal = React.createClass
  displayName: 'MediaManager Modal'

  getInitialState: ->
    files: []
    processing: true

  onHide: ()->
    if @props.onHide?
      @setState
        files: []
      @props.onHide()

  onDone: ()->
    if @props.onDone?
      files = @state.files
      @props.onDone(files)
      @setState
        files: []
      
  onWallpaperSelected: (wallpaper)->
    if @props.onWallpaperSelected
      @props.onWallpaperSelected(wallpaper)

  fileUploadComplete: (dzFile)->
    if dzFile?.accepted and dzFile?.status != 'error'
      @app.authQueries.getUser()
      response = JSON.parse(dzFile.xhr.responseText)
      console.log JSON.parse(dzFile.xhr.responseText)
      if _.isArray response
        file = response[0]
      else
        file = response
      files = @state.files
      files.push file
      @setState
        files: files
        processing: false       
    else
      @setState
        processing: false
        
  fileRemoved: (dzFile)->
    # if dzFile?
    #   console.log dzFile
    #   console.log @state.files
    return

  fileUploadProcessing: (dzFile)->
    @setState
      processing: true

  fileUploadError: (error)->
    @setState
      processing: false

  renderDone: ()->
    if not @state.processing
      return <a onClick={@onDone} className="btn-flat pull-right"><i className="icon icon-check"></i></a>
    else 
      return undefined

  renderHeader: ()->
    switch @props.mediaType
      when 'wallpaper'
        return <div className="modal-title">{if @props.mediaType? then "#{@props.mediaType}" else 'Media'} Select </div>
      else
        return <div className="modal-title">{if @props.mediaType? then "#{@props.mediaType}" else 'Media'} Upload </div>

  renderInteraction: ()->
    switch @props.mediaType
      when 'wallpaper'
        return (
          <WallpaperGallery {...@props} onWallpaperSelected={@onWallpaperSelected} />
        )
      else
        return (
          <div>
            <div className="sidebar">
              <QuotaWidget {...@props} />
            </div>
            <div className="content">
              <Dropzone onRemove={@fileRemoved} onComplete={@fileUploadComplete} onProcessing={@fileUploadProcessing} onError={@fileUploadError} {...@props} />
            </div>
          </div>
        )

  render: ()->
    <Modal enforceFocus={false} backdrop={'static'} className="media-manager-1" {...@props} id="modal_media_manager">
      <div className="modal-header teal-bg">
        <div className="row">
          <div className="col-sm-3">
            <a onClick={@onHide} className="btn-flat pull-left"><i className="icon icon-times"></i></a>
          </div>
          <div className="col-sm-6 text-center">
            {
              @renderHeader()
            }
          </div>
          <div className="col-sm-3">
            {
              @renderDone()
            }        
          </div>
        </div>
      </div>
      <div className="modal-body">
        {
          @renderInteraction()
        }
      </div>  
    </Modal>

MediaManagerModalContainer = Marty.createContainer MediaManagerModal,
  # listenTo: ['']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        user: @props.app.authStore.fetchUser()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <MediaManagerModal {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <MediaManagerModal {...props} />
  failed: (error)->
    console.log error
    return <div>Media Manager Modal Error</div>

module.exports = MediaManagerModalContainer

