React = require 'react'
Marty = require 'marty'
imageConstants = require '../constants/ImageConstants'

ImageActionCreators = Marty.createActionCreators
  id: 'ImageActionCreators'

  uploadCoverImage: (file)->
    return @.app.imageHttpAPI.uploadCoverImage(file)
    .then (response)=>
      @.dispatch imageConstants.COVER_IMAGE_CREATED, response
    .catch (error)=>
      console.log error
      @.dispatch imageConstants.IMAGE_ERROR, error


module.exports = ImageActionCreators
