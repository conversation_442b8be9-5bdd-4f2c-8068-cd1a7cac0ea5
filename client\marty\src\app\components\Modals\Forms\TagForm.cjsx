React = require 'react'
FormData = require 'react-form-data'

TagForm = React.createClass
  displayName: 'TagForm'
  mixins:[FormData]

  onChange: ()->
    return true

  render: ()->
    if @props.showSave
      header = undefined
    else
      header = <li className="sub-heading">Tag Name</li>

    <ul>
      {
        header
      }
      <li>
        <div className="form-group title" onChange={@.updateFormData}>
          <input type="text" className="full-input form-control full-width" id="name" name="name" placeholder="Please enter a tag title" value={@.formData.name} onChange={@onChange}/>
        </div>
      </li>
    </ul>

module.exports = TagForm