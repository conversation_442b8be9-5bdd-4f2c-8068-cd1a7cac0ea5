React = require 'react'
Link = require('react-router').Link
Sticky = require 'react-sticky'

AllAboutMeHeaderControl = require './AllAboutMeHeaderControl'

QuestionListHeader = React.createClass
  displayName: 'QuestionListHeader'
  STICKY_STYLES: {}

  renderNewButton: ()->
    if @props.random_question?.id?
      return <Link to={"/all-about-me/new/" + @props.random_question.id} query={aamId: 1} className="btn btn-new btn-navy pull-right">Answer a Random Question</Link>
    else
      return undefined
      
  render: ()->
    <Sticky stickyStyle={@STICKY_STYLES} stickyClass={'nav-sticky'} topOffset={-60}>
      <div id="nav_crumb" className="gray">
        <div className="container">
          <div id="crumb-bar" className="pull-left">
          <AllAboutMeHeaderControl {...@props}/>
          </div>
          <div id="tools" className="pull-right text-right">
            {
              @renderNewButton()
            }
          </div>
        </div>
      </div>
    </Sticky>

module.exports = QuestionListHeader