React = require 'react'
Marty = require 'marty'
Link = require('react-router').Link
CreateModal = require '../Modals/CreateModal'
Sticky = require 'react-sticky'

LibraryHeader = React.createClass
  displayName: 'Library Header'
  STICKY_STYLES: {}

  propTypes:
    title: React.PropTypes.string

  getInitialState: ->
    showCreateModal: false
  
  renderNewModal: ()->
    return @props.newModal
  
  renderNewButton: ()->
    return @props.newButton

  openCreateModal: ()->
    @setState
      showCreateModal: true

  closeCreateModal: ()->
    @setState
      showCreateModal: false

  render: ()->
    <Sticky stickyStyle={@STICKY_STYLES} stickyClass={'nav-sticky'} topOffset={-60}>
      <div id="nav_crumb" className="gray">
        <div className="container">
          <div id="crumb-bar" className="pull-left">
            <div id="date" className="pull-left">
              <Link to={"/library/"} className="icon crumb-icon icon-library"></Link>
              <i className="icon icon-angle-right"></i><span>{@props.title}</span>
            </div>
            <div id="options" className="pull-right">
            </div>
          </div>
          <div id="tools" className="pull-right text-right">
            <CreateModal onHide={@closeCreateModal} show={@state.showCreateModal} createAction={@props.createAction} form={@props.createForm} title={@props.buttonLabel} {...@props} />
            <a onClick={@openCreateModal} className="btn btn-new pull-right btn-navy">{@props.buttonLabel}</a>
          </div>
        </div>
      </div>
    </Sticky>

module.exports = LibraryHeader