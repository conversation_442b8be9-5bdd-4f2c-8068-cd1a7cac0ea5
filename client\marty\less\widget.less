/*---------------------------------------------------------------------------
  >Widget
---------------------------------------------------------------------------*/

.widget {
	margin: 0 0 20px;
	.full-width;
	.fl;
	
	.widget-title {
		padding: 9px 12px;
		.border-btm;
		
		.icon {
		  .gray3-txt;
		  top: 5px;
		  right: -5px;
		  .fr;
		  
		  &:hover {.teal-txt;}
		}
		
		h3 {
			margin: 0;
			.font2;
			.w500;
			.f18;
			.navy-txt; 
			
			img {
				top: -2px;
				width: 24px;
			}
		}
	}

	.widget-body {
		padding: 15px 20px 20px;
	  border-radius: 0 0 3px 3px;
		.gradient-g-w;
	
		p {margin: 0 0 15px;}
		
		.icon-refresh {
		  position: absolute;
		  right: 15px;
		  bottom: 24px;
		  .f20;
		  .gray4-txt;
		  
		  &:hover {.teal-txt;}
		}
		
		select.form-control {
			font-size: 12px;
			padding: 3px 4px;
			height: 27px;
		}
	}

	.list {
		border-radius: 0 0 3px 3px;
		
		a {padding-right: 20px;}	
	}		
}

/*---------------------------------------------------------------------------
  >Queries
---------------------------------------------------------------------------*/


/* SM */ @media screen and (max-width: 991px) {
	
	.widget-body p {.f14;}
	
}
/* MD */ @media screen and (max-width: 1199px) and (min-width: 992px) {
	
	.widget-body p {.f16;}
	
}
/* LG + */ @media screen and (min-width: 1200px) {
	
	.widget-body p {.f18;}
	
}