/*---------------------------------------------------------------------------
  >Timeline
---------------------------------------------------------------------------*/

#timeline-column {

	@media @sm-x {padding-bottom: 5px;}
	@media @sm-x {padding-bottom: 40px;}
	
	.btn-list-new, #load_more .btn {
		border: none;
    .shadow;
	}
	
	.btn-list-new {
		
		@media @sm {margin-bottom: 10px;}
		@media @md-x {margin-bottom: 15px;}
	}
	
	#load_more .btn {
    margin-bottom: 1em;
	}

	#date-separator {
		width: 100%;
		
		#date {
			padding: 10px 15px;
			.navy-txt;
			.white-bg;
			.radius3;
			.shadow;
			
			@media @md-x {	font-size: 17px;}
		}
		
		a {
			margin-left: 8px;
			display: inline-block;
			width: 14px;
			text-align: center;
		}
		
		&.padding-top {
			
			@media @sm {padding: 0 0 10px;}
			@media @md-x {padding: 0 0 15px;}
		}
	}
		
	#date-separator, #date-separator a {
		.font2;
		.w400;
	}
	
	.btn-empty-timeline {
	  width: 100%;
	  padding: 10px 50px;
	  .f20;
	}
	
	.empty-timeline {height: 355px;}
	
}