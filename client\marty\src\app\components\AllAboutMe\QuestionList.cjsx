React = require 'react'
Marty = require 'marty'
moment = require 'moment'
_ = require 'underscore'

# TODO npm package for global namespace access
AuthenticatedComponent = require '../AuthenticatedComponent'

Drawer = require '../Sidebar/Drawer'
AllAboutMeSidebar = require './AllAboutMeSidebar'
AppHeader = require '../Header/AppHeader'
QuestionListHeader = require './QuestionListHeader'

EntryTile = require '../Entry/EntryTile'
AnswerTile = require '../AllAboutMe/AnswerTile'
EmptyTile = require '../Timeline/EmptyTile'

LoadMore = require '../Timeline/LoadMore'
WelcomeModal = require '../Modals/WelcomeModal'

QuestionTile = require './QuestionTile'

QuestionList = React.createClass
  displayName: 'Questions List'

  getInitialState: ->
    isEmpty: false
    pager: {
      previous: @props.page?.previous
      next: @props.page?.next
      total_count: @props.page?.total_count
      offset: @props.page?.offset
      limit: @props.page?.limit
    }
    showWelcomeModal: false

  componentDidMount: ()->
    if @state.pager?
      if parseInt(@state.pager.total_count) == 0
        @setState
          isEmpty: true

  componentWillReceiveProps: (nextProps) ->
    isEmptyForReal = false
    if @props.entries? and nextProps.entries?
      if !_.isEmpty(@props.entries) and !_.isEmpty(nextProps.entries)
        if @props.entries[0].id == "EMPTY" and nextProps.entries[0].id == "EMPTY"
          isEmptyForReal = true
        else
          isEmptyForReal = false
      else
        isEmptyForReal = false

    if nextProps.page?
      if parseInt(nextProps.page.total_count) == 0
        isEmptyForReal = true

    if nextProps.user?.has_seen_welcome_web?
      if nextProps.user.has_seen_welcome_web
        @setState
          showWelcomeModal: false
      else
        @setState
          showWelcomeModal: true
    
    @setState
      isEmpty: isEmptyForReal
      pager: {
        previous: nextProps.page?.previous
        next: nextProps.page?.next
        total_count: nextProps.page?.total_count
        offset: nextProps.page?.offset
        limit: nextProps.page?.limit
      }

  onLoadMore: ()->
    if @state.pager.next?
      @.app.entriesQueries.getPage(@state.pager.next, 'Question' + @props.questionId)

  openWelcomeModal: ()->
    @setState
      showWelcomeModal: true

  closeWelcomeModal: ()->
    @app.userActionCreators.updateWelcomeStatus(true)
    @setState
      showWelcomeModal: false

  renderEmpty: ()->
    if @state.isEmpty
      return (
        <EmptyTile {...@props} />
      )
    else
      return undefined

  renderPager: ()->
    if @state.pager?.next? and @props.entries?
      return (
        <div>
          <LoadMore pager={@state.pager} loaded={@props.entries.length} onClick={@onLoadMore}/>
        </div>
      )
    else
      return undefined

  renderEntriesOrEmpty: (entries)->
    if entries?
      addButton = false
      output = entries.map (entry)=>               
        switch entry.entry_type
          when "QA"
            addButton = true
            return (
              <AnswerTile {...@props} key={entry.id} answer={entry} />
            )
          when "Default"
            addButton = true
            return (
              <EntryTile {...@props} key={entry.id} entry={entry} />
            )
          when ""
            addButton = true
            return (
              <EntryTile {...@props} key={entry.id} entry={entry} />
            )
          when "EMPTY"
            return @renderEmpty()
      
      output.unshift <QuestionTile key={'questiontile1'} {...@props}/>
      
      return output
    else
      return <QuestionTile key={'questiontile0'} {...@props}/>

  render: ()->
    entries = @props.entries  
    <div>
      <WelcomeModal show={@state.showWelcomeModal} onHide=(@closeWelcomeModal) {...@props} />
      <Drawer />
      <div id="page">
        <AppHeader view_title={'All About Me'} {...@props}/>
        <QuestionListHeader onViewModeChange={@onViewModeChange} widgetCallback={@onWidgetCallback} {...@props}/>
        <div id="content">
          <div className="container">
            <div id="timeline-column" className="pull-left right-padding">
              {
                @renderEntriesOrEmpty(entries)
              }  
              {
                @renderPager()
              }
            </div>
            <AllAboutMeSidebar widgetCallback={@onWidgetCallback} {...@props}/>
          </div>
        </div>
      </div>
    </div>

QuestionListContainer = Marty.createContainer QuestionList,
  listenTo: ['allaboutmeStore', 'entryStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        user: @.app.authStore.fetchUser()
        question: @.app.allaboutmeStore.getQuestionById(@props.params.questionId)
        entries: @.app.entryStore.getAnswersByQuestion(@props.params.questionId, 'Question' + @props.params.questionId)
        random_question: @props.app.allaboutmeStore.getRandomQuestion()
        aamId: 1
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <QuestionList {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <QuestionList {...props} />
  failed: (error)->
    console.log error
    return <div>QuestionList Error</div>

module.exports = QuestionListContainer