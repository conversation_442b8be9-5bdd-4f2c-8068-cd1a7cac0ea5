React = require 'react'
Marty = require 'marty'

CalendarWidget = require './Widgets/CalendarWidget'

Sidebar = React.createClass
  displayName: 'Sidebar'

  onWidgetCallback: (widget, value)->
    if @props.widgetCallback?
      @props.widgetCallback(widget, value)

  render: ()->
    <div id="default-sidebar" className="pull-right">
      <CalendarWidget widgetCallback={@onWidgetCallback} {...@props} />
    </div>

module.exports = Sidebar