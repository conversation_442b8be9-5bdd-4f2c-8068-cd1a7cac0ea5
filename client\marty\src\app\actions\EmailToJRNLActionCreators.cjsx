Marty = require 'marty'
emailToJRNLConstants = require '../constants/EmailToJRNLConstants'

EmailToJRNLActionCreators = Marty.createActionCreators
  id: 'EmailToJRNLActionCreators'

  generate: (options)->
    @.app.emailToJRNLHttpAPI.generate(options)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @.dispatch emailToJRNLConstants.EMAIL_TO_JRNL_GENERATED, success, options
    .catch (error)=>
      console.log error
      @.dispatch emailToJRNLConstants.EMAIL_TO_JRNL_ERROR, error

  download_vcard: (journal_id)->
    @.app.emailToJRNLHttpAPI.download_vcard(journal_id)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.blob()
    .then (success)=>
      windowUrl = window.URL || window.webkitURL
      url = windowUrl.createObjectURL(success)
      @dispatch emailToJRNLConstants.DOWNLOAD_VCARD_SUCCESS, url, journal_id
    .catch (error)=>
      console.log error
      @dispatch emailToJRNLConstants.EMAIL_TO_JRNL_ERROR, error

  send_vcard_email: (journal_id)->
    @.app.emailToJRNLHttpAPI.send_vcard_email(journal_id)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @dispatch emailToJRNLConstants.SEND_VCARD_EMAIL, success
    .catch (error)=>
      console.log error
      @dispatch emailToJRNLConstants.EMAIL_TO_JRNL_ERROR, error    

  reset_vcard: (journal_id)->
    @dispatch emailToJRNLConstants.RESET_VCARD, journal_id

module.exports = EmailToJRNLActionCreators