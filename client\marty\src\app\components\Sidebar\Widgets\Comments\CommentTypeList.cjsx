React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
classnames = require 'classnames'

CommentTypeItem = require './CommentTypeItem'
InvitationModal = require '../../../Modals/Invitation/InvitationModal'

TypeHeader = React.createClass
  displayName: 'TypeHeader'

  render: ()->
    if @props.type is 'one_on_one'
      type = '1on1'
    else
      type = @props.type
    iconClassnames = classnames('icon',"icon-share-#{type}")
    <div className="header">
      <i className={iconClassnames} />
      {
        @props.label
      }
      {
        if @props.onCreate?
          <a onClick={@props.onCreate}>
            <i className="icon-plus"></i>
          </a>
      }
    </div>

CommentTypeGroup = React.createClass
  displayName: 'Comment Type'
  
  render: ()->
    if @props.type is 'one_on_one'
      type = '1on1'
    else
      type = @props.type
    classNames = classnames('comment-cat', "comment-cat-#{type}")
    <div className={classNames}>
      {
        React.Children.map @props.children, (child)=>
          React.cloneElement child, @props
      }
    </div>

CommentTypeList = React.createClass
  displayName: 'CommentTypeList'

  getInitialState: ->
    showShareModal: false
    invitationType: undefined
      
  openShareModal: (type)->
    console.log 'openShareModal'
    @setState
      showShareModal: true
      invitationType: type

  closeShareModal: (type)->
    console.log 'closeShareModal'
    @setState
      showShareModal: false
      invitationType: type

  onHide: ()->
    if @props.onHide?
      @props.onHide()

  showPersonal: ()->
    if @props.onTypeChange?
      @props.onTypeChange('personal')

  showGroup: ()->
    if @props.onTypeChange?
      @props.onTypeChange('group')

  showOneOnOne: (channelUser)->
    if @props.onTypeChange?
      @props.onTypeChange('one_on_one', channelUser)

  renderInvitationModal: ()->
    <InvitationModal app={@props.app} entry={@props.entry} user={@props.user} onHide={@closeShareModal} show={@state.showShareModal} initialHeader={'Share Entry'} type={@state.invitationType}/>

  render: ()->
    <div>
      {
        if @state.showShareModal
          @renderInvitationModal()
      }
      <div id="drawer-comments-header" className="comments-header-list">
        <div className="title-row header-row">
          <div className="col col-title">
            <div className="title">Comments and Sharing</div>
          </div>
          <div className="col col-close">
            <a onClick={@onHide} className="btn-close"><i className="icon-times"></i></a>
          </div>                                    
        </div>
      </div> 
      <div id="drawer_comments" className="comments comments-list">
        {
          if @props.personalChannel?
            <TypeHeader label=" Personal" type={"personal"} onCreate={undefined}/>
        }
        {
          if @props.personalChannel?
            <CommentTypeGroup type='personal' onItemClick={@showPersonal}> 
              <CommentTypeItem {...@props}  count={@props.personalChannel.comments?.length} comments={@props.personalChannel.comments}/>
            </CommentTypeGroup>
        }
        {
          if @props.groupChannel?.members? and @props.groupChannel?.members?.length > 0
            <TypeHeader label=" Group Sharing" onCreate={if @props.entry?.isOwnedByMe then @openShareModal.bind(null, 'group') else undefined} type={"group"}/>        
        }
        { 
          if @props.groupChannel?.members? and @props.groupChannel?.members?.length > 0
            <CommentTypeGroup type='group'>
              <CommentTypeItem {...@props} groupMemberCount={@props.groupChannel?.members?.length} onItemClick={@showGroup} comments={@props.groupChannel.comments} />
            </CommentTypeGroup>
        }
        {
          if not _.isEmpty @props.oneOnOneChannel?.channels
            <TypeHeader label=" 1 on 1 Sharing" onCreate={if @props.entry?.isOwnedByMe then @openShareModal.bind(null, 'one_on_one') else undefined} type={"one_on_one"}/>
        }
        {
          if not _.isEmpty @props.oneOnOneChannel?.channels
            <CommentTypeGroup type='one_on_one'>
              {
                content = []
                _.mapObject @props.oneOnOneChannel.channels, (channelObject, channel)=>

                  content.push <CommentTypeItem {...@props} key={"#{channel}-one-on-one"} channelUser={channel} onItemClick={@showOneOnOne} comments={channelObject.comments}/>    
                content
              }
            </CommentTypeGroup>
        }
      </div>
    </div>

CommentTypeListContainer = Marty.createContainer CommentTypeList,
  listenTo: ['invitationStore']

  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <CommentTypeList {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <CommentTypeList {...props} />
  failed: (error)->
    console.log error
    return <div>CommentTypeList Error</div>


module.exports = CommentTypeListContainer