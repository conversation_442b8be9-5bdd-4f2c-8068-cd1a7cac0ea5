React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
History = require('react-router').History

BookShippingCalculator = require './BookShippingCalculator'
PdfViewer = require 'react-pdf'
PDFJS = require 'pdfjs'

Lifecycle = require('react-router').Lifecycle

BookPreview = React.createClass
  displayName: 'BookPreview'
  mixins: [History, Lifecycle]
  SUCCESS_BOOK_BUILDER_STATUS: [100, 99, 98]
  
  getInitialState: ->
    pdfComplete: undefined
    currentPage: 1
    showError: !!@props.error?
    error: @props.error
    saved: true

  componentWillReceiveProps: (nextProps) ->
    if nextProps.error != @state.error
      @setState
        showError: true
        error: nextProps.error

    if nextProps.saved?
      @setState
        saved: nextProps.saved

  routerWillLeave: ()->
    if !@state.saved
      return "Discard unsaved changes?"
  
  updateBook: (e)->
    if @props.updateBook?
      @props.updateBook(e)

  canProceed: ()->
    if @props.book.book_builder_status in @SUCCESS_BOOK_BUILDER_STATUS and @props.book.shipping_address != null and @props.book.approved
      return true
    else
      return false

  onPrevious: ()->
    @history.pushState null, '/book/cover/' + @props.params.bookId

  onNext: ()->
    if @props.book.book_builder_status in @SUCCESS_BOOK_BUILDER_STATUS and @props.book.shipping_address != null and @props.book.approved
      if @props.onSave?
        @props.onSave(true, '/book/order/')

  onDocumentCompleted: (pages)->
    if @isMounted()
      @setState
        pdfPages: pages

  onPageCompleted: (page)->
    @setState
      currentPage: page

  onPreviousPage: ()->
    if @state.currentPage > 1
      @setState
        currentPage: @state.currentPage-1 

  onNextPage: ()->
    if @state.currentPage < @state.pdfPages
      @setState
        currentPage: @state.currentPage+1 

  renderPageCount: ()->
    if @props.book?
      return <li><span className="icon icon-check"></span> {@props.book.page_count} Pages</li> 

  renderAllAboutMeSelection: ()->
    if @props.book?
      switch @props.book.all_about_me
        when "MR"
          return <li><span className="icon icon-check"></span> Most recent All About Me questions</li> 
        when "ALL"
          return <li><span className="icon icon-check"></span> All All About Me questions</li>
        when ""
          return undefined

  renderPhotos: ()->
    if @props.book
      if @props.book.photos
        return <li><span className="icon icon-check"></span> Photos</li>
      else
        return <li><span className="icon icon-check"></span> No Photos</li>

  renderColor: ()->
    if @props.book
      if @props.book.color
        return <li><span className="icon icon-check"></span> Full color</li>
      else
        return <li><span className="icon icon-check"></span> Black and White</li>
  
  renderPdfLoader: ()->
    if @state.pdfPages?
      return <span></span>
    else
      return <span>Preview Loading</span>

  renderPdf: ()->
    if not _.isEmpty(@props.book?.preview_pdf)
      # file = "/static/pdf/preview-229_1.pdf"
      file = @props.book?.preview_pdf
      return (
        <div id="book_previewer" style={clear: 'both'}>
          <PdfViewer file={file} page={@state.currentPage} onDocumentComplete={@onDocumentCompleted} onPageCompleted={@.onPageCompleted} loading={(@renderPdfLoader())} />
        </div>
      )
    else
      return undefined

  renderPreviewHeader: ()->
    return (        
      <div id="preview_pager">
        {
          @renderSampleDownload()
        }
        <nav>
          <ul className="pagination">
            <li><a onClick={@onPreviousPage} className="icon-angle-left"></a></li>
            {
             if @state.pdfPages?
              <li>
              <span>
                <span>{@state.currentPage}</span> <span> / </span><span>{@state.pdfPages}</span>
              </span>
              </li>
            }
            <li><a onClick={@onNextPage}className="icon-angle-right"></a></li>
          </ul>
        </nav>
      </div>
        
    )

  renderSampleDownload: ()->
    if @props.book?.download_pdf or @props.book?.preview_pdf
      return (
        <a className="btn btn-gray btn-small" target="_blank" href={@props.book?.preview_pdf}>Download preview PDF</a>
      )
    else
      return undefined

  renderNextButton: ()->
    if @canProceed()
      return <a onClick={@onNext} className="btn btn-navy btn-large pull-right">Next</a>
    else
      return <a onClick={@onNext} className="btn btn-navy btn-large pull-right disabled">Next</a>

  renderFieldError: (field)->
    if @state.showError
      if @state.error?
        if @state.error.validation_errors[field]?
          messages = @state.error.validation_errors[field]
          output = []
          for message in messages
            output.push <div className="alert danger text-center">{message}</div>
          return output
    return undefined    

  removeError: ()->
    @setState
      showError: false
      error: undefined

  isRequired: (warning)->
    switch warning
      when 'approved'
        if !@props.book?.approved
          return <div className="alert warning">Review and confirm preview to proceed.</div>
        else
          return undefined
      when 'shipping'
        if !(@props.book?.shipping_address != null)
          return <div className="alert warning">Add shipping address to proceed.</div>
        else
          return undefined
      else
        return undefined

  render: ()->
    <form onClick={@removeError}>
      <div className="row">
        <div className="col-sm-12 col-md-7">
          <h2 className="section-title">Book Preview</h2>
          {
            @renderPreviewHeader()
          }
          
          {
            @renderPdf()
          }
          
          <hr className="hidden-md hidden-lg" />
        </div>
        <div className="col-sm-12 col-md-5">
          <h2 className="section-title">Confirm Preview</h2>
          {
            @isRequired('approved')
          }
          <div className="panel panel-default book-details">
            <div className="panel-body">
              <ul>
                <li className="title">Included:</li>
                {
                  @renderPageCount()
                }
                {
                  @renderAllAboutMeSelection()
                }
                {
                  @renderColor()
                }
                {
                  @renderPhotos()
                }
              </ul>
            </div>
          </div>                    
          <div className="form-group">
            <div className="checkbox">
              <label>
                <input type="checkbox" name="approved" checked={@props.book?.approved} onChange={@updateBook} />
                Everything looks great, I approve of how it all looks and approve it for printing.
              </label>
            </div>
            {
              @renderFieldError('approved')
            }
          </div>
          <hr />
          <BookShippingCalculator isRequired={@isRequired} {...@props} />
        </div>    
      </div>
      <hr />
      <a onClick={@onPrevious} className="btn btn-gray btn-large pull-left">Previous</a>
      {
        @renderNextButton()
      }
    </form>

BookPreviewContainer = Marty.createContainer BookPreview,
  listenTo: []

  fetch:()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <BookPreview ref="innerComponent" {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <BookPreview ref="innerComponent" {...props} />
  failed: (error)->
    console.log error
    return <div>Book Preview Error</div>

module.exports = BookPreviewContainer