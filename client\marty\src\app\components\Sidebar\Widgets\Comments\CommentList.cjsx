React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
classnames = require 'classnames'

AddComment = require './AddComment'
CommentSubtitleMenu = require './CommentSubtitleMenu'
Comment = require './Comment'

ManageSharingModal = require '../../../Modals/Invitation/ManageSharingModal'
InvitationModal = require '../../../Modals/Invitation/InvitationModal'

AutoAffix = require('react-overlays').AutoAffix
Measure = require('react-measure')

Title = (props)->
    Title.displayName = 'Title'
    switch props.type
      when 'personal'
        header = 'Personal Sharing'
      when 'group'
        header = 'Group Sharing'
      when 'one_on_one'
        header = '1 on 1 Sharing'
    <div className="title">
      {
        <a onClick={props.onBack}><i className="icon-angle-left"></i></a>
      }
      {header}
    </div>

Icon = (props)->
  Icon.displayName = 'Icon'
  if props.type is 'one_on_one'
    <i className={"icon-share-1on1"}></i>
  else
    <i className={"icon-share-#{props.type}"}></i>

Subtitle = (props)->
  Subtitle.displayName = 'Subtitle'
  switch props.type
    when 'personal'
      <span> For your eyes only</span>
    when 'group'
      if props.groupChannel?.members?.length > 1
        return <span> Shared with <a onClick={props.openManageSharing}>{props.groupChannel?.members?.length} people</a></span>
      else
        return <span> Shared with <a onClick={props.openManageSharing}>{props.groupChannel?.members?.length} person</a></span>
    when 'one_on_one'
      return <span> {props.channelUser?.username} & Me </span>
    else
      <span></span>

SubtitleOption = (props)->
  SubtitleOption.displayName = 'SubtitleOption'
  subtitleOption = undefined
  if props.entry?.isOwnedByMe
    if props.type in ['group', 'one_on_one']
      subtitleOption = true
  
  if subtitleOption
    <CommentSubtitleMenu onManage={props.openManageSharing} onInvite={props.openSharingModal} type={props.type}/>
  else
    <div className="col col-option"></div>
  

IntroImg = (props)->
  IntroImg.displayName = 'IntroImg'
  if props.type is 'one_on_one'
    type = '1on1'
  else
    type = props.type
  <img src={"/static/images/sketch-sharing-#{type}.jpg"} />

IntroTitle = (props)->
  IntroTitle.diplayName = 'IntroTitle'
  switch props.type
    when 'personal'
      title = "These are your own personal comments that nobody else can see, even if you share this entry."
    when 'group'
      title = "Any comments made here will be visible to whoever the entry has been shared with."
    when 'one_on_one'
      title = "Comments here are visible only between you and the person you have shared with."
  <div className="title">
    {title}
  </div>

CommentList = React.createClass
  displayName: 'CommentList'  

  getInitialState: ->
    showSubtitleOptionDropdown: false
    showManageSharing: false
    showShareModal: false
    invitationType: undefined
    isCommentEditOpen: undefined
    initialScrollCompleted: false

  componentDidUpdate: (prevProps, prevState) ->
    if not @state.initialScrollCompleted
      if @refs.comments?
        @refs.comments.scrollTop = @refs.comments.scrollHeight
        @setState
          initialScrollCompleted: true

    if @props.comments? and @props.comments?.length > prevProps.comments?.length
      @refs.comments.scrollTop = @refs.comments.scrollHeight

  openShareModal: (type)->
    @setState
      showShareModal: true
      invitationType: type

  closeShareModal: (type)->
    @setState
      showShareModal: false
      invitationType: type

  openSubtitleOptionDropdown: ()->
    @setState
      showSubtitleOptionDropdown: true

  closeSubtitleOptionDropdown: ()->
    @setState
      showSubtitleOptionDropdown: false

  openManageSharing: ()->
    @setState
      showManageSharing: true

  closeManageSharing: ()->
    @setState
      showManageSharing: false

  onPostComment: (comment)->
    @.props.app.commentActionCreators.createComment(comment)

  onBack: ()->
    if @props.onTypeChange?
      @props.onTypeChange('list')

  onHide: ()->
    if @props.onHide?
      @props.onHide()

  onToggleEdit: (editableComment)->
    @setState
      editableComment: editableComment

  renderMainHeader: ()->
    <div className="title-row header-row">
      <div className="col col-title">
        <Title onBack={@onBack} {...@props} />
      </div>
      <div className="col col-close">
        <a onClick={@onHide} className="btn-close">
          <i className="icon-times"></i>
        </a>
      </div>                                    
    </div>

  renderSubHeader: ()->
    if @props.type in ['group','one_on_one']
      type = @props.type
    <div className="subtitle-row header-row">
      <div className="col col-subtitle">
        <div className="subtitle">
          <Icon {...@props} />
          <Subtitle {...@props} openManageSharing={@openManageSharing}/>
        </div>
      </div>
      <SubtitleOption {...@props} openManageSharing={@openManageSharing} openSharingModal={@openShareModal.bind(null, type)}/>
    </div>
    
  renderIntro: ()->
    if _.isEmpty @props.comments
      <div id="comments_intro" className="">
        <IntroImg {...@props} />
        <IntroTitle {...@props} />
      </div>
    else
      return undefined
    
  renderComments: ()->
    if not _.isEmpty @props.comments
      @props.comments.map (comment)=>
        <Comment key={"#{@props.type}-comment-#{comment?.id}"} app={@props.app} entry={@props.entry} comment={comment} onToggleEdit={@onToggleEdit} editableComment={@state.editableComment} />

  renderInvitationModal: ()->
    <InvitationModal app={@props.app} entry={@props.entry} user={@props.user} onHide={@closeShareModal} show={@state.showShareModal} initialHeader={'Share Entry'} type={@state.invitationType}/>

  onContentFocused: ()->
    if @refs.comments?
      @refs.comments.scrollTop = @refs.comments.scrollHeight + 120

  onAlert: (e)->
    # console.log 'Top'
    @onMeasure()
    # console.log @refs.measure?.getDimensions()
    # @setState
    #   maxHeight: @maxHeight()

  onAlert2: (e)->
    # console.log 'Bottom'
    @onMeasure()
    # @setState
    #   maxHeight: @maxHeight()

  onMeasure: (dimensions, mutations)->
    # console.log 'onMeasure'
    
    if dimensions?
      if dimensions?.height + dimensions?.top > @state.initialCommentSectionHeight or not @state.initialCommentSectionHeight?
        initialCommentSectionHeight = dimensions?.height + dimensions?.top
      else
        initialCommentSectionHeight = @state.initialCommentSectionHeight
      
      @setState
        dimensions: dimensions
        viewportWidth: Math.max(document.documentElement.clientWidth, window.innerWidth || 0)
        viewportHeight: Math.max(document.documentElement.clientHeight, window.innerHeight || 0)
        initialCommentSectionHeight: initialCommentSectionHeight

  maxHeight: ()->
    # dimensions = @refs.measure?.getDimensions()
    dimensions = @state.dimensions
    if dimensions?
      viewportWidth = @state.viewportWidth
      viewportHeight = @state.viewportHeight
      entryHeight = (document.getElementById('timeline-column').offsetHeight) - dimensions?.top
      initialCommentSectionHeight = @state.initialCommentSectionHeight
      viewportMaxHeight = (viewportHeight - dimensions?.top) * 0.90
      # console.log dimensions
      # console.log "#{viewportHeight} viewportHeight"
      # console.log "#{viewportWidth} viewportWidth"
      # console.log "#{entryHeight} entryHeight"
      # console.log "#{dimensions?.top + dimensions?.height} comment section total current height"
      # console.log "#{initialCommentSectionHeight} initialCommentSectionHeight"
      if viewportHeight <= initialCommentSectionHeight
        if entryHeight <= viewportHeight 
          # console.log 'entryport used as maxheight'
          maxHeight = Math.min entryHeight, viewportMaxHeight
          minHeight = viewportMaxHeight
        else
          # console.log 'viewportMaxHeight used as maxheight'
          maxHeight = viewportMaxHeight
          minHeight = viewportMaxHeight
        # console.log "#{maxHeight} maxheight"
        # console.log "#{minHeight} minheight"
        return {
          maxHeight: maxHeight
          minHeight: minHeight
        }
      else
        # console.log 'viewportHeight is not less then comment section height'
        return {
          maxHeight: viewportMaxHeight
        }
    else 
      # console.log 'no dimensions'
      return {}

  shouldMeasure: (mutations)->
    return true

  render: ()->
    if @props.type is 'one_on_one'
      type = '1on1'
    else
      type = @props.type
    headerClass = classnames("comments-header-#{type}")
    commentClass = classnames("comments", "comments-#{type}")
       
    <Measure ref='measure' shouldMeasure={@shouldMeasure} onMeasure={@onMeasure}>
      <div>        
        {
          if @state.showManageSharing
            switch @props.type
              when 'one_on_one'
                <ManageSharingModal {...@props} onHide={@closeManageSharing} show={@state.showManageSharing} invitations={@props.oneOnOneChannel?.invitations} />
              when 'group'
                <ManageSharingModal {...@props} onHide={@closeManageSharing} show={@state.showManageSharing} invitations={@props.groupChannel?.invitations} />
        }
        {
          if @state.showShareModal
            @renderInvitationModal()
            
        }
        <div>
          <div id="drawer-comments-header" className={headerClass}>
            {
              @renderMainHeader()
            }
            {
              @renderSubHeader()
            }
          </div>    
        
          <div ref="comments" id="drawer_comments" className={commentClass} style={@maxHeight()}>
            <div className="comments">
              {
                @renderIntro()
              }
              { 
                @renderComments()
              }      
              <AddComment postComment={@onPostComment} onAddCommentFocused={@onContentFocused} {...@props} />
            </div>
          </div>  
        </div>
      </div>
    </Measure>

CommentListContainer = Marty.createContainer CommentList,
  listenTo: ['commentStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      fetchState = {}
      switch @props.type
        when 'group'
          fetchState = {
            comments: @props.groupChannel.comments
          }
        when 'personal'
          fetchState = {
            comments: @props.personalChannel.comments
          }
        when 'one_on_one'
          if @props.channelUser? and @props.oneOnOneChannel.channels[@props.channelUser.resource_uri]?
            fetchState = {
              comments: @props.oneOnOneChannel.channels[@props.channelUser.resource_uri].comments
            }
      return fetchState
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <CommentList {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <CommentList {...props} />
  failed: (error)->
    console.log error
    return <div>CommentList Error</div>

module.exports = CommentListContainer