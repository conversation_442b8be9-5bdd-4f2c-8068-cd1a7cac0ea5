React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
classnames = require 'classnames'

Link = require('react-router').Link

InvitationModal = require '../Modals/Invitation/InvitationModal'
TagMenu = require '../Tags/TagMenu'

InteractionBar = React.createClass
  displayName: 'InteractionBar'

  getInitialState: ->
    showShareModal: false

  getDefaultProps: ->
    resourceURL: "/entry/"
  

  query: ()->
    query = {}  
    
    if @props.aamId?
      query = {aamId: 1}
    else if @props.journalId?
      query = {journalId: @props.journalId}
    else if @props.tagId?
      query = {tagId: @props.tagId}
    else if @props.searchId?
      query = {searchId: 1}

    return query

  openShareModal: ()->
    @setState
      showShareModal: true

  closeShareModal: ()->
    @setState
      showShareModal: false

  renderTags:()->
    if @props.entry?.isOwnedByMe
      if not _.isEmpty(@props.entry?.tags)
        return <TagMenu {...@props}/>
      else
        return (
          <div className="tags button dropdown pull-right">
            <a className="dropdown-toggle disabled"><i className="icon icon-tag"></i></a>  
          </div>
        )
    else
      return undefined

  renderShareIcon: ()->
    if @props.entry?.isOwnedByMe
      <a className="button" onClick={@openShareModal}>
        <i className="icon icon-share"></i>
        <span>Share</span>
      </a>       
    else 
      return undefined

  renderCommentsIcon: ()->
    if @props.invitations? or @props.entry?.isOwnedByMe
      if 'add_entry_comments' in (@props.invitations?.granted_permissions || []) or @props.entry?.isOwnedByMe
        if @props.entry? and @props.entry?.comments?.length > 0 or @props.sharedComments?.length > 0
            iconClassname = classnames("icon","icon-comment-full")
        else
            iconClassname = classnames("icon","icon-comment")
        
        if @props.showComments?
          <a className="button" onClick={@props.showComments}><i className={iconClassname}></i><span>Comments</span></a>
        else
          query = _.extend {}, @query(), {comments: 1}
          <Link to={@props.resourceURL + @props.entry?.id} query={query} params={entryId: @props.entry?.id} query={query} className="button"><i className={iconClassname}></i><span>Comments</span></Link>
      else
        <Link to={@props.resourceURL + @props.entry?.id} query={@query()} params={entryId: @props.entry?.id} query={@query()} className="button"><i className="icon icon-eye"></i><span>View only</span></Link>
          
  render: ()->
    <div className="interaction">
      <div className="interaction-row dropup">
        <div className="col col-left">
          {
            @renderCommentsIcon()
          }
          {
            if @state.showShareModal
              <InvitationModal onHide={@closeShareModal} show={@state.showShareModal} {...@props} initialHeader={'Share Entry'}/>
          }
          {
            @renderShareIcon()
          }
        </div>
        <div className="col col-right">
          {
            @renderTags()
          }
        </div>
      </div>
    </div>

InteractionBarContainer = Marty.createContainer InteractionBar,
  listenTo: ['invitationStore', 'commentStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      if @props.entry?.isOwnedByMe? and not @props.entry?.isOwnedByMe
        fetchState = {}
        if @props.user?.sharing
          fetchState = 
            invitations: @props.app.invitationStore.getInvitationForEntry(@props.entry?.id)
            sharedComments: @props.app.commentStore.getSharedCommentsForEntry(@props.entry?.id)
        return fetchState
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <InteractionBar {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <InteractionBar {...props} />
  failed: (error)->
    console.log error
    return <div>InteractionBar Error</div>

module.exports = InteractionBarContainer