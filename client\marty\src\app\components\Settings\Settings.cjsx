React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

Drawer = require '../Sidebar/Drawer'
AppHeader = require '../Header/AppHeader'
AuthenticatedComponent = require '../AuthenticatedComponent'

ProfileSettings = require './ProfileSettings'
SecuritySettings = require './SecuritySettings'
BillingSettings = require './BillingSettings'
ReminderSettings = require './ReminderSettings'
EmailToJRNLSettings = require './EmailToJRNLSettings'
CustomizeSettings = require './CustomizeSettings'
SocialConnectSettings = require './SocialConnectSettings'

Settings = React.createClass
  displayName: 'Settings Index'

  resetSecuritySettings: ()->
    @props.app.authActionCreators.resetPasswordUpdateState()

  render: ()->
    <div id="settings" className="entry-list">
      <Drawer />
      <div id="page">
        <AppHeader view_title='Settings' {...@props}/>
        <div id="content">
          <div className="container">  
            <div className="settings-column pull-left full-width">
              <div className="frame full-width">
                <ul className="nav nav-tabs tabs-vertical" role="tablist">
                  <li role="presentation" className="active">
                    <a href="#profile-settings" role="tab" data-toggle="tab">Profile</a>
                  </li>
                  <li role="presentation">
                    <a href="#social-connect-settings" role="tab" data-toggle="tab">Social Connect</a>
                  </li>
                  <li role="presentation">
                    <a onClick={@resetSecuritySettings} href="#security-settings" role="tab" data-toggle="tab">Security</a>
                  </li>
                  <li role="presentation">
                    <a href="#reminders-settings" role="tab" data-toggle="tab">Reminders</a>
                  </li>
                  <li role="presentation">
                    <a href="#emailToJRNL-settings" role="tab" data-toggle="tab">Email to JRNL</a>
                  </li>
                  <li role="presentation">
                    <a href="#billing-settings" role="tab" data-toggle="tab">Billing</a>
                  </li>
                  <li role="presentation">
                    <a href="#customize-settings" role="tab" data-toggle="tab">Theme</a>
                  </li>
                </ul>
                {
                  if @props.app.authStore.isLoggedIn()
                    <div className="tab-content">
                      <ProfileSettings {...@props} />
                      <SecuritySettings {...@props} />
                      <ReminderSettings {...@props} />
                      <BillingSettings {...@props} />
                      <EmailToJRNLSettings {...@props} />
                      <CustomizeSettings {...@props} />
                      <SocialConnectSettings {...@props} />
                    </div>
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

SettingsContainer = AuthenticatedComponent(Marty.createContainer Settings,
  listenTo: []

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        username: @props.app.authStore.getFirstNameOrUsername()
        firstname: @.props.app.authStore.getFirstNameOrUsername()
        user: @props.app.authStore.fetchUser()
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Settings {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Settings {...props} />
  failed: (error)->
    return <div>Settings Error</div>
)
module.exports = SettingsContainer
