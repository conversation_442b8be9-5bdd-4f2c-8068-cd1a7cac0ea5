React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
Link = require('react-router').Link

# TODO npm package for global namespace access
AuthenticatedComponent = require '../AuthenticatedComponent'

Drawer = require '../Sidebar/Drawer'
AppHeader = require '../Header/AppHeader'

AuthenticatedComponent = require '../AuthenticatedComponent'

Library = React.createClass
  displayName: "Library"
  mixins: [Marty.createAppMixin()]

  render: ()->
    <div>
      <Drawer />
      <div id="page">
        <AppHeader username={@props.username} view_title='Library' {...@props} />
        <div id="content">
          <div className="container">
            <div id="category" className="frame">
              <ul className="list">
                <li>
                  <Link to={"/library/tags/"}>
                    <div className="item-icon-right">
                      <span className="icon icon-angle-right"></span>
                      <span className="sub-text">{@props.tag_pager?.total_count}</span>
                    </div>
                    <div className="item-icon-left">
                      <img src="/static/images/icon-tag.svg" />
                    </div>
                    <h2>Tags</h2>
                  </Link>
                </li>
                <li>
                  <Link to={"/library/journals/"}>
                    <div className="item-icon-right">
                      <span className="icon icon-angle-right"></span>
                      <span className="sub-text">{@props.journal_pager?.total_count}</span>
                    </div>
                    <div className="item-icon-left">
                      <img src="/static/images/icon-journal.svg" />
                    </div>
                    <h2>Journals</h2>
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

LibraryListContainer = AuthenticatedComponent(Marty.createContainer Library,
  listenTo: ['tagsStore', 'journalStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      return {
        tags: @props.app.tagsStore.getTags('Tags')
        journals: @props.app.journalStore.getJournals('Journals')
        username: @props.app.authStore.getUsername()
        firstname: @.props.app.authStore.getFirstNameOrUsername()
        user: @props.app.authStore.fetchUser()
        tag_pager: @.props.app.pageStore.getPage('Tags')
        journal_pager: @.props.app.pageStore.getPage('Journals')
      }
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <Library {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Library {...props}/>
  failed: (error)->
    console.log error
    return <div>Library Error</div>
)

module.exports = LibraryListContainer
