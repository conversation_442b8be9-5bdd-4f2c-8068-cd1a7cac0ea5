/*---------------------------------------------------------------------------
  >Modal Media Manager
---------------------------------------------------------------------------*/

#modal_media_manager {
	
	.modal-dialog {
    width: 1000px;
    
    .modal-body {
      height: 500px;
	    
	    .title {
		    .navy-txt;
		    .font2;
		    .f16;
	    }
	    
	    .sidebar, .content {
		    float: left;
		    height: 500px;
	    }
	    
	    .sidebar {
		    
		    .widget {
	        border-bottom: 1px solid @gray9;
			    padding: 15px 20px;
			    margin-bottom: 0;
		    }
	    }
	    
	    .content {
		    border-left: 1px solid @gray9;
		    
		    .title {
					padding: 15px 20px 10px;
		    }
		    
		    .thumbnail-scroller {
			    white-space: nowrap;
			    overflow-y: hidden;
			    overflow-x: scroll;
			    -webkit-overflow-scrolling: touch;
					height: 140px;
					padding: 0px 12px 12px;
					
					.instruction-text {
						.gray5-txt;
						font-size: 20px;
						letter-spacing: -0.5px;
						font-weight: 300;
						top: 48px;
						width: 100%;
						text-align: center;
					}
		    }
		    
		    .upload-area {
			    
			    #quota_warning {top: 55px;}
			    
			    .dotted-line {
				    border: 3px dotted #ddd;
				    padding: 50px;
				    margin: 20px; 
			    }
			    
			    .upload-instructions {
				    text-align: center;
				    
				    .btn {margin: 10px 0;}
			    }
		    }	    
		    
				#quota_warning {
					text-align: center;
					top: 100px;
				  max-width: 400px;
				  margin: auto;
					
					.icon {
						font-size: 60px;
						padding: 8px 0;
						.orange-txt;
					}
					
					.title {
				    padding: 10px 0;
						.f30;
					}
				}
	    }
    }
  }
}

.file {
  width: 100px;
  height: 100px;
  border: 1px solid rgba(0, 0, 0, 0.2);
	display: inline-block;
  margin: 8px;
  background-color: @gray10;
  
  .indicator {
    z-index: 10;
		position: absolute;
    top: -8px;
    right: -8px;
    width: 25px;
    height: 25px;
    padding: 0;
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.2);
 		border-radius: 100px; 
	  .gradient-w-g;
	  
	  i {.f15;}
	  
	  &:hover {
		  .white-txt;
		  background: @red;
	  }
  }
  
  .thumb-icon {
    margin: 16px auto 0;
    height: 48px;
    width: auto;
    display: block;
  }

	.file-info {  
		position: absolute;
		width: 98px;
		text-align: center;
		font-size: 9px;
    bottom: 0;
    z-index: 10;
    .gradient-g-w;
    padding: 4px 0;
    
    .divider {
	    opacity: .2;
	    padding: 0 4px;
    }
	}
  
  &.uploaded {
	  .white-bg;
	  
  	.indicator {
		  .white-txt;
			background: @green; 
		}
  }
  
  .thumb {
		width: 98px;
    height: 98px;
		display: block;
  }
    
  .status {
    text-align: center;
    position: absolute;
    width: 98px;
    
    .loading {
	    display: block;
	    margin-bottom: 4px;
	    font-size: 28px;
	  }
    
    .message {
	    font-size: 10px;
	    line-height: 14px;
	    display: block;
			.gray5-txt;
	  }
  }
  
	&.uploading .status {top: 25px;}
	
	&.upload-error {
		.status {
			top: 18px;
			
			.loading {
		    margin-bottom: 4px;
		    font-size: 28px;
				.gray5-txt;
			}
		}
	}
}

.dropzone {

	.dz-preview {
		display: inline-block;
		
		.dz-progress,
		.dz-progress .dz-upload,
		&.dz-error .dz-error-message,
		&.dz-error .dz-error-mark,
		&.dz-error .file.upload-error,
		&.dz-success .dz-success-mark,
		&.dz-success.dz-complete .file.uploaded,
		&.dz-processing .file.uploading 
		{display: block;}
		
		.dz-error-message,
		.dz-error-mark, 
		.dz-success-mark,
		.file.uploaded,
		.file.upload-error,
		&.dz-processing.dz-complete .file.uploading, 
		&.dz-error.dz-complete .file.uploading
		{display: none;}
	}
}

/*---------------------------------------------------------------------------
  >Queries
---------------------------------------------------------------------------*/

/* SM */ @media screen and (max-width: 991px) {
	
	#modal_media_manager {
		
		.modal-dialog {
	    width: 700px;
	    
	    .sidebar {width: 220px;}
	    .content {width: 478px;}
	  }
	}
	
}
/* MD */ @media screen and (max-width: 1199px) and (min-width: 992px) {
	
	#modal_media_manager {
		
		.modal-dialog {
	    width: 800px;
	    
	    .sidebar {width: 220px;}
	    .content {width: 578px;}
	  }
	}
	
}
/* LG + */ @media screen and (min-width: 1200px) {
	
	#modal_media_manager {
		
		.modal-dialog {
	    width: 1000px;
	    
	    .sidebar {width: 280px;}
	    .content {width: 718px;}
	  }
	}
	
}