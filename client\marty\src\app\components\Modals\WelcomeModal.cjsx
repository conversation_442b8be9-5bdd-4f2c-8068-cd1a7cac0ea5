React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

Modal = require('react-bootstrap').Modal

WelcomeModal = React.createClass
  displayName: 'WelcomeModal'

  render: ()->
    <Modal {...@props} backdrop={'static'} className="modal carousel-fade modal-carousels">
      <div id="welcome-carousel" className="carousel slide" data-interval="false">
        <div className="modal-header teal-bg">
          <div className="row">
            <div className="col-sm-3">
              <a onClick={@props.onHide} className="btn-flat pull-left" data-dismiss="modal" aria-label="Close"><i className="icon icon-times"></i></a>
            </div>
            <div className="col-sm-6 text-center">
            </div>
            <div className="col-sm-3 text-right">
              <ol className="carousel-indicators">
                <li data-target="#welcome-carousel" data-slide-to="0" className="active"></li>
                <li data-target="#welcome-carousel" data-slide-to="1"></li>
                <li data-target="#welcome-carousel" data-slide-to="2"></li>
                <li data-target="#welcome-carousel" data-slide-to="3"></li>
                <li data-target="#welcome-carousel" data-slide-to="4"></li>
                <li data-target="#welcome-carousel" data-slide-to="5"></li>
                <li data-target="#welcome-carousel" data-slide-to="6"></li>
                <li data-target="#welcome-carousel" data-slide-to="7"></li>
              </ol>             
            </div>
          </div>
        </div>
        
        <div className="carousel-inner" role="listbox">
          
          <div className="item active">
            <img src="/static/images/carousel-main.jpg" width="100%" height="auto" />
            <div className="description padding-30">
              <h2>Welcome to JRNL!</h2>
              <p>We've built the best tool to help you record, preserve, reflect, and publish the most important moments of your life. We'd like to tell you a little about what JRNL can do starting with creating entries, click the button below to continue.</p>
              <hr />
              <div className="row">
                <div className="col-sm-6">
                </div>
                <div className="col-sm-6 text-right">
                  <a className="btn btn-navy btn-medium" href="#welcome-carousel" role="button" data-slide="next">Up Next: Creating Entries <i className="icon-angle-right"></i></a>
                </div>
              </div>
            </div>
          </div>
          
          <div className="item">
            <iframe className="carousel_video" width="560" height="315" src="https://www.youtube.com/embed/uR5Nn8rzaPQ?rel=0" frameborder="0" allowfullscreen></iframe>
            <div className="description padding-30">
              <h2>Creating Journal Entries</h2>
              <p>Create unlimited journal entries and write to your heart's content. Add photos, tags, and format your text. Import content from Social Media directly into your entry.</p>
              <hr />
              <div className="row">
                <div className="col-sm-7"></div>
                <div className="col-sm-5 text-right">
                  <a className="btn btn-navy btn-medium" href="#welcome-carousel" role="button" data-slide="next">Up Next: All About Me <i className="icon-angle-right"></i></a>
                </div>
              </div>
            </div>
          </div>

          <div className="item">
            <iframe className="carousel_video" width="560" height="315" src="https://www.youtube.com/embed/uTcQ45JP9yU?rel=0" frameborder="0" allowfullscreen></iframe>
            <div className="description padding-30">
              <h2>All About Me</h2>
              <p>We've created a huge library of categorized questions that you can answer any time to help build a snapshot of who you are. All About Me is a great place to visit when you get stuck and can't think of anything else to write about.</p>
              <hr />
              <div className="row">
                <div className="col-sm-7"></div>
                <div className="col-sm-5 text-right">
                  <a className="btn btn-navy btn-medium" href="#welcome-carousel" role="button" data-slide="next">Up Next: Social Import <i className="icon-angle-right"></i></a>
                </div>
              </div>
            </div>
          </div>

          <div className="item">
            <iframe className="carousel_video" width="560" height="315" src="https://www.youtube.com/embed/R7_J9uiA-2s?rel=0" frameborder="0" allowfullscreen></iframe>
            <div className="description padding-30">
              <h2>Social Import</h2>
              <p>Collect all of life's meaningful moments in one location with JRNL's Social Importing. Connect your Instagram account (more coming soon) and only keep the posts you feel are journal-worthy.</p>
              <hr />
              <div className="row">
                <div className="col-sm-7"></div>
                <div className="col-sm-5 text-right">
                  <a className="btn btn-navy btn-medium" href="#welcome-carousel" role="button" data-slide="next">Up Next: Entry Sharing <i className="icon-angle-right"></i></a>
                </div>
              </div>
            </div>
          </div>

          <div className="item">
            <iframe className="carousel_video" width="560" height="315" src="https://www.youtube.com/embed/rBSBwLyCDxc?rel=0" frameborder="0" allowfullscreen></iframe>
            <div className="description padding-30">
              <h2>Entry Sharing</h2>
              <p>Group sharing allows you to share an entry with multiple people and request a contribution from each of them that is visible to everyone in the group. 1 on 1 sharing allows you to share an entry with individuals privately, and request a contribution that will only be visible to you and that specific person.</p>
              <hr />
              <div className="row">
                <div className="col-sm-7"></div>
                <div className="col-sm-5 text-right">
                  <a className="btn btn-navy btn-medium" href="#welcome-carousel" role="button" data-slide="next">Up Next: Share Extensions <i className="icon-angle-right"></i></a>
                </div>
              </div>
            </div>
          </div>

          <div className="item">
            <iframe className="carousel_video" width="560" height="315" src="https://www.youtube.com/embed/cv-l26i0tdw?rel=0" frameborder="0" allowfullscreen></iframe>
            <div className="description padding-30">
              <h2>Share Extensions</h2>
              <p>Create journal entries quickly and easily with share extensions <a href="https://itunes.apple.com/us/app/jrnl.com/id1036338627?mt=8">in the JRNL iOS app</a>. Add photos and captions or use Siri to dictate your journal entry.</p>
              <hr />
              <div className="row">
                <div className="col-sm-7"></div>
                <div className="col-sm-5 text-right">
                  <a className="btn btn-navy btn-medium" href="#welcome-carousel" role="button" data-slide="next">Up Next: Email to JRNL <i className="icon-angle-right"></i></a>
                </div>
              </div>
            </div>
          </div>

          <div className="item">
            <iframe className="carousel_video" width="560" height="315" src="https://www.youtube.com/embed/S4cLIcct4Jo?rel=0" frameborder="0" allowfullscreen></iframe>
            <div className="description padding-30">
              <h2>Email to JRNL</h2>
              <p>You can create entries by emailing them to any journal in your account.</p>
              <hr />
              <div className="row">
                <div className="col-sm-7"></div>
                <div className="col-sm-5 text-right">
                  <a className="btn btn-navy btn-medium" href="#welcome-carousel" role="button" data-slide="next">Up Next: Bookbuilder <i className="icon-angle-right"></i></a>
                </div>
              </div>
            </div>
          </div>

          <div className="item">
            <img src="/static/images/carousel-bookbuilder.jpg" width="100%" height="auto" />
            <div className="description padding-30">
              <h2>Bookbuilder</h2>
              <p>One of the most exciting and unique features of JRNL is our Bookbuilder. You can easily publish your entries into a beautiful hard-cover, archival-quality book that can be enjoyed for generations to come.</p>
              <hr />
              <div className="row">
                <div className="col-sm-8">
                </div>
                <div className="col-sm-4 text-right">
                  <a className="btn btn-navy btn-medium" onClick={@props.onHide}>Close Tour</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
        
    </Modal>

WelcomeModalContainer = Marty.createContainer WelcomeModal,
  listenTo: ['userStore']
  
  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <WelcomeModal {...props} />
  pending: (fetches)->  
    props = _.extend {}, @props, fetches
    return <WelcomeModal {...props} />
  failed: (error)->
    console.log error
    return <div>Welcome Modal Error</div>

module.exports = WelcomeModalContainer