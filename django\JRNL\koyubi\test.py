import re
CARD_TYPES = {
    'visa': r'4\d{12}(\d{3})?$',
    'amex': r'3[47]\d{13}$',
    'mc': r'5[1-5]\d{14}$',
    'discover': r'6011\d{12}',
    'diners': r'(30[0-5]\d{11}|(36|38)\d{12})$'
}

# Test with 34xx AMEX
test_card = "***************"  # 15 digits starting with 34
amex_pattern = CARD_TYPES['amex']
print(f"Pattern: {amex_pattern}")
print(f"34xx AMEX matches: {bool(re.match(amex_pattern, test_card))}")

# Test with 37xx AMEX  
test_card_37 = "***************"
print(f"37xx AMEX matches: {bool(re.match(amex_pattern, test_card_37))}")