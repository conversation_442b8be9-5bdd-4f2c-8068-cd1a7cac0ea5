React = require 'react'
Marty = require 'marty'
moment = require 'moment'
_ = require 'underscore'

# TODO npm package for global namespace access
AuthenticatedComponent = require '../AuthenticatedComponent'

Drawer = require '../Sidebar/Drawer'
Sidebar = require '../Sidebar/Sidebar'
AppHeader = require '../Header/AppHeader'

JournalListHeader = require './JournalListHeader'
EntryTile = require '../Entry/EntryTile'
AnswerTile = require '../AllAboutMe/AnswerTile'
EmptyTile = require '../Timeline/EmptyTile'
LoadMore = require '../Timeline/LoadMore'
NewEntryListButton = require '../Timeline/NewEntryListButton'

WelcomeModal = require '../Modals/WelcomeModal'

JournalList = React.createClass
    displayName:"JournalList Handler"
    mixin: [Marty.createAppMixin()]

    getInitialState: ->
      isEmpty: false
      pager: {
        previous: @props.page?.previous
        next: @props.page?.next
        total_count: @props.page?.total_count
        offset: @props.page?.offset
        limit: @props.page?.limit
      }
      showWelcomeModal: false

    componentDidMount: ()->
      # @.app.calendarActionCreators.updateCalendar(@buildOptions(), 'Journal' + @props.journalId)
      if @state.pager?
        if parseInt(@state.pager.total_count) == 0
          @setState
            isEmpty: true
    
    componentWillReceiveProps: (nextProps) ->
      isEmptyForReal = false
      if @props.entries? and nextProps.entries?
        if !_.isEmpty(@props.entries) and !_.isEmpty(nextProps.entries)
          if @props.entries[0].id == "EMPTY" and nextProps.entries[0].id == "EMPTY"
            isEmptyForReal = true
          else
            isEmptyForReal = false
        else
          isEmptyForReal = false

      if nextProps.page?
        if parseInt(nextProps.page.total_count) == 0
          isEmptyForReal = true

      if nextProps.user?.has_seen_welcome_web?
        if nextProps.user.has_seen_welcome_web
          @setState
            showWelcomeModal: false
        else
          @setState
            showWelcomeModal: true
      
      @setState
        isEmpty: isEmptyForReal
        pager: {
          previous: nextProps.page?.previous
          next: nextProps.page?.next
          total_count: nextProps.page?.total_count
          offset: nextProps.page?.offset
          limit: nextProps.page?.limit
        }
    
    onLoadMore: ()->
      if @state.pager.next?
        @.app.entriesQueries.getPage(@state.pager.next, 'Journal' + @props.journal.id)
               
    onWidgetCallback: (widget, value, view_mode)->
      if widget == 'calendar'
        if not view_mode?
          view_mode = 'day'
        options = @buildOptions(value, view_mode, @props.entry_view_mode)
        if @props.journal?
          options['journal'] = @props.journal.id
        
        @.app.entryActionCreators.updateEntries(options, 'Journal'+ @props.journal.id)
        @.app.calendarActionCreators.lockWidget()

    buildOptions: (value, view_mode, entry_view_mode)->
      filter_by = @.app.entryStore.getFilterBy()
      options = 
        order_by: @props.order_by
        min_date: moment(value).startOf(view_mode).format('YYYY-MM-DD'+'T'+'00:00:00')
        max_date: moment(value).endOf(view_mode).format('YYYY-MM-DD'+'T'+'23:59:59')
        filter_by: filter_by
        view_mode: view_mode
        journal: @props.journalId
        entry_view_mode: entry_view_mode

    onViewModeChange: (view_mode)->
      switch view_mode
        when 'day'
          @.app.entryActionCreators.updateViewMode({view_mode: view_mode}, 'Journal')
        when 'month','year'
          options = @buildOptions @props.min_date, view_mode, @props.entry_view_mode    
          @.app.entryActionCreators.updateEntries(options, 'Journal' + @props.journal.id)

    onEntryViewModeChange: (entry_view_mode, id)->
      options = @buildOptions @props.min_date, @props.view_mode, entry_view_mode
      @app.entryActionCreators.updateEntries(options, 'Journal' + @props.journal.id)
      calendarOptions = {utc_offset: moment().format('Z'), journal: @props.journalId}
      @app.calendarQueries.getCalendar(calendarOptions, 'Journal' + @props.journal.id)

    openWelcomeModal: ()->
      @setState
        showWelcomeModal: true

    closeWelcomeModal: ()->
      @app.userActionCreators.updateWelcomeStatus(true)
      @setState
        showWelcomeModal: false

    renderEmpty: ()->
      if @state.isEmpty
        return (
          <EmptyTile key={'journallist-empty'} {...@props} />
        )
      else
        return undefined

    renderPager: ()->
      if @state.pager?.next? and @props.entries?
        return (
          <div>
            <LoadMore pager={@state.pager} loaded={@props.entries.length} onClick={@onLoadMore}/>
          </div>
        )
      else
        return undefined

    renderEntriesOrEmpty: (entries)->
      if entries?
        addButton = false
        output = entries.map (entry)=>               
          switch entry.entry_type
            when "QA"
              addButton = true
              return (
                <AnswerTile {...@props} key={entry.id} answer={entry} />
              )
            when "Default"
              addButton = true
              return (
                <EntryTile {...@props} key={entry.id} entry={entry} />
              )
            when ""
              addButton = true
              return (
                <EntryTile {...@props} key={entry.id} entry={entry} />
              )
            when "EMPTY"
              return @renderEmpty()
        
        if addButton and @props.view_mode == 'day'
          output.unshift <NewEntryListButton {...@props} />
        
        return output
      else
        return undefined

    render: ()->      
      entries = @props.entries
      <div>
        <WelcomeModal show={@state.showWelcomeModal} onHide=(@closeWelcomeModal) {...@props} />
        <Drawer />
        <div id="page">
          <AppHeader view_title={@props.journal?.title} {...@props}/>
          <JournalListHeader onEntryViewModeChange={@onEntryViewModeChange} onViewModeChange={@onViewModeChange} widgetCallback={@onWidgetCallback} view_id='Journal' {...@props}/>
          <div id="content">
            <div className="container">
              <div id="timeline-column" className="pull-left right-padding">
                {
                  @renderEntriesOrEmpty(entries)
                }  
                {
                  @renderPager()
                }
              </div>
              <Sidebar widgetCallback={@onWidgetCallback} {...@props}/>
            </div>
          </div>
        </div>
      </div>

JournalListContainer = AuthenticatedComponent(
  Marty.createContainer JournalList,
    listenTo: ['authStore', 'entryStore','journalStore','calendarStore','allaboutmeStore']

    fetch: ()->
      if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
        journalId = @props.params.journalId
        options = {journal: journalId}
        viewId = 'Journal' + journalId
        return {
          journalId: journalId
          journal: @props.app.journalStore.getJournalById(journalId, viewId)
          order_by: @props.app.entryStore.getOrderBy()
          min_date: @props.app.listStore.getMinDate(options, viewId)
          max_date: @props.app.listStore.getMaxDate(options, viewId)
          username: @props.app.authStore.getUsername()
          firstname: @.props.app.authStore.getFirstNameOrUsername()
          view_mode: @props.app.listStore.getDateViewMode(viewId)
          entry_view_mode: @props.app.listStore.getEntryViewMode(viewId)
          page: @props.app.pageStore.getPage(viewId)
          entries: @props.app.entryStore.getEntriesByJournal(journalId, viewId)
          random_question: @props.app.allaboutmeStore.getRandomQuestion()
          user: @props.app.authStore.fetchUser()
        } 
      else
        return {}
    done: (results)->
      props = _.extend({}, @props, results)
      return <JournalList {...props}/>
    pending: (fetches)->
      props = _.extend({}, @props, fetches)
      return <JournalList {...props}/>
    failed: (error)->
      console.log error
      return <div>Error</div>
)

module.exports = JournalListContainer
