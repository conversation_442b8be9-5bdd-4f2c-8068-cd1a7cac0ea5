React = require 'react'
Marty = require 'marty'
$ = require 'jquery'

Dropdown = require('react-bootstrap').Dropdown

EntryListSortControl = React.createClass
  displayName: 'EntryListSortControl'
  mixins:[Marty.createAppMixin()]

  getInitialState: ->
    isOpen: false
  
  onToggle: (isOpen)->
    @setState
      isOpen: isOpen
  
  onClick: (options, e)->
    id = 'Timeline'
    if @props.journal?
      options['journal'] = @props.journal.id
      id = 'Journals'

    if @props.tag?
      options['tags__id'] = @props.tag.id
      id = 'Tags'

    if options.entry_view_mode?  
      if @props.onEntryViewModeChange?
        @props.onEntryViewModeChange(options.entry_view_mode,id)
    else
      @.app.entryActionCreators.updateEntries(options, id)

    @setState
      isOpen: !@state.isOpen
  
  activate: (sort_option)->
    if sort_option == @props.order_by
      return <span className="icon icon-check pull-right"></span>
    else
      return undefined

  activateViewModeOption: (viewModeOption)->
    if viewModeOption is @props.entry_view_mode
      return <span className="icon icon-check pull-right"></span>
    else
      return undefined

  render: ()->
    <Dropdown id={'FilterAndSortMenu'} className="fr" componentClass={'div'} onToggle={@onToggle} open={@state.isOpen}>
      <Dropdown.Toggle noCaret={true} useAnchor={true} className="dropdown-toggle icon icon-filter" />
      <Dropdown.Menu className={"dropdown-swing-left dropdown-default"} style={{minWidth: "240px"}} componenentClass={'ul'}>
          <li className="heading">Entry View</li>
          <li>
            <a onClick={@onClick.bind null, {entry_view_mode: 'just_me'}}>{@activateViewModeOption('just_me')}Just my entries</a>
          </li>
          {
            if @props.user?.sharing and not @props.disableEntryViewMode
              <li>
                <a onClick={@onClick.bind null, {entry_view_mode: 'shared_by_me'}}>{@activateViewModeOption('shared_by_me')}Entries I have shared</a>
              </li>
          }
          {
            if @props.user?.sharing and not @props.disableEntryViewMode
              <li>
                <a onClick={@onClick.bind null, {entry_view_mode: 'shared_with_me'}}>{@activateViewModeOption('shared_with_me')}Entries shared with me</a>
              </li>
          }
          { 
            if @props.user?.sharing and not @props.disableEntryViewMode
              <li>
                <a onClick={@onClick.bind null, {entry_view_mode: 'all'}}>{@activateViewModeOption('all')}All entries</a>
              </li>
          }
          <li className="heading">Sort Entries by</li>
          {
            @.app.entryStore.getOrderByOptions().map (sort_option)=>
              return (
                <li key={sort_option.value + '-option'}>
                  <a onClick={@onClick.bind null,{order_by: sort_option.value, filter_by: 'entry_date'}} data-value={sort_option.value}>{@activate(sort_option.value)}{sort_option.title}</a>
                </li>
              )
          }
      </Dropdown.Menu>
    </Dropdown>
              
module.exports = EntryListSortControl