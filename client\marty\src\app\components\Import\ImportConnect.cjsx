React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
classnames = require('classnames')

ListItem = require '../SocialConnect/SocialConnectListItem'

ImportConnect = React.createClass
  displayName: 'ImportConnect'

  componentDidMount: ->
    window.addEventListener 'message', @receiveMessage, false

  componentWillUnmount: ->
    window.removeEventListener 'message', @receiveMessage
  
  receiveMessage: (event)->
    if event.data.code?
      if event.data.error?
        console.log event.data
      else
        @app.socialConnectActionCreators.setInstagramCode(event.data.code)
  
  requestMediaImport: ()->
    @app.socialConnectActionCreators.startMediaImport()
  
  renderConnectedAccounts: ()->
    if !(_.isEmpty @props.instagram_access_token)
      [
        <div key={'1'} className="list__divider">Connected Accounts</div>,
        <ListItem 
          key={'2'}
          avatarURL={@props.instagram_access_token.ig_profile_picture}
          username={@props.instagram_access_token.ig_username} 
          title="Instagram" 
          connectedStatus={!(_.isEmpty @props.instagram_access_token)}
          onClick={@requestMediaImport}
        />
      ]
      
  render: ()->
    <div className="">
      <div className="modal-header teal-bg">
        <div className="row">
          <div className="col-xs-2">
            <a className="btn-flat pull-left" onClick={@props.onHide}><i className="icon icon-times"></i></a>
          </div>
          <div className="col-xs-8 text-center">
            <div className="modal-title">Social Accounts</div>
          </div>
          <div className="col-xs-2">
          </div>
        </div>
      </div>
      <div className="modal-body">
        <div className="connect-intro">
          <div className="connect-intro__title">Import your Instagram photos!</div>        
          <div className="connect-intro__subtitle">Include your Instagram photos with captions in your journal entry.</div>
          <img className="connect-intro__image" src="/static/images/instagram-import.png" />
        </div>
        <ListItem social="Instagram" title="Instagram" connectedStatus={!(_.isEmpty @props.instagram_access_token)} titleURL={@props.instagram_authorization_url?.url} asButton={true} />
        {
          @renderConnectedAccounts()
        }
      </div>    
    </div>

ImportConnectContainer = Marty.createContainer ImportConnect,
  listenTo: ['authStore', 'socialStore']

  fetch: ()->
    if @props.app.authStore.isLoggedIn() and @props.app.authStore.isActive()
      instagram_access_token : @props.app.socialStore.getInstagramAccessToken()
      instagram_authorization_url: @props.app.socialStore.getInstagramAuthorizationURL()
    else
      return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <ImportConnect {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <ImportConnect {...props}/>
  failed: (error)->
    console.log error
    return <div>Social Connect Settings Error</div>

module.exports = ImportConnectContainer