Marty = require 'marty'
timelineApprovalConstants = require '../constants/TimelineApprovalConstants'

TimelineApprovalActionCreators = Marty.createActionCreators
  toggleVisibility: (timelineApproval)->
    options = 
      approval_status: !timelineApproval.approval_status
    resource_uri = timelineApproval.resource_uri.substring(1)
    return @app.timelineApprovalHttpAPI.updateTimelineApproval(resource_uri, options)
    .then (response)=>
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @dispatch timelineApprovalConstants.TIMELINE_APPROVAL_UPDATED, success
      @app.calendarActionCreators.updateCalendar({}, 'Timeline')
      @app.entryActionCreators.setRefreshFlag()
    .catch (error)=>
      console.log error
      @dispatch timelineApprovalConstants.TIMELINE_APPROVAL_ERROR, error

module.exports = TimelineApprovalActionCreators