React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

Link = require('react-router').Link
History = require('react-router').History

InvitationWelcome = React.createClass
  displayName: 'InvitationWelcome'
  mixins: [History]

  componentDidMount: ->
    if @props.transitionTo?
      @history.pushState null, @props.transitionTo

    if @props.isRegistered
      @history.pushState null, '/signup/success/'
    else
      @props.app.registrationActionCreators.reset_signup()

  componentWillReceiveProps: (nextProps) ->
    if nextProps.isRegistered
      @props.history.pushState null, '/signup/success/'

  onSignup: (e)->
    e.preventDefault()
    e.stopPropagation()
    username = @refs.usernameSignupInput?.value
    email = @refs.emailSignupInput?.value
    password = @refs.passwordSignupInput?.value
    affiliate = @props.jrnlAffiliate
    hasOfferAffiliate = @props.hasOfferAffiliate
    
    user = 
      username: username
      email: email
      password: password
      affiliate: affiliate
      hasoffer_affiliate: hasOfferAffiliate
      invitation_unique_id: @props.invitationId
    @.app.registrationActionCreators.signup(user)
  
  onLogin: (e)->
    e.stopPropagation()
    e.preventDefault()
    username = @refs.usernameLoginInput?.value
    password = @refs.passwordLoginInput?.value
    
    if @props.invitationId?
      nextPath = "/invitation/#{@props.invitationId}"
    else
      nextPath = "/"

    @.app.authActionCreators.authenticate(username, password, nextPath)
    
  renderLoginError: ()->
    if @props.loginError?
      return <div className="alert danger text-center">{@props.loginError.user_message}</div>
    else
      return undefined

  removeLoginError: ()->
    @props.app.authActionCreators.resetUserError()

  removeSignupError: ()->
    @app.registrationActionCreators.reset_registration_error()

  onKeyDown: (e)->
    if e.keyCode == 13
      @onLogin(e)

  renderSignupError: ()->
    if @props.signupError?
      if @props.signupError.validation_errors?
        output = []
        for field, error of @props.signupError.validation_errors
          output.push <div key={"validation-error"} className="alert danger text-center">{error[0]}</div>
        return output
    return undefined

  renderSignupFieldError: (field)->
    if @props.signupError?.validation_errors?
      if @props.signupError.validation_errors[field]?
        messages = @props.signupError.validation_errors[field]
        output = []
        for message, i in messages
          output.push <div key={"message#{i}"} className="alert danger text-center">{message}</div>
        return output
    return undefined    

  render: ()->
    <div id="access" className="access-share">
      <div className="container">
        <div className="panel frame">
          <div className="panel-body">
            <Link to={"/"} className="jrnl-logo"><img src="/static/images/jrnl-logo.png" /></Link>
            <div className="col-xs-12">
              <div className="instructions">For security purposes, please log in or create an account to continue...</div>
            </div>
            <div className="col-sm-6">
              <div id="login">
                <div className="form-wrapper">          
                  <h2>Login to your account</h2>
                  {
                    @renderLoginError()
                  }
                  <div className="form-group">
                    <input ref="usernameLoginInput" type="email" className="form-control" placeholder="JRNL username" onClick={@removeLoginError} onChange={@removeLoginError} onKeyDown={@onKeyDown} autoComplete={"off"}/>
                    <input ref="passwordLoginInput" type="password" className="form-control" placeholder="JRNL password" onClick={@removeLoginError} onChange={@removeLoginError} onKeyDown={@onKeyDown} autoComplete={"off"}/>
                    <a onClick={@onLogin} className="btn btn-navy btn-medium full-width">Login</a>
                    <div className="access-links">
                      <Link to={"/forgot-password/"}>Forgot Password?</Link>
                    </div>
                  </div>
                </div>
              </div> 
            </div>
            <div className="col-sm-6">
              <div id="signup">
                <div className="form-wrapper">
                  <h2>Create a new account</h2>
                  <div className="form-group">
                    <input ref="usernameSignupInput" type="text" className="form-control" placeholder="Choose a username" autoComplete={"off"} onClick={@removeSignupError} onChange={@removeSignupError} defaultValue={@props.user?.username}/>
                      {
                        @renderSignupFieldError('username')
                      }
                    <input ref="emailSignupInput" type="email" className="form-control" placeholder="Enter a valid Email address" autoComplete={"off"} onClick={@removeSignupError} onChange={@removeSignupError} defaultValue={@props.user?.email}/>
                      {
                        @renderSignupFieldError('email')
                      }
                    <input ref="passwordSignupInput" type="password" className="form-control" placeholder="Create a password" autoComplete={"off"} onClick={@removeSignupError} onChange={@removeSignupError} />
                      {
                        @renderSignupFieldError('password')
                      }
                    <a onClick={@onSignup} className="btn btn-navy btn-med full-width">Signup Here!</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="legal">
          <div className="links">
            <Link to={"/"}>Home</Link><a href="http://jrnl.com/privacy.html">Privacy</a><a href="http://jrnl.com/terms.html">Terms</a>
            <div className="copyright">©2016 JRNL Inc.</div>
          </div>
        </div>
      </div>
    </div>
  

InvitationWelcomeContainer = Marty.createContainer InvitationWelcome,
  listenTo: ['authStore', 'registrationStore']

  fetch: ()->
    if @app.authStore.isLoggedIn() and @app.authStore.isActive()
      # redirect to timeline or the invitationId?
      # Direct navigation/mistake is the only way they get here without
      return {
        transitionTo: '/'
      }
    else
      return {
        invitationId: @props.app.registrationStore.getInvitationId()
        isLoggedIn: @.app.authStore.isLoggedIn()
        isActive: @.app.authStore.isActive()
        loginError: @.app.authStore.getError()
        isRegistered: @props.app.registrationStore.isRegistered()
        signupError: @props.app.registrationStore.getError()
        jrnlAffiliate: @props.app.registrationStore.getJRNLAffiliate()
        hasOfferAffiliate: @props.app.registrationStore.getHasOfferAffiliate()
        user: @props.app.registrationStore.getUser()
      }
  done: (results)->
    props = _.extend {}, @props, results
    return <InvitationWelcome {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <InvitationWelcome {...props} />
  failed: (errors)->
    console.log errors
    return <div>InvitationWelcome Error</div>

module.exports = InvitationWelcomeContainer