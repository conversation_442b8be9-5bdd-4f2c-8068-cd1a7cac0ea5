React = require 'react'
Marty = require 'marty'
_ = require 'underscore'

BookStatusIndicator = React.createClass
  displayName: 'BookStatusIndicator'

  render: ()->
    return (
      <div id="status_indicator" className="panel panel-default">
        <div className="panel-body">
          <div className="col-left">
            <img src="/static/images/preloaders/preloader-001.gif" width="40" height="40" />
          </div>
          <div className="col-right">
            Calculating page count and generating pdf preview...
          </div>
        </div>
      </div>
    )

BookStatusIndicatorContainer = Marty.createContainer BookStatusIndicator,
  listenTo: []

  fetch: ()->
    return {}
  done: (results)->
    props = _.extend {}, @props, results
    return <BookStatusIndicator {...@props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <BookStatusIndicator {...@props} />
  failed: (error)->
    console.log error
    return <div>Error</div>

module.exports = BookStatusIndicatorContainer