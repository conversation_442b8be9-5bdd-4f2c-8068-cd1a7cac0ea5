React = require 'react'

OptionsListHeader = React.createClass
  displayName: "Options List Header"

  propTypes:
    title: React.PropTypes.string
    onDone: React.PropTypes.func
  
  onCancel: (e)->
    @props.onHide()

  onDone: (e)->
    @props.onDone()

  render: ()->
    <div className="modal-header teal-bg">
      <div className="row">
        <div className="col-sm-3">
          <a onClick={@onCancel} className="btn-flat pull-left" data-dismiss="modal"><i className="icon icon-times"></i></a>
        </div>
        <div className="col-sm-6 text-center">
          <div className="modal-title">{@props.title}</div>
        </div>
        <div className="col-sm-3">
          <a onClick={@onDone} className="btn-flat pull-right"><i className="icon icon-check-thin"></i></a>
        </div>
      </div>
    </div>

module.exports = OptionsListHeader