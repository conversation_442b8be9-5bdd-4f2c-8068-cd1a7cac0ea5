React = require 'react'
Marty = require 'marty'
_ = require 'underscore'


WallpaperCategoryList = React.createClass
  displayName: 'WallpaperCategory'

  onWallpaperSelected: (wallpaper)->
    if @props.onWallpaperSelected 
      @props.onWallpaperSelected(wallpaper)

  render: ()->
    <div style={display: 'inline-block'}>
      <h2>{@props.category.name}</h2>
      {
        if @props.publicWallpapers?
          _.filter @props.publicWallpapers, (wallpaper)=>
            wallpaper.category == @props.category.resource_uri
          .map (wallpaper)=>
            return <img src={wallpaper.thumbnail} key={wallpaper.id} onClick={@onWallpaperSelected.bind null, wallpaper} />
      }
    </div>

WallpaperGallery = React.createClass
  displayName: 'Wallpaper Gallery'

  getInitialState: ->
    isLoading: false
  
  
  componentWillReceiveProps: (nextProps) ->
    if nextProps.wallpaperPage?
      if nextProps.wallpaperPage.next != null
        if nextProps.publicWallpapers?
          if nextProps.wallpaperPage.total_count == nextProps.publicWallpapers.length
            @setState
              isLoading: false    
          else if nextProps.wallpaperPage.total_count > nextProps.publicWallpapers.length
            if nextProps.wallpaperPage.next != @props.wallpaperPage.next or !@state.isLoading
              @setState
                isLoading: true
              , ()-> @app.wallpaperQueries.getWallpaperPage(nextProps.wallpaperPage.next, 'Wallpapers')
      else
        @setState
          isLoading: false
  
  render: ()->
    <div className="image-library wallpaper">
      {
        if @props.publicWallpaperCategories?
          @.props.publicWallpaperCategories.map (category)=>
            return <WallpaperCategoryList key={category.id} {...@props} category={category} onWallpaperSelected={@props.onWallpaperSelected} />
      }
    </div>

WallpaperGalleryContainer = Marty.createContainer WallpaperGallery,
  listenTo: ['authStore', 'userStore']

  fetch: ()->
    return {
      publicWallpapers: @props.app.userStore.getPublicWallpapers('Wallpapers')
      publicWallpaperCategories: @props.app.userStore.getPublicWallpaperCategories('WallpaperCategories')
      wallpaperPage: @.app.pageStore.getPage('Wallpapers')
      wallpaperCategoryPage: @.app.pageStore.getPage('WallpaperCategories')
    }
  done: (results)->
    props = _.extend {}, @props, results
    return <WallpaperGallery {...props} />
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <WallpaperGallery {...props} />
  failed: (error)->
    console.log error
    return <div>Wallpaper Gallery Error</div>

module.exports = WallpaperGalleryContainer