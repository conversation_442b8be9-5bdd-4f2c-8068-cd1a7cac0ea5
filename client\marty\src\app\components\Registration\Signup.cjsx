React = require 'react'
Marty = require 'marty'
_ = require 'underscore'
FormData = require 'react-form-data'
Link = require('react-router').Link
settings = require '../../settings'

Signup = React.createClass
  displayName: "Signup"
  mixins: [FormData]
  
  componentDidMount: ->
    # console.log "#{@.constructor.displayName} mount"
    @props.app.authActionCreators.logout()
    if @props.isRegistered
      @props.history.pushState null, '/signup/success/'
    else
      @props.app.registrationActionCreators.reset_signup()

  componentWillReceiveProps: (nextProps) ->
    # console.log "#{@.constructor.displayName} will receive props"
    if nextProps.isRegistered
      @props.history.pushState null, '/signup/success/'

  componentWillUnmount: ()->
    # console.log "#{@.constructor.displayName} will unmount"
    if @props.isRegistered
      @props.history.pushState null, '/signup/success/'
    else
      @app.registrationActionCreators.reset_signup()
  
  handleSubmit: (e)->
    e.preventDefault()
    e.stopPropagation()
    username = @formData.username || @refs.usernameInput?.value
    email = @formData.email || @refs.emailInput?.value
    password = @formData.password || @refs.passwordInput?.value
    affiliate = @props.jrnlAffiliate
    hasOfferAffiliate = @props.hasOfferAffiliate
    
    user = 
      username: username
      email: email
      password: password
      affiliate: affiliate
      hasoffer_affiliate: hasOfferAffiliate
    @app.registrationActionCreators.signup(user)

  renderError: ()->
    if !!@props.error?
      if @props.error?
        if @props.error.validation_errors?
          output = []
          for field, error of @props.error.validation_errors
            output.push <div key={"validation-error"} className="alert danger text-center">{error[0]}</div>
          return output
    return undefined

  renderFieldError: (field)->
    if !!@props.error?
      if @props.error?
        if @props.error.validation_errors[field]?
          messages = @props.error.validation_errors[field]
          output = []
          for message, i in messages
            output.push <div key={"message#{i}"} className="alert danger text-center">{message}</div>
          return output
    return undefined    

  removeError: ()->
    @app.registrationActionCreators.reset_registration_error()

  renderSignupForm: ()->
    <div>
      <div id='access'>
        <div className='container'>      
          <form onChange={@updateFormData} autoComplete="off" onSubmit={@handleSubmit} > 
            <input style={display:"none"}/>
            <input type="password" style={display:"none"}/> 
            <div className="panel frame">
              <div className="panel-body">
                <Link to={"/"} className="jrnl-logo"><img src="/static/images/jrnl-logo.png"/></Link>
                <div className="">
                  <div className="" id="signup">
                    <div className="form-wrapper">          
                      <div className="form-group">
                        <input ref="usernameInput" autoComplete="off" name='username' id='username' type='text' className='form-control' placeholder="Username" onClick={@removeError} onChange={@removeError} defaultValue={@props.user?.username}/>
                          {
                            @renderFieldError('username')
                          }
                        <input ref="emailInput" autoComplete="off" name='email' id='email' type='text' className='form-control' placeholder="Email" onClick={@removeError} onChange={@removeError} defaultValue={@props.user?.email}/>
                          {
                            @renderFieldError('email')
                          } 
                        <input ref="passwordInput" autoComplete="off" name='password' id='password' type='password' className='form-control' placeholder="Password" onClick={@removeError}  onChange={@removeError} />
                          {
                            @renderFieldError('password')
                          }
                        <button onClick={@handleSubmit} className="btn btn-navy btn-medium full-width">Signup Here!</button>
                      </div>
                    </div>
                    <div className="access-links">
                      <div className="center">
                        <Link to={"/login/"}>Login to my account</Link>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

  render: ()->
    content = @renderSignupForm()
    <div>
      <div id="content">
        {content}
      </div>
    </div>

Success = React.createClass
  displayName: 'Signup Success'
  
  componentWillUnmount: ->
    # console.log "#{@.constructor.displayName} unmount"
    @props.app.registrationActionCreators.reset_signup()
  
  registerAgain: ()->
    @props.app.registrationActionCreators.reset_signup()
    @props.history.pushState null, '/signup/'

  renderLogoutLink: ()->
    if @props.userAuth?
      <div className="center">
        <Link to={"/logout/"}>Logout and log in to another account</Link>
      </div>
    else 
      output = []
      output.push <a key={'create-account-button'} className="btn btn-navy" onClick={@registerAgain}>Create account</a>
      output.push <Link key={'login-to-account-button'} to={"/login/"} className="btn btn-navy">Login to account</Link>
      return output

  getEmail: ()->
    email = @props.userAuth?.email || @props.user?.email
    if email?
      return "An activation email has been sent to #{email}"
    else
      return "An activation email has been sent."

  render: ()-> 
    <div id="welcome_page">
      <div className="bg-pattern" style={backgroundImage: "url('/static/images/backgrounds/patterns/pattern-001.png')"}></div>
      <div className="welcome-wrapper">
        <div id="welcome_panel" className="panel frame">
          <div className="panel-heading">
            <Link to={"/"} className="jrnl-logo"><img src="/static/images/jrnl-logo-white.png" /></Link>
          </div>
          <div className="panel-body">
            <h2>Thank you for registering!</h2>
            <h3>
            {
              @getEmail()
            }
            </h3>
          </div>
          <div className="panel-footer">
          {
            @renderLogoutLink()
          }
          </div>
        </div>
      </div>
    </div>

SignupContainer = Marty.createContainer Signup,
  listenTo: ['registrationStore']

  fetch: ()->
    return {
      isRegistered: @props.app.registrationStore.isRegistered()
      error: @props.app.registrationStore.getError()
      jrnlAffiliate: @props.app.registrationStore.getJRNLAffiliate()
      hasOfferAffiliate: @props.app.registrationStore.getHasOfferAffiliate()
      user: @props.app.registrationStore.getUser()
      invitationId: @props.app.registrationStore.getInvitationId()
    }
  done: (results)->
    props = _.extend {}, @props, results
    return <Signup {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Signup {...props}/>
  failed: (error)->
    console.log error
    return <div>Error</div>

SuccessContainer = Marty.createContainer Success,
  listenTo: ['registrationStore','authStore']

  fetch: ()->
    return {
      user: @props.app.registrationStore.getUser()
      userAuth: @props.app.authStore.getUser()
      invitationId: @props.app.registrationStore.getInvitationId()
    }
  done: (results)->
    props = _.extend {}, @props, results
    return <Success {...props}/>
  pending: (fetches)->
    props = _.extend {}, @props, fetches
    return <Success {...props}/>
  failed: (error)->
    console.log error
    return <div>Error</div>

module.exports = {
  Signup: SignupContainer
  Success: SuccessContainer
}