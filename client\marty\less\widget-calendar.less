/*---------------------------------------------------------------------------
  >Calendar Widget
---------------------------------------------------------------------------*/

#calendar-widget.widget {
	.radius3;
   overflow: hidden;
   
	.dp-header {
		.btn-gray {
			padding-top: 3px;
			padding-bottom: 3px;
			margin: 2px 0;
		}	
	}
   
	.dp-cell {
    padding: 5px;
    line-height: 30px;
	}
	
	.dp-footer-today {padding: 6px 0;}
}

/*---------------------------------------------------------------------------
  >Queries
---------------------------------------------------------------------------*/

/* SM */ @media screen and (max-width: 991px) {
	
	#calendar-widget.widget {
	
		.dp-body {
			
			.dp-week-day-names {
				
				.dp-cell {
					font-size: 8px;
					line-height: 6px;
				}
			}
			
			.dp-cell {.f10;}
		}
	}
	
}
/* MD */ @media screen and (max-width: 1199px) and (min-width: 992px) {

	#calendar-widget.widget {
	
		.dp-body {
			
			.dp-week-day-names {
				
				.dp-cell {line-height: 10px;}
			}
			
			.dp-cell {.f11;}
		}
	}
	
}
/* LG + */ @media screen and (min-width: 1200px) {
	
	#calendar-widget.widget {
	
		.dp-body {
			
			.dp-week-day-names {
				
				.dp-cell {
					font-size: 11px;
					line-height: 13px;
				}
			}
			
			.dp-cell {line-height: 28px;}
		}
	}
	
}