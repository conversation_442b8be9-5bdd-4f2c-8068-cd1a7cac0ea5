/*---------------------------------------------------------------------------
  >Entries
---------------------------------------------------------------------------*/

.entry {
	margin-bottom: 10px;
  position: relative;
  .radius3;
  
  @media @lg-x {margin-bottom: 15px;}
  
  .main {
	  @media @xs {
		  font-size: 95%;
		  padding: 0 18px;
		}
	  @media @sm {font-size: 95%;}
	  @media @sm-lg {padding: 0 18px;}
	  @media @lg-x {padding: 0 20px;}
  }
  
  &.no-entries {
	  padding: 4em 2em;
	  text-align: center;
	  
	  .title {
		  padding-bottom: 25px;
		  .font2;
		  .f18;
		}
  }
  
  .top-bar {
	  //border-bottom: 1px solid @gray9;
	  display: table;
	  width: 100%;
	  
	  .entry-date {
	    border-radius: 3px 0 3px 0;
      padding: 7px 12px;
		  
		  input[type="checkbox"] {
			  margin-right: 8px;
			  display: none;
		  }
		  
		  &:hover {.teal2-bg;}
		  
		  .entry-date-date {
			  .white-txt;
			  .f14;
			  .w700;
			}

			.entry-date-time {
			  margin-left: 5px;
   			opacity: .7;
			  .w600;
			  .f12;
			  .white-txt;
			}
	  }
	  
	  .date-posted {
      padding: 9px 12px 0;
		  color: @gray5;
		  .f12;
		  .w500;
	  }
	  
	  .dropdown-toggle {
	    display: inline-block;
	    color: @gray8;
		  .f19;
		  
		  @media @xs {padding: 6px 7px 0;}
	    @media @sm-lg {padding: 6px 7px 0;}
      @media @lg-x {padding: 6px 10px 0;}
	  }
  }
	
	.media {
		@media @sm-x {margin: 0 0 15px;}
	}
  
	.avatar {
		.fl;
		width: 36px;
		height: 36px;
	}
	
	iframe {
		width: 100%;
		min-height: 350px;
		display: block;
		.w300;
	}
	
	.entry-title {
		display: block;
		margin: 0;
		.f18;
		.w500;
		.font2;
		
		@media @xs {padding: 0 18px 8px;}
		@media @sm-lg {padding: 0 18px 8px;}
	  @media @lg-x {padding: 0 20px 8px;}
	  
		&:last-child {padding-bottom: 0;}
	}
	
	.meta {
		.f12;
		color: @gray6;
		
		@media @sm-lg {margin: 4px 18px 0;}
		@media @lg-x {margin: 5px 20px 0;}
		
		.divider {
			padding: 0 8px;
			font-size: 5px;
			top: -1px;
			.gray3-txt;
		}
	}
  
  .expand-overlay {
    height: 100px;	
    margin-top: -100px;
    margin-bottom: 10px;
    z-index: 3;
    width: 100%;
    text-align: center;
    background-image: url(images/more-gradient.png);
    
    div {
	    height: 100%;
	    
			@media @sm-lg {margin: 0 18px;}
			@media @lg-x {margin: 0 20px;}
    }	  
    
    .btn {
	    position: absolute;
	    bottom: 0;
	    left: 0;
	    right: 0;
	    width: 100%;
	    margin-left: auto;
	    margin-right: auto;
    }
  }
}