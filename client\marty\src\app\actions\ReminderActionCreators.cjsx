Marty = require 'marty'
reminderConstants = require '../constants/ReminderConstants'

ReminderActionCreators = Marty.createActionCreators
  id: 'ReminderActionCreators'

  updateReminderProfile:(reminderProfile)->
    @.app.reminderHttpAPI.updateReminderProfile(reminderProfile)
    .then (response)->
      if response.ok
        return response
      else
        throw response
    .then (response)->
      return response.json()
    .then (success)=>
      @dispatch reminderConstants.REMINDER_PROFILE_UPDATED, success
      return
    .catch (error)=>
      console.log error
      @dispatch reminderConstants.REMINDER_PROFILE_ERROR, error
      return

module.exports = ReminderActionCreators